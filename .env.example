APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_LEVEL=debug
# info for production
LOG_PATH=./storage/logs
# /var/log/ts4sf/ for production

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=database
SESSION_DRIVER=cookie
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

AMQP_USER=admin
AMQP_PASSWORD=admin
AMQP_HOST=localhost
AMQP_PREVIEW_QUEUE=pdf-editor-preview
AMQP_GENERATE_QUEUE=pdf-editor-job

PHP_CLI_SERVER_WORKERS=5

EDITOR_TEST_EMAIL=<EMAIL>
EDITOR_TEST_PASS=password123
EDITOR_TEST_HOST=http://127.0.0.1:8000