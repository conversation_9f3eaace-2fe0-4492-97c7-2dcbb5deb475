<?php
ini_set('error_reporting', E_ALL);

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\EnsureTokenIsValid;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\EditorController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('/salesforce/checktoken', 'SalesforceController@checktoken');
Route::get('/salesforce/project/list', 'SalesforceController@projectlist');
Route::get('/salesforce/project/{id}/preview', 'SalesforceController@preview');
Route::get('/salesforce/project/{id}/checkdata', 'SalesforceController@checkdata');
Route::get('/salesforce/project/{id}/check/import/{activity_id}', 'SalesforceController@importResult');
Route::get('/salesforce/project/{id}/get/used_vars', 'SalesforceController@getUsedVars');
Route::post('/salesforce/project/{id}/import/csv', 'SalesforceController@importCsv');
Route::post('/salesforce/project/{id}/import/json', 'SalesforceController@importJson');

Route::post('/token', 'UserController@get_token');

Route::put('/response/delivery/{recordId}/status', 'ResponseController@setResponse');

Route::post('/getPrintStatus', 'StatusController@getPrintStatus');

Route::put('/printstatus/set', 'PrintstatusController@setStatus');


Route::middleware([EnsureTokenIsValid::class])->group(function () {
    //
    Route::get('/get/job/{jobId}/fileId/{fileId}/version/{version}', 'EditorController@getGenarationDataAndFileId');
    Route::get('/get/job/{jobId}/fileId/{fileId}/dataset/{dataset}/version/{version}', 'EditorController@getGenarationDataAndFileIdAndDataset');
    Route::get('/get/job/{jobId}/version/{version}', 'EditorController@getGenarationTestData');
    Route::get('/get/job/{jobId}/dataset/{dataset}/version/{version}', 'EditorController@getGenarationTestDataSingleDataset');
    Route::get('/job/image/get/{hash}', [DocumentController::class, 'getImage']);

    Route::put('/store/printpdf/{jobId}/{stepId}/{format}', [EditorController::class, 'storePDF']);
    Route::post('/done/printpdf/{customerFileId}', [EditorController::class, 'donePDF']);


});


