<?php

use App\Http\Controllers\ESPController;
use App\Http\Controllers\FTPController;
use App\Http\Controllers\WeddingController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\DocumentController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use App\Jobs\DeliverPWL;
use App\Models\User;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();
Route::group(['middleware' => 'auth'], function () {
    Route::get('/', function () {
        return redirect('projekte');
    });
    Route::get('/home', function () {
        return redirect('projekte');
    });
    Route::get('/dashboard/all', function () {
        return redirect('dashboard');
    });

    Route::controller(JobController::class)->group(function () {
        Route::get('job/{id}', 'job');
        Route::get('duplicate/{id}', 'duplicateJob');
        Route::get('neuesprojekt', 'newproject');
        Route::get('neuesprojekt/{id}', 'newproject');
        Route::post('neuesprojekt', 'store');
        Route::post('neuesprojekt/{id}', 'store');
        Route::get('togglefav/{id}', 'favoritetoggle');
        Route::get('getprojekt/{id}', 'getproject');
        Route::get('freigabe/previewdocpdf/{id}', 'setpreviewrelease');
        Route::get('previewdocpdf/{id}', 'get_document_preview');
        Route::get('freigabe/{id_job}/doc/previewdocpdf/{id}', 'get_document_preview');
        Route::get('jobdetails/{id}', 'get_job_details');
        Route::get('getdirdokpreview/{id}', 'gert_direct_document_preview');
        Route::get('previewpdf/{id}', 'get_preview');
        Route::post('intervall', 'store_intervall')->name('intervall');
    });

    Route::controller(FreigabeController::class)->group(function () {
        Route::get('freigabe/{id}', 'projectrelease');
        Route::get('freigabe/{id}/doc/{id_doc}', 'docrelease');
        Route::get('freigabe/{id}/editor', 'editorrelease');
        Route::get('autozahlen/{id}', 'statistics');
        Route::post('autozahlen/settransmitdate/{id}', 'storeTransmitdate');
        Route::get('freigabe/{id}/file/{id_doc}', 'get_file_by_id');
        Route::get('freigabe/{id}/file/{id_doc}', 'get_file_by_id');
        Route::get('freigabe/{id}/reg/marketingcloud', 'showMarketingcloud');
        Route::get('freigabe/{id}/reg/{reg}', 'get_job_by_reg');
        Route::get('freigabe/{id}/tag/{tag}', 'get_job_by_tag');
        Route::get('freigabe/{id}/weddingdoc/{id_doc}/jobfile/{jobfile}', 'show_file_by_document');
        Route::get('freigabe/{id}/weddingdoc/{id_doc}', 'show_file_by_id');
        Route::post('docfreigabe', 'set_doc_release');
        Route::post('docablehnung', 'set_doc_reject');
        Route::post('dateifreigabe', 'set_file_release');
        Route::post('jobfreigabe', 'set_job_release');
        Route::post('uebergabestoppen', 'stop_delivery');
        Route::post('jobKfreigabe', 'set_customer_job_release');
    });

    Route::controller(DisplayController::class)->group(function () {
        Route::get('sidebarwidth/{width}', 'setsidebarwidth');
        Route::get('sidebarwidth/{width}/{textshow}', 'setsidebartextwidth');
        Route::get('textshow/{textshow}', 'getpreviewrelease');
    });

    Route::controller(FileController::class)->group(function () {
        Route::get('previewhashpdf/{id}', 'getpreviewarchivepdf');
        Route::get('showresponse/{id}', 'getresponsefile');
        Route::get('showhash/{id}', 'getarchivepdf');
        Route::get('filedata/{id}', 'get_file_from_id');
        Route::get('file/{id}', 'get_file_by_id');
        Route::get('file/{id}/daten/{linenr}', 'get_csv_by_id_and_linenr');
        Route::get('upload/', 'get_pwl_list');
        Route::get('freigeben/{id}', 'set_release');
        Route::get('filedata/{id}/page/{page}', 'get_filedata_by_id_and_page');
        Route::get('druckdateiview/{id}', 'get_base64_printdata');
        Route::get('ablehnen/{id}', 'set_rejection');
        Route::get('sonstigeresponsedownload/{id}', 'download_other_responses');
        Route::get('directdocdownload/{id}', 'directDownload');
        Route::get('responsedownload/{id}', 'download_response');
        Route::get('docdownload/{id}', 'download_document');
        Route::get('weddingautopreview/{id_doc}/file/{id_file}', 'wedding_auto_preview');
        Route::get('weddingautopreview/{id_doc}/file/{id_file}/{showDataSetNr}', 'wedding_auto_preview');
        Route::get('editorpreview/{id_file}/file/{showDataSetNr}', 'editor_auto_preview');
        Route::get('weddingautopreview/{id_doc}/file/{id_file}/{showDataSetNr}/{showVars}', 'wedding_auto_preview');
        Route::get('hashpreview/{id_hash}', 'archive_preview');
        Route::get('druckpreview/{id_doc}', 'print_preview');
        Route::post('file/{id}/daten/{linenr}', 'get_csv_data');
        Route::post('file/{id}', 'show_file_by_id');
        Route::post('file/{id}/page/{page}', 'get_file_by_search');
        Route::post('pwlupload', 'upload_pwl');
        Route::get('csvdateiview/{id}', 'show_csv_file');
        Route::post('csvdateiview/{id}', 'show_csv_file');
    });

    Route::controller(DokumentenController::class)->group(function () {
        Route::get('documentlist/{id}', 'showdoclist');
        Route::get('dokumente/{id}', 'showdocument');
        Route::get('dokumente', 'index');
        Route::get('documentlist', 'index_list');
        Route::get('showsfpreview/{id}', 'get_sf_preview');
        Route::post('dokumente/{id}', 'set_document');
        Route::get('get/image/{id}', 'getImage');
    });

    Route::controller(UserController::class)->group(function () {
        Route::get('kunden/{id}', 'showcustomer');
        Route::get('returnkuser/{kunde}', 'getcustomerusers');
        Route::get('returnkusershort/{kunde}', 'getcustomerusersshort');
        Route::get('stammdaten/returnkuser/{kunde}', 'getcustomerusers');
        Route::get('stammdaten/{id}', 'get_basic_data_by_id');
        Route::get('stammdaten/', 'get_basic_data');
        Route::get('dienstleister/{id}', 'get_service_provider');
        Route::get('returnduser/{dienstleister}', 'return_user_from_dl');
        Route::get('stammdaten/returnduser/{dienstleister}', 'return_user_from_dl');
        Route::get('returndusershort/{dienstleister}', 'return_shortuser_from_dl');
        Route::get('kunden/', 'get_clients');
        Route::get('dienstleister/', 'get_service_providers');
        Route::post('stammdaten/', 'set_basis_data');
        Route::post('stammdaten/{id}', 'set_basic_data_by_id');
        Route::post('kunden/', 'set_customer');
        Route::post('kunden/{id}', 'set_customer_by_id');
        Route::post('dienstleister/', 'set_dl');
        Route::post('dienstleister/{id}', 'set_dl_by_id');
    });

    Route::controller(WeddingController::class)->group(function () {
        Route::get('weddingdocdownload/{id}', 'weddingdocdownload');
        Route::get('weddingpreview/{id}', 'get_wedding_preview');
        Route::get('weddingmaker/{id_doc}', 'get_wedding_by_id');
        Route::get('weddingmakerpreview/{id}/file/{id_file}', 'get_wedding_pdf');
        Route::get('weddingmakerpreview/{id}/file/{id_file}/{showDataSetNr}', 'get_wedding_pdf');
        Route::get('previewmakerdoclookup/{id}', 'get_wedding_pdf_from_testdata');
        Route::get('weddingpreview/{id}/file/{id_file}', 'get_wedding_preview_with_bw');
        Route::get('mcpdf/{id}/file/{id_file}/{showDataSetNr}', 'mcView');
        Route::get('weddingpreview/{id}/file/{id_file}/{showDataSetNr}', 'get_wedding_preview_with_bw');
        Route::get('mcpdf/{id}/file/{id_file}/{showDataSetNr}/showVars/{showVars}', 'mcView');
        Route::get('weddingpreview/{id}/file/{id_file}/{showDataSetNr}/showVars/{showVars}', 'get_wedding_preview_with_bw');
        Route::get('weddingpreview/{id}/file/{id_file}/showVars/{showVars}', 'get_wedding_showVars');
        Route::get('reset_document/{id}', 'set_reset_document');
        Route::post('reset_document', 'set_reset_storno_document');
        Route::post('weddingmaker/{id_doc}', 'get_weddingmaker');
        Route::get('getmakercsv/{id}', 'getMakerCsv');
    });

    Route::controller(EditorController::class)->group(function () {
        Route::get('get/editor/pdf/{id}/{dataset}', 'generatePDF');
        Route::get('get/editor/printpdf/{customerFileId}', 'generatePrintPdf');
        Route::get('get/editor/pdf/{id}/{dataset}/showVars/{showVars}', 'generatePDF');
        Route::get('get/editor/testpdf/download/{id}', 'downloadTestPDF');
        Route::get('get/editor/testpdf/{jobId}', 'generateTestPDF');
        Route::get('get/editor/testpdf/{JobId}/{dataset}', 'generateTestPDF');
        Route::get('get/editor/pdf/{id}/{version}', 'generatePDF');
        Route::get('get/editor/generate', 'generatePrintdoc');
    });

    Route::controller(DashboardController::class)->group(function () {
        Route::get('dashboard', 'index');
        Route::get('archiv', 'archiv');
    });

    Route::controller(ProjektController::class)->group(function () {
        Route::get('projekte', 'show');
        Route::get('projekte/kunde/{id}', 'get_jobs_by_customer');
        Route::get('favoriten', 'favoriten');
    });

    Route::controller(LoginController::class)->group(function () {
        Route::get('logout', 'destroy');
    });

    Route::resource("upload", "FileController");

    Route::controller(WeddingController::class)->group(function () {
        Route::get('test/gen/', 'generatePrintdoc');
        Route::get('test/archiv/', 'generateEinzelHashdoc');
    });

    Route::get('editor', function () {
        $editorRight = User::current()->getRightFromJob($_GET['id'], 'editor')['editor'];
        if ($editorRight == 1) {
            $file = base_path('editor/dist/index.html');
            if (!File::exists($file)) {
                abort(404);
            }
            return response()->file($file);
        }
        else{
            return redirect('projekte');
        }
    });
    Route::get('editor/assets/{path}', function ($path) {
        // FIXME - check $path
        $file = base_path("editor/dist/assets/{$path}");
        if (!File::exists($file)) {
            abort(404);
        }

        $mimeType = File::mimeType($file);
        if (str_ends_with($path, '.js')) {
            $mimeType = 'application/javascript';
        } else if (str_ends_with($path, '.css')) {
            $mimeType = 'text/css';
        }

        return response()->file($file, [
            'Content-Type' => $mimeType,
        ]);
    })->where('path', '.*');


    Route::group(['prefix' => 'api/'], function () {
        //IMAGE STORAGE
        Route::put('/image/store', [DocumentController::class, 'storeImage']);
        Route::get('/image/get/{hash}', [DocumentController::class, 'getImage']);
        Route::get('/thumb/get/{hash}', [DocumentController::class, 'getThumbnail']);
        Route::get('/images/get', [DocumentController::class, 'getImages']);
        Route::get('/images/folder/get', [DocumentController::class, 'getImageFolders']);
        Route::post('/images/folder/create', [DocumentController::class, 'createImageFolder']);
        Route::delete('/images/folder/delete/{folderId}', [DocumentController::class, 'deleteImageFolder']);
        Route::put('/images/folders/update', [DocumentController::class, 'updateImageFolders']);

        //FONT STORAGE
        Route::put('/font/store', 'FontController@store');
        Route::get('/font/get/{hash}', 'FontController@get');
        Route::get('/fonts/get', 'FontController@getFonts');

        Route::get('/colors/get', [DocumentController::class, 'getColors']);

        //HTML STORAGE
        Route::put('/html/store', [DocumentController::class, 'storeHtml']);
        Route::get('/html/get/{hash}', [DocumentController::class, 'getHtml']);

        //JSON STORAGE
        Route::put('/json/store', [DocumentController::class, 'storeJson']);
        Route::get('/json/get/{hash}', [DocumentController::class, 'getJson']);

        //CSS STORAGE
        Route::put('/css/store', [DocumentController::class, 'storeCss']);
        Route::get('/css/get/{hash}', [DocumentController::class, 'getCss']);

        //TEMPLATE STORAGE
        Route::post('/template/store', 'TemplateController@storeTemplate');
        Route::get('/templates/get', 'TemplateController@getTemplates');

        //PAGES STORAGE
        Route::post('/pages/store', 'PageController@storePages');
        Route::get('/pages/get/{id}', 'PageController@getLastPages');
        Route::get('/pages/get/{id}/history', 'PageController@getPagesHistory');
        Route::post('/pages/set/{id}/frontify', 'PageController@setFrontify');
        Route::get('/pages/get/{id}/frontify', 'PageController@getFrontify');
        Route::post('/pages/delete/{id}/frontify', 'PageController@deleteFrontify');
        Route::get('/pages/get/{id}/{version}', 'PageController@getPagesByVersion');
        Route::get('/block/get/{pageFormatId}/job/{jobId}', 'PageController@getBlockedAreas');

        // GET AVAIABLE VARS
        Route::get('/vars/get/{jobId}', 'VarsController@get');

        // GET TEST DATA
        Route::get('/testdata/get/{jobId}', 'TestDataController@get');

        // GET PAGE FORMATS
        Route::get('/pageformats/get', 'PageController@get');
    });


});

Route::controller(UserController::class)->group(function () {
    Route::get('resetpasswd/{token}', 'call_reset_passwd');
    Route::get('initpasswd/{token}', 'call_initial_passwd');
    Route::post('initpasswd/{token}', 'set_new_passwd');
    Route::post('resetpasswd/{token}', 'set_new_passwd');
});

Route::controller(LoginController::class)->group(function () {
    Route::get('forgotpassword/sendlink', 'sendPasswordlink');
});

Route::controller(UserController::class)->group(function () {
    Route::get('session/get/status', 'getSessionStatus');
});

Route::get('forgotpassword', function () {
    return view('forgot');
});

Route::controller(StatusController::class)->group(function () {
    Route::get('status/get/{monitoringSecret}', 'getStatus');
});

Route::controller(ESPController::class)->group(function () {
    Route::get('check/get/token/{monitoringSecret}', 'checkGetToken');
});




