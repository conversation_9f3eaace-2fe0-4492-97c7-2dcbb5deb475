<?php

use \App\Jobs\ProcessPrintdata;
use \App\Jobs\ProcessPrintstatus;
use \App\Jobs\CheckDlFtp;
use \App\Jobs\CheckProcesses;
use \App\Jobs\ProcessSinglePrintDoc;
use \App\Jobs\ProcessMergeDoc;
use \App\Jobs\SetFileSize;
use \App\Jobs\ProcessArchive;
use \App\Jobs\ProcessTransmit;
use App\Jobs\ProcessTransmitResponses;
use \App\Jobs\ProcessDeleteMail;
use \App\Jobs\ProcessDelete;
use \App\Jobs\DeliverPrintData;
use \App\Jobs\GetResponseFromDl;
use \App\Jobs\DeliverPWL;
use \App\Jobs\GetPalDates;
use Illuminate\Support\Facades\Log;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('processPrintData',  function() {
    Log::info('process/processPrintData started.');
    ProcessPrintdata::dispatch()->onQueue('processPrintData');
    Log::info('process/processPrintData done.');
})->describe('Queue: processPrintData');

Artisan::command('GetPalDates',  function() {
    Log::info('process/GetPalDates started.');
    GetPalDates::dispatch()->onQueue('GetPalDates');
    Log::info('process/GetPalDates done.');
})->describe('Queue: GetPalDates');


Artisan::command('checkDlFtp',  function() {
    Log::info('process/checkDlFtp started.');
    CheckDlFtp::dispatch()->onQueue('checkDlFtp');
    Log::info('process/checkDlFtp done.');
})->describe('Queue: checkDlFtp');

Artisan::command('checkProcesses',  function() {
    Log::info('process/checkProcesses started.');
    CheckProcesses::dispatch()->onQueue('checkProcesses');
    Log::info('process/checkProcesses done.');
})->describe('Queue: checkProcesses');

Artisan::command('processSinglePrintDoc',  function() {
    Log::info('process/processSinglePrintDoc started.');
    ProcessSinglePrintDoc::dispatch()->onQueue('processSinglePrintDoc');
    Log::info('process/processSinglePrintDoc done.');
})->describe('Queue: processSinglePrintDoc');

Artisan::command('processMergeDoc',  function() {
    Log::info('process/processMergeDoc started.');
    ProcessMergeDoc::dispatch()->onQueue('processMergeDoc');
    Log::info('process/processMergeDoc done.');
})->describe('Queue: processMergeDoc');

Artisan::command('setFileSize',  function() {
    Log::info('process/setFileSize started.');
    SetFileSize::dispatch()->onQueue('setFileSize');
    Log::info('process/setFileSize done.');
})->describe('Queue: setFileSize');

Artisan::command('processArchive',  function() {
    Log::info('process/processArchive started.');
    ProcessArchive::dispatch()->onQueue('processArchive');
    Log::info('process/processArchive done.');
})->describe('Queue: processArchive');

Artisan::command('processTransmit',  function() {
    Log::info('process/processTransmit started.');
    ProcessTransmit::dispatch()->onQueue('processTransmit');
    Log::info('process/processTransmit done.');
})->describe('Queue: processTransmit');

Artisan::command('processTransmitResponses',  function() {
    Log::info('process/processTransmitResponses started.');
    ProcessTransmitResponses::dispatch()->onQueue('processTransmitResponses');
    Log::info('process/processTransmitResponses done.');
})->describe('Queue: processTransmitResponses');

Artisan::command('processDeleteMail',  function() {
    Log::info('process/processDeleteMail started.');
    ProcessDeleteMail::dispatch()->onQueue('processDeleteMail');
    Log::info('process/processDeleteMail done.');
})->describe('Queue: processDeleteMail');

Artisan::command('processDelete',  function() {
    Log::info('process/processDelete started.');
    ProcessDelete::dispatch()->onQueue('processDelete');
    Log::info('process/processDelete done.');
})->describe('Queue: processDelete');

Artisan::command('deliverPrintData',  function() {
    Log::info('process/deliverPrintData started.');
    DeliverPrintData::dispatch()->onQueue('deliverPrintData');
    Log::info('process/deliverPrintData done.');
})->describe('Queue: deliverPrintData');

Artisan::command('getResponseFromDl',  function() {
    Log::info('process/getResponseFromDl started.');
    GetResponseFromDl::dispatch()->onQueue('getResponseFromDl');
    Log::info('process/getResponseFromDl done.');
})->describe('Queue: getResponseFromDl');

Artisan::command('deliverPWL',  function() {
    Log::info('process/deliverPWL started.');
    DeliverPWL::dispatch()->onQueue('deliverPWL');
    Log::info('process/deliverPWL done.');
})->describe('Queue: deliverPWL');

Artisan::command('processPrintStatus',  function() {
    Log::info('process/processPrintStatus started.');
    ProcessPrintstatus::dispatch()->onQueue('processPrintStatus');
    Log::info('process/processPrintStatus done.');
})->describe('Queue: processPrintStatus');
