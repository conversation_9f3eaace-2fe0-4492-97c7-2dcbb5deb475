# Setting common arguments

services:
  app:
    build:
      dockerfile: ./docker/app/Dockerfile
    volumes:
      - type: bind
        source: ./
        target: /var/www/html/
    entrypoint: ["docker-php-entrypoint"]
    command: ["apache2-foreground"]
    ports:
      - 8080:80
    depends_on:
      - mysql
    environment:
      DB_HOST: mysql
      DB_ROOT_PASSWORD: rootpw
      DB_DATABASE: ts4sf
      DB_USERNAME: ts4sf
      DB_PASSWORD: ts4sfpw
  mysql:
    image: registry.gitlab.com/dialogagentur/docker/mariadb:latest
    environment:
      MYSQL_ROOT_PASSWORD: rootpw
      MYSQL_DATABASE: ts4sf
      MYSQL_USER: ts4sf
      MYSQL_PASSWORD: ts4sfpw
    volumes:
      - 'mysql:/var/lib/mysql'
    healthcheck:
      test: "${DOCKER_HEALTHCHECK_TEST:-mysqladmin -u root -p\"$MYSQL_ROOT_PASSWORD\" ping}"
  #phpmyadmin:
  #  image: registry.gitlab.com/dialogagentur/docker/phpmyadmin
  #  ports:
  #    - '${PHPMYADMIN_PORT:-8081}:80'
  #  environment:
  #    PMA_HOST: 'mysql'
  #  networks:
  #    - ts4sf

  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    ports:
      - "5672:5672"
      - "15672:15672"

  generate-pdf:
    #image: registry.gitlab.com/dialogagentur/applications/ts4sf-pdf-generator:latest
    build:
      context: ../ts4sf-pdf-generator/
    environment:
      - EDITOR_API_KEY=THIS_KEY_IS_ONLY_FOR_DEVELEOPMENT_FIX_ME
      - API_URL=http://app
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=admin
      - RABBITMQ_PASSWORD=admin
    depends_on:
      - rabbitmq
    restart: always

  maildev:
    image: maildev/maildev
    ports:
      - "1080:1080"

volumes:
    mysql:
        driver: local
