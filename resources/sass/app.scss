// Fonts
//@import url('https://fonts.googleapis.com/css?family=Nunito');
@import url('https://fonts.googleapis.com/css?family=Open+Sans');

// Variables
@import 'variables';

// Bootstrap
@import '~bootstrap/scss/bootstrap';

body {
    color: #888888 !important;
    padding: 0;
    margin: 0;
    background-color: #eeeeee !important;
    font-family: 'Rubik', sans-serif;
    height: 100%;
    font-size: 12px;
    color: #444444;
    letter-spacing: 1px;

}

.underline {
    border-bottom: 5px solid $tsblue;
    border-radius: 0;
    padding-bottom: 9px;

}

.popover {
    max-width: 100%; /* Max Width of the popover (depending on the container!) */
    margin: auto auto;
}

html {
    height: auto;
}

div, li {
}

button {
}

label {
    opacity: 1;
    font-family: 'Rubik', sans-serif;
    font-weight: bold;
    font-size: 12px;
    color: #444444;
    letter-spacing: 0.5px;
    text-align: center;
}

button:hover {
}

button .btn_big {
    width: 210px;
    height: 40px;
    background: #2F4B70;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

button .btn_big {
    width: 210px;
    height: 40px;
    background: #2F4B70;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    color: #FFFFFF;
    letter-spacing: 0.5px;
    text-align: center;
    line-height: 22px;
}

h1 {
    font-family: 'Rubik', sans-serif;
    font-weight: normal;
    font-size: 30px;
    color: #444444;
    letter-spacing: 1px;
    line-height: 44px;
}

h2 {
    font-family: 'Rubik', sans-serif;
    font-weight: bold;
    font-size: 26px;
    color: #444444;
    letter-spacing: 1px;
    line-height: 36px;
}

h3 {
    font-family: 'Rubik', sans-serif;
    font-weight: normal;
    color: $tsblue;
    font-size: 20px;
    line-height: 24px;
    text-align: left;
}

h4 {
    font-family: 'Rubik', sans-serif;
    font-weight: bold;
    font-size: 18px;
    color: #444444;
    letter-spacing: 1px;
    line-height: 30px;
}

h5 {
    font-family: 'Rubik', sans-serif;
    font-weight: bold;
    font-size: 16px;
    color: $tsblue;
    letter-spacing: 1px;
    line-height: 30px;
}


li {
    list-style: none;

}

.kunden .projekte {
    text-align: center;
}

.kunden .projekte a {
    float: right;
}

.leftarrow {
    margin: auto auto;
}

.rotate {
    transform: rotate(-180deg);
    /*transform: rotate(180deg);*/
    transition: .3s;
}

.rotate-reset {
    transform: rotate(0deg);
    transition: .3s;
}


.ueberschrift img {
    height: 10px;
}

.dashboardslide {
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    padding-top: 5px;
}

.sidemenue {
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    z-index: 1000;
}

a {
    color: #98A9BC;
    text-decoration: none;
}

.menuelogo img {
    fill: #FFF;
    width: 120px;
}

input {


}

.whitebox #button {
    width: 100%;
    border-radius: 2px;
    color: #FFFFFF;
    border: none;
    height: 30px;
}

.font-normal {
    font-weight: normal !important;
}

/* LOGIN VIEW */
.splitview {
    position: absolute;
    right: 0px;
    background-color: rgba(255, 255, 255, 0.7);
    height: 100%;
    width: 50%;
}

.whitebox {
    background-color: #FFF;
    width: 100%;
    border-radius: 2px;
    padding: 90px 90px 40px 90px;
    z-index: 100;
}

.whitebox_table {
    background-color: #FFF;
    width: 100%;
    border-radius: 2px;
    padding: 90px 90px 40px 90px;
    z-index: 100;
    overflow-x: scroll;
}


.centerloginbox {
    width: 555px;
    margin-top: 0px;
}

.buttomnav {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 0.8vw;
    font-weight: bold;
    color: $tsblue;
}

.welcome {
    margin: 10px 0px 10px 0px;
}

.abbinder {
    margin-top: 20px;
    font-size: 10px;
    margin-left: -20px;
    margin-right: -20px;
    padding: 20px;
    border-radius: 0;

}

.error {
    background-color: rgba(255, 0, 0, 0.1);
    padding: 5px;
    margin-bottom: 5px;
    width: 100%;
    border-radius: 2px;
    color: #444444;
    padding-left: 10px;
}

.succsess {
    background-color: rgba(0, 255, 0, 0.1);
    padding: 5px;
    margin-bottom: 5px;
    width: 100%;
    border-radius: 2px;
    color: #444444;
}

.tgd_logo {
    width: 50%;
    margin: auto auto;
}

.transfersafe_logo {
    width: 75%;
    left: 50%;
    margin: auto auto;
}

.subtitel {
    margin: 10px;
    width: 100%;
    text-align: center;
}

.centerbox {
    min-width: 200px;
    width: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* MENUE */
.sidemenue {
    background-color: $tsblue;
    color: #fff;
    min-height: 100%;
    white-space: nowrap;
}

.sidemenue .projekterstellenicon::before {
    background-color: white;
    -webkit-mask-image: url("../images/icons/hinzufuegen.svg");
    mask-image: url("../images/icons/hinzufuegen.svg");
    position: absolute;
    background-size: 15px 15px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .dashboardicon::before {
    background-color: white;
    -webkit-mask-image: url("../images/icons/dashboard.svg");
    mask-image: url("../images/icons/dashboard.svg");
    position: absolute;
    background-size: 15px 15px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .projekticon::before {
    background-color: white;
    -webkit-mask-image: url("../images/icons/ordner.svg");
    mask-image: url("../images/icons/ordner.svg");
    position: absolute;
    background-size: 13px 15px;
    display: inline-block;
    width: 19px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .dokumenteicon::before {
    background-color: white;
    -webkit-mask-image: url("../images/icons/dokument.svg");
    mask-image: url("../images/icons/dokument.svg");
    position: absolute;
    background-size: 13px 10px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .favoritenicon::before {
    background-color: white;
    mask-size: 10px 10px;
    -webkit-mask-image: url("../images/icons/favoriten.svg");
    mask-image: url("../images/icons/favoriten.svg");
    position: absolute;
    background-size: 13px 10px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .benutzericon::before {
    background-color: white;
    mask-size: 10px 10px;
    -webkit-mask-image: url("../images/icons/benutzergruppe.svg");
    mask-image: url("../images/icons/benutzergruppe.svg");
    position: absolute;
    background-size: 13px 10px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .jobarchivicon::before {
    background-color: white;
    mask-size: 10px 10px;
    -webkit-mask-image: url("../images/icons/archiv.svg");
    mask-image: url("../images/icons/archiv.svg");
    position: absolute;
    background-size: 13px 10px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue .einstellungenicon::before {
    background-color: white;
    mask-size: 10px 10px;
    -webkit-mask-image: url("../images/icons/einstellungen.svg");
    mask-image: url("../images/icons/einstellungen.svg");
    position: absolute;
    background-size: 13px 10px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.sidemenue a {
    text-decoration: none;
    color: #fff;
}

.menutitel {
    text-transform: uppercase;
    margin-top: 30px;
    font-size: 12px;
}

.menusub {
    margin-left: 0px;
    margin-top: 5px;
    font-size: 12px;
}

.menusub i{
    font-size: 20px;
}

.menusub svg g {
    fill: #FFFFFF !important;
}

.sym_spacer svg g {
    fill-opacity: 1;
}

.sym_spacer svg g {
    fill: #ffffff !important;
}

#sym_spacer{
    margin-top: 10px;
    display: none;
}

.logout svg {
    width: 30px !important;
    height: 30px !important;
    fill: #FFFFFF !important;
    opacity: 1;
}

.menusub.download svg {
    transform: rotate(180deg);
}

.logout {
    position: absolute;
    bottom: 50px;
    margin-left: -10px;
}

.dashboardslide {
    text-align: center;
    position: absolute;
    bottom: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);

    margin-left: -10px;
}


/* Master */
.content {

    height: 100%;
    bottom: 0px;
    background-color: #eeeeee;


}

/* Dahsboard */
.main-left {
    width: calc(100% - 290px);
    line-height: 25px;
}

.main-right {
    width: 290px;
    height: 100%;
    float: right;
}

.main-right .headline {
    background-color: #FFF;
    padding: 10px;
    margin-left: -10px;
    margin-bottom: 10px;
}

.main-left h2, .main-right h2 {
    padding: 0;
    margin: 0;
}

.main-left .headline {
    background-color: #FFF;
    padding: 10px;
    margin-left: 0px;
    margin-bottom: 10px;
}

.main-small-left {
    float: left;
    width: 23%;
}

.main-big-right {
    right: 0px;
    width: 75%;
    height: 100%;
    float: right;
}

.begruessung {
    background-color: #eeeeee;
    min-height: 160px;
    font-size: 16px;
    color: #444444;
    letter-spacing: 1px;
    line-height: 22px;
}


.projektaktivitaet {
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.projektaktivitaet .whitebackground {
    background-color: #fff !important;
    height: 100%;
}

.brand_logo {
    width: 100%;
    text-align: right;
}

.brand_logo img {
    width: 120px;
}

.anrede {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #555555;
}

/* Projektakktivitaeten */
.projektaktivitaet {
    padding: 5px;
    margin-top: 5px;
}

.projektaktivitaet ul {
    padding: 0;
    border: none;
}

.projektaktivitaet ul {
    margin: 0;
    list-style: none;
}

.projektaktivitaet .klappen {
    width: 20px;
    height: 20px;
    float: right;
    text-align: center;
    vertical-align: middle;
}

.projektaktivitaet .kundensektion {
    padding: 5px;
    margin: -5px;
    border-radius: 0px;
    min-height: 25px;
}

.projektaktivitaet .kundensektion .kunde {
    font-size: 14px;
}

.projektaktivitaet .kundensektion:nth-child(even) {
    background-color: #eeeeee;
}

.projektaktivitaet .kundensektion:nth-child(odd) {
    background-color: #fff;
}

.projektaktivitaet ul li:nth-child(even) .job {
    background-color: #FFF;
    margin: 10px;
    padding: 5px;
}

.projektaktivitaet ul li:nth-child(odd) .job {
    background-color: #eeeeee;
    margin: 10px;
    padding: 5px;
}

.projektaktivitaet .jobtitel {
    font-weight: bold;
    float: left;
}

.projektaktivitaet .jobsubtitel {
    font-weight: normal;
    font-size: 10px;
}

.projektaktivitaet .jobtitel a {
    text-decoration: none;
    color: #888888;
}

.projektaktivitaet .jobtermine {
    float: right;
}

.projektaktivitaet .jobtermine div {
    text-align: right;
}

.projektaktivitaet .startdatum::before {
    /*background-image: url('../images/einstellungen.svg');*/
    position: absolute;
    background-size: 15px 15px;
    display: inline-block;
    width: 15px;
    height: 15px;
    content: "";
    margin-left: -20px;
}

.projektaktivitaet .loeschdatum {
    border-top: 1px solid #888888;
    padding-top: 5px;
    padding-bottom: 10px;
    margin-top: 5px;
    border-radius: 0px;
}

.projektaktivitaet .loeschdatum svg {
    height: 12px;
}

/* offene Aufgaben */
.offene_aufgaben {
    padding: 0;
}

.offene_aufgaben ul {
    padding: 0;
}

/*
.offene_aufgaben ul li{
  list-style: none;
  clear:both;
  height: 18px;
  padding: 5px;
  border: 1px solid $color-boxborder;
  margin: 0;
  margin-top: -1px;



}

*/
.hidden {
    display: none;
}


.projektaktivitaet .kunde {
    float: left;
    width: 200px;
}

.projektaktivitaet .count {
    float: right;
    width: 10px;
}

.offene_aufgaben {
    padding: 0;
    margin: 0;
}

.offene_aufgaben a {
    text-decoration: none;
    color: #888888;
}

.offene_aufgaben table {
    background-color: #ffffff;

}

.offene_aufgaben table td:nth-child(4) {
    width: 120px;

}

.projektaktivitaet ul li:nth-child(even) {
    background-color: #eeeeee;
}

/*
.offene_aufgaben .tablesorter-header-inner {
    color: #555555; }



.offene_aufgaben table {
    width: 100% !important; }

.offene_aufgaben tr td {
    padding: 5px;
}



.offene_aufgaben thead th {
    text-align: left; }

.offene_aufgaben .favorit div {
    text-align: center !important;
    width: 100%;
    margin-left: 7px; }

.tablesorter-headerASC {
    border-bottom: none; }

.offene_aufgaben .favorit {
    text-align: center !important; }

.offene_aufgaben thead tr {
    border-bottom: 1px solid #eeeeee; }

.offene_aufgaben .aufgabentitel {
    font-weight: bold;
    width: 40%;
    float: left; }

.offene_aufgaben .aufgabendatum {
    width: auto;
    float: right;
    margin-left: 10px;
    margin-rght: 10px; }

.offene_aufgaben .aufgabendatei {
    width: auto;
    float: right; }

.offene_aufgaben .attachment {
    width: auto;
    margin-right: 10px;
    margin-top: 2px; }

.offene_aufgaben .favorit svg g {
 }

.offene_aufgaben .jobtitel {
    width: auto;
    background: #eeeeee;
    padding: 5px;
    border-radius: 10px;
    width: 20%; }

.offene_aufgaben .jobnummer {
    width: auto;
    background: #eeeeee;
    padding: 5px;
    border-radius: 10px;
    width: 20%; }

.offene_aufgaben a {
    text-decoration: none;
    color: #888888; }

.offene_aufgaben .jobbezeichnung {
    width: auto;
    background: #eeeeee;
    padding: 2px;
    border-radius: 10px;
    width: 30%;
    margin-left: 20px; }

.offene_aufgaben .jobbezeichnung a {
    text-decoration: none;
    color: #888888; }


.offene_aufgaben .topline {
    border-top: 1px solid #eeeeee;
}

.offene_aufgaben, .kunden, .jobs {
    background-color: #fff;
}

*/

/* Kunden */
.kunden {

    padding: 0;

}

.kunden ul {
    padding: 0;
}

.kunden ul li {
    list-style: none;
    clear: both;
    min-height: 23px;
    padding: 5px;
    border: 1px solid #e0e0e0;
    margin: 0;
    margin-top: -1px;
    overflow: hidden;
}

.kunden .kunde {
    font-weight: bold;
    margin-top: 20px;
}

.kunde a {
    color: #555555;
}

.kunden .anzahl_jobs {
    font-weight: bold;
    width: auto;
    min-width: 20px;
    float: right;
    right: 20px;
    text-align: center;
    padding: 2px;
    background-color: #eeeeee;
    border-radius: 10px;
}

/* JOBS */
.jobs {
    padding: 0;
}

.jobs ul {
    padding: 0;
}

.jobs ul li {
    list-style: none;
    clear: both;
    min-height: 32px;
    padding: 5px;
    border: 1px solid #e0e0e0;
    margin: 0;
    margin-top: -1px;
}

.jobs .jobnummer {
    font-weight: bold;
    width: 40%;
    float: left;
}

.jobs .jobnummer a {
    text-decoration: none;
    color: #555555;
}

.jobs .aufgabendatum {
    width: auto;
    float: right;
    margin-left: 10px;
    margin-rght: 10px;
}

.jobs .aufgabendatei {
    width: auto;
    float: right;
}

.jobs .attachment {
    width: auto;
    float: right;
    margin-right: 10px;
    margin-top: 2px;
}

.jobs .favorit {
    width: auto;
    float: right;
    margin-left: 5px;
    margin-top: -2px;
}

.jobs .favorit svg g {
    fill: #f1be13 !important;
}

.jobs .jobtaetigkeit {
    width: auto;
    float: right;
    background: #eeeeee;
    padding: 3px;
    font-size: 10px;
    border-radius: 10px;
}

.projektkunde {
    background-color: #fff;
    margin: 10px;
    padding: 5px;
    min-height: 20px;
}

.projektkunde .kunde {
    float: left;
    width: 40px;
    font-size: 20px;
    font-weight: bold;
}

.projektkunde .einstellungen {
    float: right;
    width: 40px;
}

.projektkunde .suche {
    background-color: #eeeeee;
    padding: 0px;
    clear: both;
    margin: -5px;
}

.projektkunde .suche input {
    background-color: #fff;
    margin: 10px 10px 0 10px;
    margin-left: 0px;
}

.jobansicht .jobnummer {
    background-color: #FFFFFF;
    font-size: 16px;
    padding: 10px;
    vertical-align: middle;
    color: #555555;
}

.jobansicht .benutzer {
    vertical-align: middle;
    color: #555555;
}

.jobansicht .benutzer svg {
    vertical-align: middle;
    margin-right: 10px;
}

.jobansicht .projektuebersicht {
    background-color: #FFF;
    margin-top: 10px;
}

.jobansicht .trenner {
    border: 1px solid #bcbcbc;
}

.jobansicht .projektuebersicht .projektnametitel {
    width: 30%;
    float: left;
    color: #555555;
    padding: 10px;
}

.jobansicht .projektuebersicht .bestandteiletitel {
    width: 30%;
    float: left;
    color: #555555;
    padding: 10px;
    padding-left: 15px;
}

.jobansicht .projektuebersicht .aktivitaetsansichttitel {
    width: 30%;
    float: right;
    color: #555555;
    padding: 10px;
}

.jobansicht .projektuebersicht .projektname {
    width: 30%;
    float: left;
    padding: 10px;
    background-color: #FFF;
    min-height: 100px;
    color: #555555;
    font-size: 18px;
    font-weight: bold;
    border: 1px solid #bcbcbc;
    margin-right: 5px;
}

.jobansicht .whitebackground {
    background-color: #FFF;
    clear: both;
}

.jobansicht .projektuebersicht .bestandteile {
    width: calc(100% - 60% - 86px);
    float: left;
    padding: 10px;
    background-color: #FFF;
    min-height: 100px;
    border: 1px solid #bcbcbc;
    margin-right: 5px;
    margin-left: 5px;
}

.jobansicht .projektuebersicht .bestandteile ul {
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 16px;
}

.jobansicht .projektuebersicht .bestandteile ul li {
    border-bottom: 1px solid #bcbcbc;
    border-radius: 0px;
    padding: 5px;
}

.jobansicht .projektuebersicht .aktivitaetsansicht {
    width: 30%;
    float: right;
    padding: 10px;
    background-color: #FFF;
    min-height: 100px;
    border: 1px solid #bcbcbc;
    margin-left: 5px;
    font-size: 16px;
}

.jobansicht .jobnummer button {
    float: right;
    margin-right: 20px;
}

.aktivitaetsansicht a {
    color: #bcbcbc !important;
}

.jobansicht .jobtitel {
    background-color: #FFFFFF;
    margin-top: 16px;
    font-size: 24px;
    padding: 5px;
}

.jobansicht .von_bis {
    margin-top: 20px;
    width: 75%;
    float: left;
}

.jobansicht .geloescht_in {
    margin-top: 20px;
    width: calc(25% - 40px);
    float: right;
    margin-left: 40px;
}

.jobansicht progress {
    border-radius: 4px !important;
    background: #bcbcbc;
    height: 6px;
}

progress::-webkit-progress-bar {
    background: transparent;
}

.jobansicht .geloescht_in progress::-webkit-progress-value {
    border-radius: 4px;
    background: #edc641;
    box-shadow: inset 0 -2px 4px rgba(255, 0, 0, 0.4), 0 2px 5px 0px rgba(0, 0, 0, 0.3);
}

.jobansicht .von_bis progress::-webkit-progress-value {
    border-radius: 4px;
    background: #61c58e;
    box-shadow: inset 0 -2px 4px rgba(255, 0, 0, 0.4), 0 2px 5px 0px rgba(0, 0, 0, 0.3);
}


.jobansicht .von_bis progress::-moz-progress-bar {
    background-color: #61c58e;
}

.yellowpg {
    background: #edc641 !important;
    background: #bcbcbc !important;
    width: 100%;

}

.jobansicht .progressbar {
    width: 100%;
}

.jobansicht .teilbereich {
    width: 100%;
    padding: 20px;
}

.jobansicht .aktivitaetsansicht {
    width: 100%;
    background-color: #FFF;
    min-height: 100px;
}

.jobansicht .benutzer {
    padding: 20px;
    background-color: #eeeeee;
}

.jobansicht .benutzeruebersicht {
    background-color: #FFF;
    min-height: 100px;
    display: flex;
    padding: 5px;
}

.jobansicht .benutzeruebersicht .kunden {
    width: 33%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    float: left;
    margin: 10px;
}

.jobansicht .benutzeruebersicht .benutzerblock {
    background-color: #FFF;
    padding: 10px;
    margin-botom: 5px;
    border-bottom: 1px solid #eeeeee;
}

.jobansicht .benutzeruebersicht .benutzerblock a {
    text-decoration: none;
}

.jobansicht .benutzeruebersicht ul {
    list-style: none;
    padding: 0px;
}

.jobansicht .benutzeruebersicht ul li {
    min-height: 20px;
}

#pass_ausgabe {
    border: 2px solid #0062a6;
    font-weight: bold;
    background-color: white;
    border-radius: .35em;
    padding: .5em 1em;
    margin-top: .5em;
    position: absolute;
    width: 200px;
    margin-top: -200px;
}

#pass_ausgabe ul {
    list-style: none;
    margin: 0;
    padding: 0;
    margin-top: 10px;
}

#pass_ausgabe li {
    padding: 3px;
    color: lightgrey;
    margin: 1px;
}

.jobansicht .benutzeruebersicht .benutzername {
    width: calc(100% - 60px);
    float: left;
    line-height: 28px;
    margin-left: 10px;
}

.jobansicht .benutzeruebersicht .shortname {
    color: $tsblue;
    width: 15px;
    height: 13px;
    padding: 6px;
    padding-top: 9px;
    float: right;
    border: 1px solid #eeeeee;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
    font-size: 7px;
    text-align: center;
    font-weight: bold;
    list-style: none;
    margin-top: -5px;
}

.jobansicht .benutzer .shortname {
    color: $tsblue;
    width: 15px;
    height: 13px;
    padding: 6px;
    padding-top: 9px;
    float: right;
    border: 1px solid #eeeeee;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
    font-size: 7px;
    text-align: center;
    font-weight: bold;
    list-style: none;
}

.jobansicht .benutzeruebersicht .agentur {
    width: 33%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    margin: 10px;
    float: left;
}

.jobansicht .benutzeruebersicht .dienstleister {
    width: 33%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    margin: 10px;
    float: left;
}

.jobansicht .benutzeruebersicht .dienstleister0, .jobansicht .benutzeruebersicht .kunde0 {
    display: none;
}

.jobansicht .benutzeruebersicht .kunden2 {
    width: 50%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    float: left;
    margin: 10px;
}

.jobansicht .benutzeruebersicht .agentur2 {
    width: 50%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    margin: 10px;
    float: left;
}

.jobansicht .benutzeruebersicht .dienstleister2 {
    width: 50%;
    background-color: #eeeeee;
    padding: 10px;
    color: #555555;
    margin: 10px;
    float: left;
}

.clear {
    clear: both;
}

.jobansicht .von_bis .von {
    float: left;
    width: 25%;
}

.jobansicht .einstellungen {
    float: right;
    margin: 3px;
}

.jobansicht .von_bis .bis {
    width: 25%;
    float: right;
    text-align: right;
}

.zeiten {
    font-size: 24px;
}

.filedetails {
    position: relative;
}

.filedetails li {
    clear: both;
    list-style: none;
}

.filedetails .ablehnen a {
    text-decoration: none;
    color: white;
}

.filedetails .freigabe a {
    text-decoration: none;
    color: white;
    width: 250px !important;
}

.filedetails .download a {
    text-decoration: none;
    color: white;
}

.fileblock .download a {
    text-decoration: none;
    color: white;
}

.alist .Download {
    background-color: #8ec2eb !important;
}

.alist .Upload {
    background-color: #edc84c !important;
}

.alist .Abgelehnt {
    background-color: #e96a6a !important;
}

.alist .Freigabe {
    background-color: #67c892 !important;
}

.filedetails li div {
    width: auto;
    min-width: 100px;
    float: left;
    border: 1px solid red;
    margin-right: 5px;
}

.filedetails table {
    min-width: 100%;
    background-color: #FFF;
}

.filedetails table th {
    text-align: left;
}

.filedetails table tr:active {
    background-color: lightgray;
}

.filedetails table tr:hover {
    background-color: lightgray;
}

.filedetails .suchform input {
    width: 150px;
    background-color: #FFF;
    margin-right: 20px;
}

.filedetails .suchform select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    height: 30px;
    min-width: 150px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.filedetails .suchform button {
}

.filedetails .pages {
    text-align: left;
    float: left;
}

.filedetails .pages button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid #eeeeee;
    height: 30px;
    min-width: 18px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #555555;
}

.filedetails .suchform {
    margin-top: 15px;
}

.filedetails table td {
    text-align: left;
    border-top: 1px solid #eeeeee;
    padding: 5px;
    color: #555555;
}

.filedetails table th a {
    color: #bcbcbc;
    text-decoration: none;
}

.filedetails .filealias {
    font-size: 20px;
    color: #555555;
    font-weight: bold;
    padding: 5px;
}

.filedetails .fileorg {
    color: #555555;
    font-weight: bold;
    padding-left: 5px;
}

.filedetails .pfadnavi {
    background-color: #FFF;
    padding: 10px;
    min-height: 30px;
    font-weight: bold;
}

.filedetails .freigegeben {
    background-color: rgba(103, 200, 146, 0.3) !important;
    color: #bcbcbc;
    cursor: none !important;
}

.filedetails .ablehnen svg, .filedetails .freigabe svg {
    vertical-align: middle;
    margin-right: 5px;
}

.filedetails .ablehnen svg, .filedetails .finalefreigabe svg {
    vertical-align: middle;
    margin-right: 5px;
}

.filedetails .ablehnen svg g, .filedetails .freigabe svg g {
    fill: #FFF !important;
    fill-opacity: 1;
}

.filedetails .ablehnen svg g, .filedetails .finalefreigabe svg g {
    fill: #FFF !important;
    fill-opacity: 1;
}

.filedetails .geprueft {
    background-color: rgba(237, 200, 76, 0.3) !important;
    color: #bcbcbc;
    cursor: none !important;
}

.filedetails .geprueft svg g {
    fill: #bcbcbc !important;
    fill-opacity: 1;
}

.filedetails .finalfreigegeben {
    background-color: rgba(93, 191, 226, 0.3) !important;
    color: #bcbcbc;
    cursor: none !important;
}

.filedetails .finalfreigegeben svg g {
    fill: #bcbcbc !important;
    fill-opacity: 1;
}

.filedetails .abgelent {
    background-color: rgba(233, 106, 106, 0.3) !important;
    color: #bcbcbc;
    cursor: none !important;
}

.filedetails .abgelent svg g {
    fill: #bcbcbc !important;
    fill-opacity: 1;
}

.filedetails button {
    border: none;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    color: #FFF;
    min-width: 120px;
    padding: 5px;
}

.filedetails .freigabe {
    background-color: #67c892;
}

.filedetails .finalefreigabe {
    background-color: #5DBFE2;
}

.filedetails .ablehnen {
    background-color: #e96a6a;
}

.filedetails .download {
    background-color: #8ec2eb;
}

.fileblock .download {
    background-color: #8ec2eb;
}

.filedetails .pfadnavi img {
    float: right;
    width: 20px;
    margin: 3px;
}

.fileblock button {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 0px solid transparent;
}

.filedetails button {

    margin-bottom: 5px;
}


.filedetails .freigabe {
}

.filedetails .finalefreigabe {
}

.filedetails .ablehnen {
}

.filedetails .download {
}

.filedetails .datensaetze {
    float: right;
    margin-top: 10px;
}

.filedetails .filedata {
    width: 100%;
    float: left;
}

.filedetails, .weddingdetails {
    height: 100%;
}

.filedetails .pdfview {
    position: relative;
    width: 100%;
    height: 100%;
}

#content {
    position: relative;
    width: 100%;
    /*border: 1px solid green;*/
    top: 0px;
}

.splitt_left {
    margin-top: 20px;
    width: 20%;
    bottom: 0px;
    padding: 10px;
    float: left;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    background-color: #FFFFFF;
}

.splitt_right {
    margin-top: 20px;
    height: 100%;
    width: calc(80% - 60px);
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    margin-left: 20px;
    background-color: #FFFFFF;
    padding: 10px;
    float: right;
}

.pdfview {
    border: 1px solid #eeeeee;
    margin-top: 10px;
}

.filedetails .pdfview object {
    min-height: 600px;
    height: 100%;
    top: 0px;
    width: 100%;
    border: 1px solid black;
}

.filedetails .pdfview .fileselect {
    position: relative;
    height: 30px;
}

.dateien ul {
    padding: 0;
}

.dateien .headline {
    padding: 10px;
    color: #555555;
}

.dateien .icon {
    margin: 5px;
    margin-top: 0px;
    float: left;
    color: #555555;
}

.dateien .fileblock {
    list-style: none;
    background-color: #FFF;
    height: 20px;
    padding: 10px;
    margin-bottom: 1px;
}

.dateien .fileblock a {
    text-decoration: none;
    color: #555555;
}

.jobdetails .jobnummer {
    background-color: #FFFFFF;
    font-size: 16px;
    font-weight: bold;
    padding: 10px;
}

.jobdetails .jobnummer button {
    width: 260px;
    margin-right: 20px;

}

.jobdetails .jobnummer button:hover {
    color: #2F4B70;
    background-color: white;
    border: 1px solid #2F4B70;

}

.jobdetails .jobnummer button:hover > svg g {
    fill: #2F4B70 !important;
    fill-opacity: 1;


}

.xjobdetails .upload {
    float: right;
    border: none;
}

.jobdetails .jobnummer button svg {
    height: 15px;
    margin-right: 10px;
    margin-top: 0px;
    vertical-align: middle;
    margin-right: 10px;
}

.jobdetails .jobnummer button svg g {
    fill: #ffffff !important;
    fill-opacity: 1;
}

.xjobdetails .jobnummer button svg g:hover {
    fill: #2F4B70 !important;
    fill-opacity: 1;
}

.jobdetails .error {
    margin-top: 20px;
    color: rgba(255, 0, 0, 0.7);
}

.jobdetails .alist svg {
    vertical-align: middle;
    height: 17px;
}

.jobdetails .alist svg g {
    fill: #ffffff !important;
    fill-opacity: 1;
    height: 20px;
}

.jobdetails .taetigkeit {
    width: 50%;
    float: left;
}

.jobdetails .filematching {
    margin-top: 10px;
    min-height: 100px;
    height: auto;
    position: relative;
    clear: both;
}

.jobdetails .spaltenueberschrift {
    font-size: 24px;
    font-weight: bold;
    color: #555555;
    padding-top: 20px;
    margin-bottom: 20px;
}

.jobdetails .dateiuebersicht {
    margin-bottom: 10px;
}

.jobdetails .dateiuebersicht .ueberschrift {
    font-size: 18px;
    color: #555555;
    padding: 10px;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 5px;
}

.jobdetails .fileinput input {
    width: 90%;
}

.jobdetails .dateiuebersicht {
    background-color: #FFF;
    min-height: 80px;
    height: 100%;
}

.jobdetails .dateiuebersicht li {
    list-style: none;
}

.jobdetails .filematching .dateiname {
    background-color: #eeeeee;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    padding: 8px;
}

.jobdetails .filematching .klappen {
    width: 20px;
    float: right;
    background-color: #ffffff;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    text-align: center;
}

.jobdetails .filematching a {
    color: #555555;
}

.jobdetails .filematching .klappen svg g {
    fill: #555555 !important;
}

.jobdetails .filematching .aktivitaeten {
    padding: 0;
    margin: 0;
}

.jobdetails .filematching .aktivitaeten .alist {
    padding: 0;
    margin: 0;
}

.jobdetails .einstellungen {
    float: right;
}

.jobdetails .filematching .aktivitaeten .datum {
    float: left;
    margin-right: 10px;
    width: 25%;
    min-width: 90px;
}

.jobdetails .filematching .aktivitaeten .user {
    float: right;
    width: 25%;
}

.jobdetails .filematching .aktivitaeten .taetigkeit {
    width: 40%;
    flaot: left;
    margin: 0px;
}

.jobdetails .dateiupload {
    padding-left: 10px;
    padding-right: 10px;
}

.jobdetails .dateiupload .fileinput {
}

.jobdetails .dateiupload button {
}

.jobdetails .dateiname {
    color: #555555;
}

.split_half {
    width: calc(50% - 20px);
    float: left;
    min-width: 300px;
    margin-right: 20px;
}

.stammdaten {
    font-size: 14px;
}


ul#kategorielist li {
    list-style: none;
}

.stammdaten .kunde {
    margin-top: 20px;
}

.transbutton {
    background-color: transparent !important;
    max-width: 30px !important;
    min-width: 0px !important;
    border: none;
    padding-top: 10px;
    padding-right: 10px;
    margin: none;
    vertical-align: middle;
    margin-top: auto !important;
}

.transbutton svg g {
    fill: #2F4B70;
    fill-opacity: 1;
}

.stammdaten .dienstleister {
    margin-top: 20px;
}


.stymmdaten .formularzeile {
    vertical-align: middle;
}

.stammdaten .formular .email {
    min-width: 250px;
}

.stammdaten .allusers ul {
    padding: 0px;
    margin: 0px;
}

.stammdaten .anrede {
    margin-bottom: 0px;
}

.stammdaten .ueberschrift {
    height: 20px;
    margin: 10px;
    border-bottom: 1px solid #bcbcbc;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
}

.stammdaten .stammdatencontent {
    background-color: #FFF;
    padding: 10px;
}

.stammdatencontent .agentur {
    display: none;
    height: 10pX;
}

.stammdaten .anrede input {
    width: auto;
}

.stammdaten .shortname {
    color: $tsblue;
    width: 15px;
    height: 13px;
    padding: 6px;
    padding-top: 9px;
    float: left;
    border: 1px solid #eeeeee;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
    font-size: 7px;
    text-align: center;
    font-weight: bold;
    list-style: none;
}

.stammdaten .adduser {
    width: 15px;
    height: 13px;
    padding: 5px;
    padding-top: 5px;
    border: 1px solid #eeeeee;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
    list-style: none;
    margin: 2px;
}

.stammdaten .neuermitarbeiter, .stammdaten .neuerkunde, .stammdaten .neuerdienstleister {
    display: none;
}

.stammdaten .adduser svg g {
    fill: #000000 !important;
    fill-opacity: 1;
}

.stammdaten input {
    border: none;
}

.stammdaten .anrede {
    font-size: 12px;
}

.stammdaten input {

    /*vertical-align: middle*/
}

.stammdaten .active {
    background-color: #66c992;
    color: #555555;
}

.stammdaten input[type="text"] {

    background-color: transparent;
}


.jobanlage {
    background-color: #eeeeee;
    padding: 20px;
}

.jobanlage .saveproject {
    float: right;
    margin-top: 5px;
}

.jobanlage .projekthead {
    background-color: white;
    padding: 10px;
}

.jobanlage .projektbestandteile {
    background-color: white;
    padding: 10px;
    margin-top: 20px;
}

.jobanlage .projektagentur {
    background-color: white;
    padding: 10px;
    margin-top: 20px;
}


$custom-file-text: (
    de: "Datei auswählen"
);


.jobanlage .projektkundencontainer {
    background-color: white;
    padding: 10px;
    margin-top: 20px;
}

.jobanlage .projektdienstleister {
    background-color: white;
    padding: 10px;
    margin-top: 20px;
}

.jobanlage .projekthead .jobnummer {
    float: left;
}

.jobanlage .projekthead .jobbezeichnung {
    float: right;
}

.jobanlage .projekthead .projektstart {
    float: left;
}

.jobanlage .projekthead .projektende {
    float: right;
}

.jobanlage .users .spacer {
    width: 10px;
}


.jobanlage .jobnummer {
    width: calc(50% - 10px);
}

.jobanlage .jobbezeichnung {
    width: calc(50% - 10px);
}

.jobanlage .projektstart {
    width: calc(50% - 10px);
}

.jobanlage .projektende {
    width: calc(50% - 10px);
}


.ui-icon {
    color: #555555 !important;
}

.anlage .dienstleister {
    padding: 10px;
    background-color: #fff;


}

.anlage .dienstleister input[type="text"] {
}

.anlage .dienstleister input[type="checkbox"] {
}

.anlage .dienstleister ul {
    padding: 0;

}

.anlage .dienstleister_single {
    margin-top: 20px;

}

.anlage .dienstleister ul li {
    list-style: none;
    clear: both;
    min-height: 23px;
    padding: 5px;
    border: 1px solid #e0e0e0;
    margin: 0;
    margin-top: -1px;
    overflow: hidden;
}

.anlage .dienstleister .dienstleister_single {
    font-weight: bold;

}

.anlage .dienstleister a {
    color: #555555;
}

.kunden {
    padding: 10px;
}

.anlage {
    padding: 10px;
}

.kunden input[type="text"] {
}

.kunden input[type="checkbox"] {
}

.inaktiv {
    text-decoration: line-through;
}

.pfadnavi button {
    margin-right: 10px;
}

/*
   * Add this if you want to disable IE10's implementation
   * of the winking eye in favor of your own.
   * Alternatively, you could set the 'innerToggle' option to
   * false for that browser only.
   */
::-ms-reveal {
    display: none !important;
}

/*
   * This toggle style shows a winking "eye-con" (nyuk, nyuk).
   * Open eye means "show," closed eye means "hide."
   */
.hideShowPassword-toggle {
    background-image: url(../images/wink.svg);
    background-position: 0 center;
    background-repeat: no-repeat;
    cursor: pointer;
    height: 100%;
    overflow: hidden;
    text-indent: -9999em;
    width: 4px;
    border: 0px solid transparent;
    background-color: transparent;
    width: 44px;
}

.hideShowPassword-wrapper {
    width: 100% !important;
    padding-right: 5px !important;
}

.hideShowPassword-toggle-hide {
    background-position: -44px center;
}

.login-field {
    padding-right: 0px !important;
}

.allusers ul li {
    list-style: none;
}

#dokumente_button div {
    margin-top: 2px;
    width: 80px;
    float: left;
}

#dokumente_button:hover svg g {
    fill: #2F4B70 !important;
    fill-opacity: 1;
}

.dateien svg {
    vertical-align: middle;

    float: left;
}

.dateien svg g {
    fill: #ffffff !important;
    fill-opacity: 1;
}

.buttomnav a {
    color: #FFFFFF;
    text-decoration: none;
    z-index: -50;
}
.buttomnav {
    z-index: -50;
}

.index {
    color: #555555;
    font-weight: bold;
}

.redbutton {
    background-color: red !important;
}

.pages button {
    width: 30px !important;
}

.splitt_left button {
    width: 30px !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid #eeeeee;
    height: 30px;
    min-width: 18px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #555555;
}

.splitt_right button {
    width: 30px !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid #eeeeee;
    height: 30px;
    min-width: 18px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #555555;
}

.datencontainer {
    background-color: #ffffff;
    padding: 10px;
}

.suchform select {
    color: #555555 !important;
    padding: 5px;
}

div .sticky {
    overflow-y: scroll;
}

.absoften {
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 10000;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.absoften_rot {
    background-color: rgba(255, 0, 0, 0.5);
    z-index: 10000;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.uploadmodal {
    height: 350px;
    background-color: #FFFFFF;
    z-index: 12000;
    position: fixed;
    margin: auto auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 20px;
}

.achtungmodal {
    height: 140px;
    background-color: #FFFFFF;
    z-index: 12000;
    position: fixed;
    margin: auto auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 20px;
}

.hinweismodal {
    height: 450px;
    background-color: #FFFFFF;
    z-index: 12000;
    position: fixed;
    margin: auto auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 20px;
}

.stopmodal {
    height: 220px;
    background-color: #FFFFFF;
    z-index: 12000;
    position: fixed;
    margin: auto auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 20px;
}

.stopmodal .line {
    border-top: 1px solid #eeeeee;
    margin-top: 10px;
    margin-bottom: 15px;
}

.hinweismodal .line {
    border-top: 1px solid #eeeeee;
    margin-top: 10px;
    margin-bottom: 15px;
}

.hinweislogo {
    width: 30%;
    float: right;
}

.hinweismodal button {
    margin-top: 20px;
    float: right;
    margin-right: 0px;
}

.hinweismodal button:hover {
    height: 30px;
    background: #FFFFFF;
    border: 1px solid #2F4B70;
    color: #2F4B70;
    cursor: pointer;
}

.hinweislogo img {
    width: 80% !important;
    float: right;
}

.hinweistext {
    float: left;
    font-weight: normal;
}

.hinweistext h2 {
    padding: 0;
    margin: 0;
}

.uploadmodal .line {
    border-top: 1px solid #eeeeee;
    margin-top: 10px;
    margin-bottom: 15px;
}

.uploadmodal h2 {
    margin-bottom: 10px;
}

.uploadmodal button {


}

.uploadmodal .custom-file {
    background-color: #eeeeee;
    border: 0px solid #555555;

}

.custom-file label {
}

.custom-file .label {
    color: #555555;
}

.custom-file .label-info {
    color: #555555;
}

.uploadmodal select {
}

.select-wrapper {
    /* ... */
    position: relative;
}

.select-wrapper::before {
}

.uploadmodal .beschreibung {
    color: #444444;
    font-weight: 400;
    letter-spacing: 1px;
    line-height: 22px;
    opacity: 0.800000011920929;
    width: 549px;
    text-align: left;
    margin-top: 10px;
    margin-bottom: 10px;
}

.inputfile {
}

.inputfile + label {
    color: white;
    background-color: #2F4B70;
    padding: 5px;
    opacity: 1;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    float: right;
}

.inputfile:focus + label,
.inputfile + label:hover {
    background-color: white;
    background-color: #2F4B70;
}

.inputfile + label {
    cursor: pointer;
    /* "hand" cursor */
}

.spacer {
    height: 20px;
}

.close {
    float: right;
}

.suche {
    margin-right: 10px;
}

.button-disabled {
    background-color: #eeeeee;
    border: none;
}


canvas {
}

.switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 17px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
   /* background-color: #2F4B70;*/
    background: $switchbg;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(13px);
    -ms-transform: translateX(13px);
    transform: translateX(13px);
}

.slider_red {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider_red:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider_red {
    background-color: #ff0000;
}

input:focus + .slider_red {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider_red:before {
    -webkit-transform: translateX(13px);
    -ms-transform: translateX(13px);
    transform: translateX(13px);
}

/* Rounded sliders */
.slider_red.round {
    border-radius: 17px;
}

.slider_red.round:before {
    border-radius: 50%;
}

/* Rounded sliders */
.slider.round {
    border-radius: 17px;
}

.slider.round:before {
    border-radius: 50%;
}

.readonly {
    background-color: transparent;
}

.jobanlage .bestandteilname {
}

.jobanlage .bestandteiledit {
    width: calc(100% - 200px - 1px);
    float: left;
    text-align: right;
}

.jobanlage .bestandteiledit svg {
    margin-top: 4px;
}

.jobanlage select {
}

.stammdaten select {
    /*
    background-color: #eeeeee;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    border: 0;
    outline: 0;
    -webkit-transition: 0.3s ease all;
    -moz-transition: 0.3s ease all;
    -ms-transition: 0.3s ease all;
    -o-transition: 0.3s ease all;
    transition: 0.3s ease all;
    -webkit-appearance: none;
    padding: 5px;
    margin-bottom: 10px;

     */
}


.stammdaten input[type=text] {
}

.jobanlage .projektagentur button {
    width: calc(25% - 10px);
    margin-right: 10px;
    min-width: 120px;
    color: #bcbcbc;
    background-color: #eeeeee;
    border: none;
}

.jobanlage .projektkundencontainer button {
    width: calc(25% - 10px);
    margin-right: 10px;
    min-width: 120px;
    color: #bcbcbc;
    background-color: #eeeeee;
    border: none;
}

.jobanlage .projektdienstleister button {
    width: calc(25% - 10px);
    margin-right: 10px;
    min-width: 120px;
    color: #bcbcbc;
    background-color: #eeeeee;
    border: none;
}

.jobanlage #subkategorielist ul {
    padding: 0px;
    margin: 0px;
}

.jobanlage ul#subkategorielist li > ul > li:first-child {
}


.jobanlage .projektbestandteile #neue_kategorie {
}

.jobanlage .projektbestandteile button {
    width: calc(25% - 10px);
    margin-right: 10px;
    min-width: 120px;
    color: #bcbcbc;
    background-color: #eeeeee;
    border: none;
}

.jobanlage ul#kategorielist {
    padding: 0px;
    margin: 0px;
}

.jobanlage #kategorielist li ul {
    padding: 0;

    margin-left: -10px;
    margin-top: 5px;
    margin-right: -10px;
    padding-left: 10px;
    padding-right: 10px;

}

.bluebutton {
    background-color: #2F4B70 !important;
}

.stammdaten .hinweismodal {
    height: 180px;
}

.stammdaten .hinweismodal button {
    width: 150px !important;
    margin-left: 10px;
}

.ftp ul li div {
    background-color: #FFFFFF !important;
    padding: 5px;
}

.ftp ul li div .jobs {
    background-color: #eeeeee !important;
    padding: 5px;
    color: #555555;
}

.ftp ul li div h3 {
    font-size: 12px;
}

.ftp ul li {
    list-style: none;
}

.job.beendet {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAABVJREFUGBljYCAO/CdG2agiBuoFAQDtEAn3Ke902gAAAABJRU5ErkJggg==) repeat;
}

.datentabelle {
    width: 100%;
    overflow-x: auto;
}

.ui-datepicker {
    background-color: #FFFFFF !important;
    border: 1px solid darkgray;
    padding: 5px;
}

.whitealpha {
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 0;
}

.vertical-center {
    min-height: 100%; /* Fallback for browsers do NOT support vh unit */
    min-height: 100vh; /* These two lines are counted as one :-)       */

    display: flex;
    align-items: center;
}

.list-group-item.active {
    background-color: grey !important;
    border-color: grey !important;
}

.ui-datepicker-next {
    float: right;
}

.ui-datepicker a {
    text-decoration: none;
    color: #000000;
}

.ui-datepicker-week-end {
    background-color: lightgrey;
    text-align: center;
}


.ui-datepicker-calendar td {
    text-align: center;
}


.btn-primary {
    /*background-color: #1b4b72;*/
    background: $buttonbg;
}


.tborder {
    border: 10px solid #eeeeee;
}

.wborder {
    border: 10px solid #FFFFFF;
}

.dgbackground {
    background-color: #eeeeee;
}

.pfadnavi button {
    min-width: 250px !important;
}


.table {
    margin-bottom: 0;
}

table.dataTable {
    margin: 0px !important;
}

.btn-basic {
    background-color: #eeeeee;
    cursor: default;
}

#upload-file-info {
    overflow: hidden;
}

.dateiname {
    overflow: hidden;
}

.jobdetails li div {
    white-space: nowrap;
}

#ui-datepicker-div {

    display: none;
}

#Filedata th {
    white-space: nowrap;
}


#Filedata td {
    white-space: nowrap;
}

th.no-icon::after {
    content: "" !important;
}

th.no-icon::before {
    content: "" !important;
}

.ui-datepicker-today {
    background-color: #f3ec9d !important;
}

.durchstrichen {
    color: lightgrey !important;
    text-decoration: line-through !important;
}


.btn-day,
.btn-day:visited {
    background-color: darkgrey;
    border-color: none;
}

.bg-grey{
    background-color: #E8ECEF;
}

.btn-day:hover,
.btn-day:active,
.btn-day:focus {
    background-color: #1b4b72 !important;
    color: #FFFFFF;
}

.btn-day.active {
    background-color: #1b4b72 !important;
    color: #FFFFFF;
}

td.preview {
    max-width: 30px !important;
}

.widgetcard {
    background-color: #FFFFFF;
    border-radius: 4px;
    width: 262px;
    height: 324px;
    margin: 10px;
    padding: 10px;
}

.projectcard {
    background-color: #FFFFFF;
    border: 3px solid #778CA2;
    border-radius: 4px;
    width: 262px;
    height: 324px;
    margin: 10px;
    padding: 10px;
    padding-top: 10px;
}


.documentcard {
    background-color: #FFFFFF;
    border: 3px solid #778CA2;
    border-radius: 4px;
    width: 262px;
    height: 324px;
    margin: 10px;
    padding: 10px;
    padding-top: 20px;
    font-size: 20px;
}

.dokumentencard .documenticon_text {

    margin-top: 25px;
    margin-bottom: 25px;
}
.dokumentencard .documenticon_tabelle {

    margin-top: 25px;
    margin-bottom: 25px;
}
.dokumentencard .documenticon_pdf {

    margin-top: 25px;
    margin-bottom: 25px;
}

.dokumentencard .name {

    font-size: 20px;
}
.dokumentencard .tags {

    font-size: 12px;
}

.dokumentencard {
    background-color: #FFFFFF;
    border: 1px solid #778CA2;
    border-radius: 4px;
    width: 262px;
    height: 324px;
    margin: 10px;
    padding: 10px;
    padding-top: 10px;
}

.documenticon_tabelle {
    background-color: #f4fbef;
    border-radius: 4px;
    width: 106px;
    height: 125px;
    color: #6DD230;
    font-size: 50px;
    padding-top: 25px;
    padding-left: 35px;
    margin-left: 65px;
}

.documenticon_text {
    background-color: #f1f5ff;
    border-radius: 4px;
    width: 106px;
    height: 125px;
    color: #4D7CFE;
    font-size: 47px;
    padding-top: 25px;
    padding-left: 35px;
    margin-left: 65px;
}

.documenticon_hoch {
    background-color: #f1f5ff;
    border-radius: 4px;
    width: 106px;
    height: 125px;
    color: #4D7CFE;
    font-size: 47px;
    padding-top: 0px;
    overflow: hidden;
    margin: auto auto;
    margin-top: 5px;
}


.documenticon_pdf {
    background-color: #ffffff;
    border-radius: 4px;
    width: 160px;
    height: 125px;
    color: #AAAAAA;
    font-size: 47px;
    overflow: hidden;
    margin: auto auto;
}

.anlage_documenticon_pdf{
    background-color: #ffffff;
    border-radius: 4px;
    width: 106px;
    min-height: 125px;
    color: #AAAAAA;
    overflow: hidden;
    border: 1px solid #AAAAAA;
}

.anlage_documenticon_tabelle {
    background-color: #f4fbef;
    border-radius: 4px;
    width: 106px;
    height: 125px;
    color: #6DD230;
    font-size: 50px;
    padding-top: 25px;
    padding-left: 35px;
}

.anlage_documenticon_text {
    background-color: #f1f5ff;
    border-radius: 4px;
    width: 106px;
    height: 125px;
    color: #4D7CFE;
    font-size: 47px;
    padding-top: 25px;
    padding-left: 35px;
}

a:hover {
    text-decoration: none;
}

.mask {
    background-image: url("../images/head.png");
    background-size: 100%;
    border-radius: 100%;
    width: 86px;
    height: 86px;
}

.title {
    background-color: #FFFFFF;
    height: 64px;
}

.field-title {
    color: $tsblue;
    font-family: Rubik;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    margin-top: 10px;
}

.field-input {
    background-color: #FFFFFF;
    border: 1px solid #E8ECEF;
    border-radius: 4px;
    width: 468px;
    height: 52px;
    margin-top: 10px;
    padding: 5px;
}

.name {
    color: $tsblue;
    font-family: Rubik;
    font-size: 31px;
    line-height: 37px;
    text-align: left;
}

.job-title {
    color: #778CA2;
    font-family: Rubik;
    font-size: 14px;
    line-height: 21px;
    width: 217px;
    text-align: left;
}

.document-title {
    font-size: 14px;
}
.dok-info {
    font-family: Rubik;
    font-size: 14px;
    line-height: 21px;
    text-align: left;
    margin-top: 10px;
    border-top: 1px solid #979797;
    padding-top: 16px;
    border-radius: 0;

}

.bestandteil-info {
    color: #FFAB2B;
    font-family: Rubik;
    font-size: 14px;
    line-height: 21px;
    text-align: left;
    margin-top: 10px;
    border-top: 1px solid #979797;
    padding-top: 16px;
    border-radius: 0;

}

.document-info {
    color: #FFAB2B;
    font-family: Rubik;
    font-size: 12px;
    line-height: 21px;
    text-align: left;
    margin-top: 5px;
    border-top: 1px solid #979797;
    padding-top: 16px;
    border-radius: 0;

}

.dok-left {
    width: 50%;
    float: left;
}

.dok-right {
    width: 50%;
    float: right;
    text-align: left;
}

.binfo-left {
    width: 75%;
    float: left;
}

.binfo-right {
    width: 25%;
    float: right;
    text-align: right;
}

.binfo-icon {
    background-color: #FFAB2B;
    width: 16px;
    height: 16px;
    color: #FFFFFF;
    border-radius: 50%;
    padding: 1px;
}

.document-icon {
    background-color: #FFAB2B;
    width: 16px;
    height: 16px;
    color: #FFFFFF;
    border-radius: 50%;
    font-size: 16px;
    line-height: 16px;

}

.documentcard .name {
    font-size: 20px;
}

.document-info small {
    color: #979797;
    margin-left: 25px;
}

.jobfreigabe {
    border: 1px solid #6DD230 !important;
}

.bestandteil-freigabe {
    color: #6DD230 !important;
}

.icon-freigabe {
    background-color: #6DD230 !important;
    color: #FFFFFF !important;
}

.job_keinefreigabe {
    border: 1px solid #FFAB2B !important;
}

.filter ul li {
    float: left;
    text-transform: uppercase;

}

.black {
    color: #0c0c0c;
    margin-right: 5px;
    margin-left: 5px;
}

.filter .space {
    margin-right: 20px;
}


.keine_freigabe_moeglich {
    background-color: #E8ECEF;
    width: 410px;
    height: 20px;
    color: #FFFFFF;
    font-family: Rubik;
    font-size: 14px;
    text-align: center;
    border-radius: 4px;
    text-transform: uppercase;
}

.freigabe_moeglich {
    background-color: $buttonbg;
    width: 410px;
    height: 20px;
    color: #FFFFFF;
    font-family: Rubik;
    font-size: 14px;
    text-align: center;
    border-radius: 4px;
    text-transform: uppercase;
}

.border-grey {
    border: 1px solid #E8ECEF;
    color: #778CA2;
    border-radius: 4px;
    width: 35px;
    height: 35px;
    padding: 6px;
}

.hochzeit {
    background-color: #FFAB2B;
}

.hochzeit .document-info {
    color: #FFFFFF;
}

.hochzeit .name {
    color: #FFFFFF;
}

.hochzeit .document-icon {

    background-color: #FFFFFF;
    color: #FFAB2B;
}

.line {
    border-top: 1px solid #E8ECEF;
    margin-top: 10px;
    margin-bottom: 10px;
}

.historie_left {
    float: left;
    width: 60%;
}

.historie_right {
    float: right;
    width: 40%;
}

#sidebar_right {
    position: relative;
}

.freigabe-btn {
    background-color: #E8ECEF;
    border-radius: 4px;
    width: 126px;
    height: 38px;
    color: #4D7CFE;
}

.fastlanefreigabe-btn {
    background-color: #E10000;
    border-radius: 4px;
    width: 126px;
    height: 38px;
    color: #FFFFFF;
}
#FastLaneFreigabeDoc .modal-body{
    background-color: #E10000;
}

.download-btn {
    background-color: #E8ECEF;
    border-radius: 4px;
    width: 126px;
    height: 38px;
    color: #4D7CFE;
}

.keine_freigabe_moeglich {

}

.document_freigabe{
    border: 3px solid #6DD230;
    background-color: #FFFFFF !important;
}

.standard-border {
    border: 3px solid #778CA2 !important;
}

.document_nicht_freigegeben {
    border: 3px solid #FFAB2B;
}

.btn-zurueck {
    background: $btnbackbg;
    color: $btnbackcolor;
    width: 126px;
    height: 38px;

}

.btn-normal {
    background-color: #F2F4F6;
    border-radius: 4px;
    width: 126px;
    height: 38px;
}

.bnt-best {
    background: $buttonbg;
    color: $buttoncolor;
    width: 126px;
    height: 38px;
}


.freigabe-padding {
    padding: 50px;
}

.historie {
    font-size: 16px;
    padding: 20px;
}

.checkblue {
    background-color: #1b4b72;
    color: #FFFFFF;
}

.docfrei {
    background-color: #6DD230;
    height: 65px;
    padding-top: 24px;
    padding-left: 10px;
    color: #FFFFFF;
}

.login-box {
    width: 555px;
}

.anmelden {
    background-color: #2F4B70;
    width: 372px !important;
    height: 54px !important;
    border: 4px;
}

.passwort-vergessen {
    color: #4D7CFE !important;
    line-height: 17px;
    text-align: right;
    padding-top: 10px;
    padding-bottom: 20px;
}

.passwort-vergessen a {
    color: #4D7CFE !important;
    font-family: Rubik;
    font-size: 14px;
}

.einloggen_mit {
    margin: auto auto;
    width: 208px;
    text-align: center;
    padding: 20px;
}

.azure {
    background-color: #FFFFFF;
    border: 1px solid #E8ECEF;
    border-radius: 4px;
    width: 208px;
    height: 46px;
    color: #4D7CFE;
    font-family: Rubik;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    padding-top: 15px;
    padding-left: 48px;
    margin: auto auto;
}

.previewimg{
    max-width: 160px;
    min-height: 160px;
    max-height: 160px;
    margin: auto auto;
    margin-top: 20px;
    margin-bottom: 10px;
    overflow: hidden;
}

.cardinfo{
    position: relative;
    margin-bottom: 10px;
}


.sidespacer{
    height: 10px;
}

.widgettitel{
    width: calc(100% - 20px);
    float: left;
    padding: 2px;
}
.widgetpoints{
    width: 20px;
    float: right;
    vertical-align: top;
    text-align: right;
    line-height: 5px;
}

.orangebg{
    background-color: #FFAB2B;
    color: #FFFFFF;
    font-size: 12px;
}

.orangeborder{
    border: 2px solid #FFAB2B !important;
}

.greenborder{
    border: 2px solid #6DD230 !important;
}

.redborder{
    border: 2px solid #FF0000 !important;
}

.gruenbg{
    background-color: #6DD230;
    color: #FFFFFF;
    font-size: 12px;
}

.whitebg{
    background-color: #FFFFFF;
    color: #FFAB2B;
    font-size: 12px;
}

.wordicon{
    background-color: #f1f5ff;
    border-radius: 4px;
    width: 250px;
    height: 295px;
    color: #4D7CFE;
    font-size: 100px;
    text-align: center;
    vertical-align: middle;
    margin: auto auto;
    padding: 60px;
}

.excelicon{
    background-color: #f4fbef;
    border-radius: 4px;
    width: 250px;
    height: 295px;
    color: #6DD230;
    font-size: 100px;
    text-align: center;
    vertical-align: middle;
    margin: auto auto;
    padding: 60px;
}

.in_salesforce {
    background-color: $tsblue;
    color: #FFFFFF;
    height: 64px;
    line-height: 64px;
}

.job_freigabe{
    border: 3px solid #6DD230;
}

.job_nichtfreigegeben{
    border: 3px solid #FFAB2B!important;
}

.tagtabs{
    color: #778CA2;
    font-family: Rubik;
    font-size: 14px;
    line-height: 17px;
    text-align: center;
    text-transform: uppercase;
}

.tagtabs ul{
    margin-top: 20px;
}

.tagtabs ul li{
    margin-right: 20px;
}


.projektfooter{
    background-color: #FFFFFF;
    min-height: 100px;
    bottom: 0px;
    width: 100%;
}

.bottom-spacer{
    height: 100px;
}

.projektfooter ul li{
    float: left !important;
    margin-right: 10px;
    border: 1px solid #E8ECEF;
    border-radius: 4px;
    padding: 5px;
}

.projektfooter .no-border{
    border: 1px solid transparent !important;
    color: #000000;
    width: 150px;

}

.projektfooter .werte{
    color: #000000;
}

.icon-blue{
    background-color: #2F4B70;
    color: #FFFFFF;
    border-radius: 100%;
    width: 14px;
    height: 14px;
    padding: 1px;
}

.sfzahlen-icon-kl{
    background-color: #2F4B70;
    color: #FFFFFF;
    border-radius: 100%;
    width: 25px;
    height: 25px;
    padding: 1px;
    font-size: 14px;
    padding: 3.5px;
    margin-right: 5px;
    border: 2px solid #D8D8D8;
}

.sfzahlen-icon{
    background: $iconbg;
    color: #FFFFFF;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    padding: 1px;
    font-size: 34px;
    padding: 4px;
    margin-right: 20px;
    border: 4px solid #D8D8D8;
}

.postzahlen-icon{
    background-color: #6DD230;
    color: #FFFFFF;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    padding: 1px;
    font-size: 34px;
    padding: 4px;
    margin-right: 20px;
    border: 4px solid #D8D8D8;
}

.nz-icon{
    background-color: #E10000;
    color: #FFFFFF;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    padding: 1px;
    font-size: 34px;
    padding: 4px;
    margin-right: 20px;
    border: 4px solid #D8D8D8;
}

.text-blue{
    background-color: #2F4B70;
    color: #FFFFFF;
    border-radius: 100%;
    width: 14px !important;
    height: 14px !important;
    padding: 1px;
    font-style: normal;
    font-size: 8px;
    float: left;
    line-height: 14px;
}

.zahlenblock{
    background-color: #FFFFFF;
    border-radius: 4px;
    width: 360px;
    padding: 20px;
    float: left;
    margin-right: 40px;
    margin-top: 5px;
}

.zahlenblock_dokument{
    background-color: #FFFFFF;
    border-radius: 4px;
    width: 360px;
    padding: 20px;
    float: left;
    margin-right: 40px;
    margin-top: 20px;
}

.projekte_dokument{
    background-color: #FFFFFF;
    border-radius: 4px;
    width: 360px;
    padding: 20px;
    float: left;
    margin-right: 40px;
    margin-top: 60px;
}

.zahl_big{
    font-size: 26px;
    color: $tsblue;
    line-height: 30px;
}

.lieferbox{
    background-color: #FFFFFF;
    border-radius: 4px;
    color: #000000;
    margin-top: 20px;
}

.responsebox{
    background-color: #FFFFFF;
    border-radius: 4px;
    color: #000000;
    margin-top: 20px;
}

.lieferbox > ul{
    border-top: 1px solid #eeeeee;
}

.status li{
    background-color: #F8FAFB;
    border-radius: 4px;
    padding: 2px;
    float: left;
    margin-right: 5px;
}

.dot{
    background-color: #6DD230;
    width: 8px;
    height: 8px;
    height: 8px;
    border-radius: 100%;
    float: left;
    margin-top: 5px;
    margin-right: 5px;
}

.dot-red{
    background-color: #FF0000;
    width: 8px;
    height: 8px;
    height: 8px;
    border-radius: 100%;
    float: left;
    margin-top: 5px;
    margin-right: 5px;
}


.aktiv{
    color: #4D7CFE;
    border-bottom: 2px solid #4D7CFE;

}

.verarbeitung{
    color: #000000;
}




.verarbeitung > ul > li:nth-child(odd) > ul > li{
    background-color: lightgrey;
    margin: 2px;
}

.verarbeitung > ul > li:nth-child(even) > ul > li{
    background-color: darkgrey;
    margin: 2px;
}

.verarbeitung > ul > li:first-child{
    background-color: transparent;
    margin: 2px;
}


.verarbeitung_head > ul > li {
    background-color: #2F4B70 !important;
    color: #FFFFFF !important;
    margin: 2px;
}

.tag{
    background-color: #F2F4F6;
    border-right: 2px;

}

#clockdiv{
    width: 250px;
    height: 55px;
    background-color: red;
    float: right;
}

#clockdiv div span{
    width: 35px;
    text-align: center;
    color: black;
}

.document_gestoppt{
    background-color: red;
    border: 3px solid red;
}

.autofiles{

}

.bestandteil-kunde{
    color: #FFAB2B;
}

.gestoppt{
    background-color: red;
}

div.verfuegbare_spalten li{
    list-style: none;
    background-color: lightgrey;
    border-radius: 5px;
    float: left;
    padding: 2px;
    padding-bottom: 0px;
    padding-top: 0px;
    margin: 1px;
    margin-right: 3px;
    color: #000000;
    z-index: 1000;
}

.add_makerdata{
    color: darkgrey ;
}



.filterDiv {
    display: none; /* Hidden by default */
}

/* The "show" class is added to the filtered elements */
.show {
    display: block;
}


.show_cell {
    display: table-row;
}


.pointer{
    cursor: pointer;
}

.dokumentencard{
    color: $cardcolor;
}

.dokumentencard .las{
    background: $iconbg;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}



.activecard{
    background-color: $activecardbg !important;
    color: $activecardcolor !important;
}
.activecard .name{
    color: $activecardcolor  !important;
}

.freigabe-btn{
    background: $buttonbg;
    color: $buttoncolor;
}


#dokcontainer > a  > .la-plus-circle{
    color: $iconbg !important;
}

.dhbg{
    background: $dhbg;
    text-align: center;

}

.dhbg #sym_spacer{
    padding: 6px;
    margin: auto auto;
}

.ohnebg{
    margin-top: 10px;
    text-align: center;
}

.logoplacer{
    margin: auto auto !important;
}


.menubox{
    margin: 10px;
}

.appsym{
    width: 20px !important;
}

.boxfinish{

}

.wm_zeile input{
    height: 20px;
    margin-right: 2px;
}

.wm_zeile select{
    height: 20px;
    margin-right: 2px;
    font-size: 10px;
    padding: 1px;
}

.blankcss{
    height: 10px;
}

.small_documenticon_pdf{
    height: 80px;
    width: 20px;
    float: left;
    margin-right: 40px;
}


.documenttable li{
    background-color: $tsblue;
    color: white;
    border-radius: 5px;
    padding: 2px;
    margin: 2px;
    width: auto;
    float: left;
}

.documenttable .name{
    font-size: 12px !important;
}


.documenttable .documenticon_tabelle{
    float:left;
    height: 40px;
    width: 20px;
    border-radius: 4px;
    color: #6DD230;
    padding-top: 10px;
    padding-left: 7px;
    margin: 0px;
    margin-right: 40px;
    font-size: 12px;
}
.documenttable .documenticon_text{
    float:left;
    height: 40px;
    width: 20px;
    border-radius: 4px;
    padding-top: 10px;
    padding-left: 4px;
    margin: 0px;
    margin-right: 40px;
    font-size: 12px;
}

.documenttable .tagcount li{
    width: 20px;
    height: 20px;
    border-radius: 10px;
    padding: 0;
    padding-left: 6px;
    padding-top: 1px;
}

.layover{
    z-index: 1000;
    right: 0px;
    top: 0px;
}

.activerow{
    background-color: $activecardbg;
    color: $activecardcolor !important;
}

.activerow .name{
    color: $activecardcolor;
}

.changeview  a.active{
    background-color: $tsblue !important;
    color: #FFFFFF;
}

.ablehnung-btn {
    background-color: #E10000;
    border-radius: 4px;
    width: 126px;
    height: 38px;
    color: #FFFFFF;
}
