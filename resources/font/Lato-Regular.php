<?php
$type = 'TrueType';
$name = 'Lato-Regular';
$desc = array('Ascent'=>805,'Descent'=>-195,'CapHeight'=>717,'Flags'=>32,'FontBBox'=>'[-547 -269 1343 1079]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>532);
$up = -233;
$ut = 45;
$cw = array(
	chr(0)=>532,chr(1)=>532,chr(2)=>532,chr(3)=>532,chr(4)=>532,chr(5)=>532,chr(6)=>532,chr(7)=>532,chr(8)=>532,chr(9)=>532,chr(10)=>532,chr(11)=>532,chr(12)=>532,chr(13)=>532,chr(14)=>532,chr(15)=>532,chr(16)=>532,chr(17)=>532,chr(18)=>532,chr(19)=>532,chr(20)=>532,chr(21)=>532,
	chr(22)=>532,chr(23)=>532,chr(24)=>532,chr(25)=>532,chr(26)=>532,chr(27)=>532,chr(28)=>532,chr(29)=>532,chr(30)=>532,chr(31)=>532,' '=>256,'!'=>269,'"'=>371,'#'=>580,'$'=>580,'%'=>802,'&'=>712,'\''=>204,'('=>267,')'=>267,'*'=>425,'+'=>580,
	','=>227,'-'=>372,'.'=>236,'/'=>452,'0'=>580,'1'=>580,'2'=>580,'3'=>580,'4'=>580,'5'=>580,'6'=>580,'7'=>580,'8'=>580,'9'=>580,':'=>250,';'=>262,'<'=>580,'='=>580,'>'=>580,'?'=>448,'@'=>837,'A'=>677,
	'B'=>647,'C'=>668,'D'=>761,'E'=>578,'F'=>566,'G'=>731,'H'=>764,'I'=>280,'J'=>423,'K'=>663,'L'=>514,'M'=>929,'N'=>764,'O'=>801,'P'=>601,'Q'=>801,'R'=>627,'S'=>543,'T'=>591,'U'=>736,'V'=>677,'W'=>1036,
	'X'=>649,'Y'=>624,'Z'=>602,'['=>306,'\\'=>452,']'=>306,'^'=>580,'_'=>459,'`'=>400,'a'=>497,'b'=>560,'c'=>478,'d'=>560,'e'=>528,'f'=>351,'g'=>520,'h'=>558,'i'=>240,'j'=>240,'k'=>508,'l'=>236,'m'=>823,
	'n'=>558,'o'=>567,'p'=>561,'q'=>560,'r'=>364,'s'=>433,'t'=>359,'u'=>558,'v'=>516,'w'=>786,'x'=>498,'y'=>516,'z'=>452,'{'=>301,'|'=>251,'}'=>301,'~'=>580,chr(127)=>532,chr(128)=>580,chr(129)=>532,chr(130)=>216,chr(131)=>338,
	chr(132)=>368,chr(133)=>750,chr(134)=>580,chr(135)=>580,chr(136)=>400,chr(137)=>1164,chr(138)=>543,chr(139)=>281,chr(140)=>1091,chr(141)=>532,chr(142)=>602,chr(143)=>532,chr(144)=>532,chr(145)=>214,chr(146)=>214,chr(147)=>366,chr(148)=>366,chr(149)=>580,chr(150)=>580,chr(151)=>794,chr(152)=>400,chr(153)=>751,
	chr(154)=>433,chr(155)=>282,chr(156)=>873,chr(157)=>532,chr(158)=>452,chr(159)=>624,chr(160)=>256,chr(161)=>252,chr(162)=>580,chr(163)=>580,chr(164)=>580,chr(165)=>580,chr(166)=>254,chr(167)=>495,chr(168)=>400,chr(169)=>832,chr(170)=>368,chr(171)=>429,chr(172)=>580,chr(173)=>372,chr(174)=>832,chr(175)=>400,
	chr(176)=>415,chr(177)=>580,chr(178)=>334,chr(179)=>334,chr(180)=>400,chr(181)=>650,chr(182)=>701,chr(183)=>260,chr(184)=>400,chr(185)=>334,chr(186)=>402,chr(187)=>430,chr(188)=>734,chr(189)=>726,chr(190)=>742,chr(191)=>440,chr(192)=>677,chr(193)=>677,chr(194)=>677,chr(195)=>677,chr(196)=>677,chr(197)=>677,
	chr(198)=>931,chr(199)=>668,chr(200)=>578,chr(201)=>578,chr(202)=>578,chr(203)=>578,chr(204)=>280,chr(205)=>280,chr(206)=>280,chr(207)=>280,chr(208)=>769,chr(209)=>764,chr(210)=>801,chr(211)=>801,chr(212)=>801,chr(213)=>801,chr(214)=>801,chr(215)=>580,chr(216)=>801,chr(217)=>736,chr(218)=>736,chr(219)=>736,
	chr(220)=>736,chr(221)=>624,chr(222)=>599,chr(223)=>580,chr(224)=>497,chr(225)=>497,chr(226)=>497,chr(227)=>497,chr(228)=>497,chr(229)=>497,chr(230)=>805,chr(231)=>478,chr(232)=>528,chr(233)=>528,chr(234)=>528,chr(235)=>528,chr(236)=>240,chr(237)=>240,chr(238)=>240,chr(239)=>240,chr(240)=>566,chr(241)=>558,
	chr(242)=>567,chr(243)=>567,chr(244)=>567,chr(245)=>567,chr(246)=>567,chr(247)=>580,chr(248)=>567,chr(249)=>558,chr(250)=>558,chr(251)=>558,chr(252)=>558,chr(253)=>516,chr(254)=>561,chr(255)=>516);
$enc = 'cp1252';
$uv = array(0=>array(0,128),128=>8364,130=>8218,131=>402,132=>8222,133=>8230,134=>array(8224,2),136=>710,137=>8240,138=>352,139=>8249,140=>338,142=>381,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),152=>732,153=>8482,154=>353,155=>8250,156=>339,158=>382,159=>376,160=>array(160,96));
$file = 'Lato-Regular.z';
$originalsize = 43428;
$subsetted = true;
?>
