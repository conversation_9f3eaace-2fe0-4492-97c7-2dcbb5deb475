<?php
$type = 'TrueType';
$name = 'Lato-Bold';
$desc = array('Ascent'=>805,'Descent'=>-195,'CapHeight'=>723,'Flags'=>32,'FontBBox'=>'[-603 -285 1344 1100]','ItalicAngle'=>0,'StemV'=>120,'MissingWidth'=>547);
$up = -231;
$ut = 50;
$cw = array(
	chr(0)=>547,chr(1)=>547,chr(2)=>547,chr(3)=>547,chr(4)=>547,chr(5)=>547,chr(6)=>547,chr(7)=>547,chr(8)=>547,chr(9)=>547,chr(10)=>547,chr(11)=>547,chr(12)=>547,chr(13)=>547,chr(14)=>547,chr(15)=>547,chr(16)=>547,chr(17)=>547,chr(18)=>547,chr(19)=>547,chr(20)=>547,chr(21)=>547,
	chr(22)=>547,chr(23)=>547,chr(24)=>547,chr(25)=>547,chr(26)=>547,chr(27)=>547,chr(28)=>547,chr(29)=>547,chr(30)=>547,chr(31)=>547,' '=>243,'!'=>281,'"'=>396,'#'=>580,'$'=>580,'%'=>820,'&'=>724,'\''=>212,'('=>275,')'=>275,'*'=>425,'+'=>580,
	','=>234,'-'=>373,'.'=>238,'/'=>466,'0'=>580,'1'=>580,'2'=>580,'3'=>580,'4'=>580,'5'=>580,'6'=>580,'7'=>580,'8'=>580,'9'=>580,':'=>258,';'=>271,'<'=>580,'='=>580,'>'=>580,'?'=>460,'@'=>844,'A'=>695,
	'B'=>659,'C'=>662,'D'=>761,'E'=>575,'F'=>566,'G'=>726,'H'=>771,'I'=>297,'J'=>430,'K'=>686,'L'=>520,'M'=>945,'N'=>771,'O'=>808,'P'=>619,'Q'=>808,'R'=>643,'S'=>549,'T'=>600,'U'=>743,'V'=>695,'W'=>1051,
	'X'=>671,'Y'=>648,'Z'=>606,'['=>314,'\\'=>466,']'=>314,'^'=>580,'_'=>467,'`'=>400,'a'=>508,'b'=>568,'c'=>482,'d'=>568,'e'=>534,'f'=>359,'g'=>528,'h'=>564,'i'=>254,'j'=>254,'k'=>535,'l'=>249,'m'=>838,
	'n'=>564,'o'=>575,'p'=>568,'q'=>568,'r'=>373,'s'=>440,'t'=>372,'u'=>564,'v'=>528,'w'=>803,'x'=>522,'y'=>528,'z'=>460,'{'=>306,'|'=>261,'}'=>306,'~'=>580,chr(127)=>547,chr(128)=>580,chr(129)=>547,chr(130)=>218,chr(131)=>351,
	chr(132)=>384,chr(133)=>772,chr(134)=>580,chr(135)=>580,chr(136)=>400,chr(137)=>1186,chr(138)=>549,chr(139)=>291,chr(140)=>1082,chr(141)=>547,chr(142)=>606,chr(143)=>547,chr(144)=>547,chr(145)=>221,chr(146)=>221,chr(147)=>387,chr(148)=>387,chr(149)=>580,chr(150)=>580,chr(151)=>798,chr(152)=>400,chr(153)=>757,
	chr(154)=>440,chr(155)=>291,chr(156)=>876,chr(157)=>547,chr(158)=>460,chr(159)=>648,chr(160)=>243,chr(161)=>270,chr(162)=>580,chr(163)=>580,chr(164)=>580,chr(165)=>580,chr(166)=>264,chr(167)=>504,chr(168)=>400,chr(169)=>824,chr(170)=>367,chr(171)=>456,chr(172)=>580,chr(173)=>373,chr(174)=>824,chr(175)=>400,
	chr(176)=>416,chr(177)=>580,chr(178)=>334,chr(179)=>334,chr(180)=>400,chr(181)=>660,chr(182)=>737,chr(183)=>275,chr(184)=>400,chr(185)=>334,chr(186)=>401,chr(187)=>456,chr(188)=>743,chr(189)=>736,chr(190)=>750,chr(191)=>455,chr(192)=>695,chr(193)=>695,chr(194)=>695,chr(195)=>695,chr(196)=>695,chr(197)=>695,
	chr(198)=>933,chr(199)=>662,chr(200)=>575,chr(201)=>575,chr(202)=>575,chr(203)=>575,chr(204)=>297,chr(205)=>297,chr(206)=>297,chr(207)=>297,chr(208)=>770,chr(209)=>771,chr(210)=>808,chr(211)=>808,chr(212)=>808,chr(213)=>808,chr(214)=>808,chr(215)=>580,chr(216)=>808,chr(217)=>743,chr(218)=>743,chr(219)=>743,
	chr(220)=>743,chr(221)=>648,chr(222)=>616,chr(223)=>603,chr(224)=>508,chr(225)=>508,chr(226)=>508,chr(227)=>508,chr(228)=>508,chr(229)=>508,chr(230)=>810,chr(231)=>483,chr(232)=>534,chr(233)=>534,chr(234)=>534,chr(235)=>534,chr(236)=>254,chr(237)=>255,chr(238)=>254,chr(239)=>254,chr(240)=>571,chr(241)=>564,
	chr(242)=>575,chr(243)=>575,chr(244)=>575,chr(245)=>575,chr(246)=>575,chr(247)=>580,chr(248)=>574,chr(249)=>564,chr(250)=>564,chr(251)=>564,chr(252)=>564,chr(253)=>528,chr(254)=>568,chr(255)=>528);
$enc = 'cp1252';
$uv = array(0=>array(0,128),128=>8364,130=>8218,131=>402,132=>8222,133=>8230,134=>array(8224,2),136=>710,137=>8240,138=>352,139=>8249,140=>338,142=>381,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),152=>732,153=>8482,154=>353,155=>8250,156=>339,158=>382,159=>376,160=>array(160,96));
$file = 'Lato-Bold.z';
$originalsize = 43596;
$subsetted = true;
?>
