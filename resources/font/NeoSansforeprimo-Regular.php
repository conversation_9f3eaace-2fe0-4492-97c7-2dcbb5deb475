<?php
$type = 'TrueType';
$name = 'NeoSansforeprimo-Regular';
$desc = array('Ascent'=>765,'Descent'=>-235,'CapHeight'=>743,'Flags'=>32,'FontBBox'=>'[-124 -216 1394 980]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>500);
$up = -49;
$ut = 57;
$cw = array(
	chr(0)=>500,chr(1)=>500,chr(2)=>500,chr(3)=>500,chr(4)=>500,chr(5)=>500,chr(6)=>500,chr(7)=>500,chr(8)=>500,chr(9)=>500,chr(10)=>500,chr(11)=>500,chr(12)=>500,chr(13)=>500,chr(14)=>500,chr(15)=>500,chr(16)=>500,chr(17)=>500,chr(18)=>500,chr(19)=>500,chr(20)=>500,chr(21)=>500,
	chr(22)=>500,chr(23)=>500,chr(24)=>500,chr(25)=>500,chr(26)=>500,chr(27)=>500,chr(28)=>500,chr(29)=>500,chr(30)=>500,chr(31)=>500,' '=>247,'!'=>217,'"'=>362,'#'=>660,'$'=>605,'%'=>794,'&'=>678,'\''=>192,'('=>305,')'=>305,'*'=>465,'+'=>605,
	','=>214,'-'=>333,'.'=>214,'/'=>355,'0'=>605,'1'=>605,'2'=>605,'3'=>605,'4'=>605,'5'=>605,'6'=>605,'7'=>605,'8'=>605,'9'=>605,':'=>219,';'=>219,'<'=>605,'='=>605,'>'=>605,'?'=>446,'@'=>767,'A'=>633,
	'B'=>616,'C'=>545,'D'=>641,'E'=>551,'F'=>551,'G'=>612,'H'=>680,'I'=>267,'J'=>257,'K'=>607,'L'=>514,'M'=>761,'N'=>680,'O'=>661,'P'=>593,'Q'=>661,'R'=>625,'S'=>550,'T'=>590,'U'=>662,'V'=>643,'W'=>825,
	'X'=>646,'Y'=>583,'Z'=>597,'['=>327,'\\'=>355,']'=>327,'^'=>605,'_'=>494,'`'=>372,'a'=>519,'b'=>541,'c'=>452,'d'=>541,'e'=>544,'f'=>381,'g'=>559,'h'=>557,'i'=>247,'j'=>247,'k'=>496,'l'=>244,'m'=>798,
	'n'=>557,'o'=>545,'p'=>541,'q'=>541,'r'=>342,'s'=>466,'t'=>385,'u'=>557,'v'=>530,'w'=>786,'x'=>547,'y'=>534,'z'=>482,'{'=>331,'|'=>249,'}'=>331,'~'=>605,chr(127)=>500,chr(128)=>605,chr(129)=>500,chr(130)=>186,chr(131)=>605,
	chr(132)=>336,chr(133)=>606,chr(134)=>404,chr(135)=>404,chr(136)=>373,chr(137)=>1134,chr(138)=>550,chr(139)=>354,chr(140)=>983,chr(141)=>500,chr(142)=>597,chr(143)=>500,chr(144)=>500,chr(145)=>186,chr(146)=>186,chr(147)=>336,chr(148)=>336,chr(149)=>418,chr(150)=>310,chr(151)=>600,chr(152)=>493,chr(153)=>790,
	chr(154)=>466,chr(155)=>354,chr(156)=>899,chr(157)=>500,chr(158)=>482,chr(159)=>583,chr(160)=>247,chr(161)=>217,chr(162)=>605,chr(163)=>605,chr(164)=>605,chr(165)=>605,chr(166)=>249,chr(167)=>547,chr(168)=>373,chr(169)=>750,chr(170)=>294,chr(171)=>614,chr(172)=>605,chr(173)=>333,chr(174)=>598,chr(175)=>373,
	chr(176)=>342,chr(177)=>605,chr(178)=>333,chr(179)=>333,chr(180)=>373,chr(181)=>557,chr(182)=>622,chr(183)=>214,chr(184)=>366,chr(185)=>333,chr(186)=>322,chr(187)=>614,chr(188)=>741,chr(189)=>744,chr(190)=>826,chr(191)=>446,chr(192)=>633,chr(193)=>633,chr(194)=>633,chr(195)=>633,chr(196)=>633,chr(197)=>633,
	chr(198)=>859,chr(199)=>545,chr(200)=>551,chr(201)=>551,chr(202)=>551,chr(203)=>551,chr(204)=>267,chr(205)=>267,chr(206)=>267,chr(207)=>267,chr(208)=>641,chr(209)=>680,chr(210)=>661,chr(211)=>661,chr(212)=>661,chr(213)=>661,chr(214)=>661,chr(215)=>605,chr(216)=>661,chr(217)=>662,chr(218)=>662,chr(219)=>662,
	chr(220)=>662,chr(221)=>583,chr(222)=>593,chr(223)=>581,chr(224)=>519,chr(225)=>519,chr(226)=>519,chr(227)=>519,chr(228)=>519,chr(229)=>519,chr(230)=>853,chr(231)=>452,chr(232)=>544,chr(233)=>544,chr(234)=>544,chr(235)=>544,chr(236)=>247,chr(237)=>247,chr(238)=>247,chr(239)=>247,chr(240)=>545,chr(241)=>557,
	chr(242)=>545,chr(243)=>545,chr(244)=>545,chr(245)=>545,chr(246)=>545,chr(247)=>605,chr(248)=>545,chr(249)=>557,chr(250)=>557,chr(251)=>557,chr(252)=>557,chr(253)=>534,chr(254)=>541,chr(255)=>534);
$enc = 'cp1252';
$uv = array(0=>array(0,128),128=>8364,130=>8218,131=>402,132=>8222,133=>8230,134=>array(8224,2),136=>710,137=>8240,138=>352,139=>8249,140=>338,142=>381,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),152=>732,153=>8482,154=>353,155=>8250,156=>339,158=>382,159=>376,160=>array(160,96));
$file = 'NeoSansforeprimo-Regular.z';
$originalsize = 47756;
$subsetted = true;
?>
