<?php
$type = 'TrueType';
$name = 'AvenirNextLTPro-Regular';
$desc = array('Ascent'=>756,'Descent'=>-244,'CapHeight'=>708,'Flags'=>32,'FontBBox'=>'[-205 -250 975 945]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>512);
$up = -125;
$ut = 50;
$cw = array(
	chr(0)=>512,chr(1)=>512,chr(2)=>512,chr(3)=>512,chr(4)=>512,chr(5)=>512,chr(6)=>512,chr(7)=>512,chr(8)=>512,chr(9)=>512,chr(10)=>512,chr(11)=>512,chr(12)=>512,chr(13)=>512,chr(14)=>512,chr(15)=>512,chr(16)=>512,chr(17)=>512,chr(18)=>512,chr(19)=>512,chr(20)=>512,chr(21)=>512,
	chr(22)=>512,chr(23)=>512,chr(24)=>512,chr(25)=>512,chr(26)=>512,chr(27)=>512,chr(28)=>512,chr(29)=>512,chr(30)=>512,chr(31)=>512,' '=>250,'!'=>328,'"'=>405,'#'=>556,'$'=>580,'%'=>833,'&'=>704,'\''=>260,'('=>300,')'=>300,'*'=>444,'+'=>666,
	','=>260,'-'=>320,'.'=>260,'/'=>370,'0'=>580,'1'=>580,'2'=>580,'3'=>580,'4'=>580,'5'=>580,'6'=>580,'7'=>580,'8'=>580,'9'=>580,':'=>300,';'=>300,'<'=>666,'='=>666,'>'=>666,'?'=>482,'@'=>800,'A'=>700,
	'B'=>636,'C'=>720,'D'=>757,'E'=>592,'F'=>562,'G'=>779,'H'=>718,'I'=>260,'J'=>492,'K'=>628,'L'=>510,'M'=>886,'N'=>764,'O'=>850,'P'=>580,'Q'=>843,'R'=>599,'S'=>564,'T'=>570,'U'=>710,'V'=>623,'W'=>972,
	'X'=>649,'Y'=>602,'Z'=>572,'['=>300,'\\'=>370,']'=>300,'^'=>666,'_'=>500,'`'=>240,'a'=>534,'b'=>637,'c'=>500,'d'=>637,'e'=>572,'f'=>295,'g'=>632,'h'=>583,'i'=>250,'j'=>251,'k'=>510,'l'=>252,'m'=>883,
	'n'=>581,'o'=>611,'p'=>635,'q'=>635,'r'=>360,'s'=>444,'t'=>317,'u'=>581,'v'=>488,'w'=>746,'x'=>484,'y'=>488,'z'=>442,'{'=>300,'|'=>222,'}'=>300,'~'=>666,chr(127)=>512,chr(128)=>580,chr(129)=>512,chr(130)=>222,chr(131)=>580,
	chr(132)=>387,chr(133)=>1000,chr(134)=>556,chr(135)=>556,chr(136)=>240,chr(137)=>1000,chr(138)=>564,chr(139)=>315,chr(140)=>992,chr(141)=>512,chr(142)=>572,chr(143)=>512,chr(144)=>512,chr(145)=>222,chr(146)=>222,chr(147)=>387,chr(148)=>387,chr(149)=>500,chr(150)=>400,chr(151)=>600,chr(152)=>240,chr(153)=>1000,
	chr(154)=>444,chr(155)=>315,chr(156)=>994,chr(157)=>512,chr(158)=>442,chr(159)=>602,chr(160)=>250,chr(161)=>328,chr(162)=>580,chr(163)=>580,chr(164)=>580,chr(165)=>580,chr(166)=>222,chr(167)=>580,chr(168)=>240,chr(169)=>800,chr(170)=>360,chr(171)=>482,chr(172)=>666,chr(173)=>320,chr(174)=>600,chr(175)=>240,
	chr(176)=>400,chr(177)=>666,chr(178)=>370,chr(179)=>370,chr(180)=>240,chr(181)=>556,chr(182)=>600,chr(183)=>260,chr(184)=>240,chr(185)=>370,chr(186)=>360,chr(187)=>482,chr(188)=>840,chr(189)=>840,chr(190)=>840,chr(191)=>482,chr(192)=>700,chr(193)=>700,chr(194)=>700,chr(195)=>700,chr(196)=>700,chr(197)=>700,
	chr(198)=>990,chr(199)=>720,chr(200)=>592,chr(201)=>592,chr(202)=>592,chr(203)=>592,chr(204)=>260,chr(205)=>260,chr(206)=>260,chr(207)=>260,chr(208)=>757,chr(209)=>764,chr(210)=>850,chr(211)=>850,chr(212)=>850,chr(213)=>850,chr(214)=>850,chr(215)=>666,chr(216)=>850,chr(217)=>710,chr(218)=>710,chr(219)=>710,
	chr(220)=>710,chr(221)=>602,chr(222)=>580,chr(223)=>597,chr(224)=>534,chr(225)=>534,chr(226)=>534,chr(227)=>534,chr(228)=>534,chr(229)=>534,chr(230)=>870,chr(231)=>500,chr(232)=>572,chr(233)=>572,chr(234)=>572,chr(235)=>572,chr(236)=>250,chr(237)=>250,chr(238)=>250,chr(239)=>250,chr(240)=>611,chr(241)=>581,
	chr(242)=>611,chr(243)=>611,chr(244)=>611,chr(245)=>611,chr(246)=>611,chr(247)=>666,chr(248)=>611,chr(249)=>581,chr(250)=>581,chr(251)=>581,chr(252)=>581,chr(253)=>488,chr(254)=>633,chr(255)=>488);
$enc = 'cp1252';
$uv = array(0=>array(0,128),128=>8364,130=>8218,131=>402,132=>8222,133=>8230,134=>array(8224,2),136=>710,137=>8240,138=>352,139=>8249,140=>338,142=>381,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),152=>732,153=>8482,154=>353,155=>8250,156=>339,158=>382,159=>376,160=>array(160,96));
$file = 'AvenirNextLTPro-Regular.z';
$originalsize = 29152;
$subsetted = true;
?>
