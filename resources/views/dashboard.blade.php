@extends('layouts.app')
@section('content')
<div class="col" >
    <div id="StopModal" class="modal fade">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Hin<PERSON>s
                </div>
                <div class="modal-body">
                    <h4>Möchten Sie wirklich dieses Projekt stoppen oder pausieren?</h4>
                    <h4 id="stopjobname" style="color: #1b4b72;"></h4>
                    <div class="float-right ">
                        <input type="hidden" id="idstop" name="idstop" value="">
                    </div>
                    <div class="modal-footer mt-4">
                        <button onclick="$('.absoften').hide();$('#StopModal').hide();" class="btn btn-primary float-right ">Nein, zurück</button>
                        <button onclick="$('.absoften').hide();$('#StopModal').hide();" class="btn btn-primary float-right ">Ja, Projekt stoppen</button>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="begruessung col-12 mb-5">
        <div class="brand_logo mt-4">
                <img src="images/TGD_Logo_RGB_positiv.svg" alt="TeamGoDirect"/>
        </div>
        <div class="anrede">Hallo {{$daten['userdata']['anrede']}} {{$daten['userdata']['name']}},</div>
        <div>
            willkommen bei {{((config('app.mandant') != 'DIRECTHUB') ? 'TransferSafe' : 'directHUB')}}, dem Tool für den Austausch sensibler Daten und Dokumente gemäß DSGVO-Vorgaben. Sollten Sie Fragen haben, kontaktieren Sie bitte Ihren zuständigen Ansprechpartner bei team go direct oder wenden Sie sich an unser Support-Team <a href="mailto:{{config('app.SUPPORT_MAIL')}}">{{config('app.SUPPORT_MAIL')}}</a>.
        </div>
    </div>
    <input type="hidden" id="akt_pdfpage" name="akt_pdfpage" value="1"/>
    @foreach($daten['aufgaben'] AS $aufgabe)
        @if(!empty($aufgabe->id))
            <div id="preview_{{$aufgabe->id}}Modal" class="modal fade" >
                <div class="modal-dialog modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <strong>{{$aufgabe->jobbezeichnung}}</strong>
                            <div calss="col-1 float-right" onclick="$('#preview_{{$aufgabe->id}}Modal').modal('hide');">X</div>
                        </div>
                        <div class="modal-body">
                            @if(!empty($aufgabe->preview_img) && substr(base64_encode($aufgabe->preview_img), 0,1) != 'J')
                        <img  class="w-100" class="preview"  data-container="body"  title="{{(!empty($aufgabe->preview_name) ? $aufgabe->preview_name : '')}}"  src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}"   />
                            @else
                            <div id="seitenzahlen">
                                <button
                                    onclick="previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                    <
                                </button>
                                <button
                                    onclick="previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1 ); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                    id="pdf_next">>
                                </button>

                            </div>
                            <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">
                            <div class="pdfview">
                                <div class="pdfviewer" style="overflow: hidden !important;">
                                    <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                    <canvas data="" type="" id="preview_{{$aufgabe->id}}_big" class="pdf_inhalt" style="overflow: hidden; width: 100%;" >
                                    </canvas>
                                    <script>
                                        $(document).ready(function(){
                                            previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big', 1, 1);
                                        });
                                    </script>

                                </div>
                            </div>
                                @endif
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
        @endif
    @endforeach


<div>
    <div class="col-12 col-md-12 float-left">
        <div class="headline">
            <h2>Ihre Projekte (letzte Aktivitäten)</h2>
        </div>
        {{--
        <div class="bg-white col-12 p-3">
            <ul class="p-0">
                <li class="float-left mr-4 underline">Alle</li>
                <li class="float-left mr-4">Standard</li>
                <li class="float-left mr-4">Auto-Mailer</li>
            </ul>
        </div>
        --}}
        <div class="offene_aufgaben">
            <table class="table table-striped table-bordered" id="dashboardtable" >
                <thead>
                <tr>
                    <th class="preview  no-icon">

                    </th>
                    <th class="sorting">
                        Projektnummer
                    </th>
                    <th class="w-60">
                        Projektname
                    </th>
                    <th class="w-40">
                        Aktivität
                    </th>
                    <th class="w-40">
                        Upload-Menge
                    </th>
                    <th data-date-format="ddmmyyyy" >
                        Datum
                    </th>
                    <th class="favorit text-center p-2 no-icon" >
                        <img src="{{asset('images/fav_unset.svg')}}">
                    </th>
                    {{--
                    <th class="dashaktion text-center p-2 no-icon" >
                    </th>
                    --}}
                </tr>
                </thead>
                <tbody>
                @foreach($daten['aufgaben'] AS $aufgabe)


                    @if(!empty($aufgabe->id))

                    <tr class="topline">
                        <td style="max-width: 30px;" class="preview">
                            <a href="{{url('freigabe/' . $aufgabe->id )}}">
                            @if(!empty($aufgabe->preview_img) && substr(base64_encode($aufgabe->preview_img), 0,1) != 'J')
                               <img src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}" class="w-100" class="preview"   data-toggle="modal" data-target="#preview_{{$aufgabe->id}}Modal" data-container="body"  title="{{(!empty($aufgabe->preview_name) ? $aufgabe->preview_name : '')}}" />
                            @else
                                <canvas data="" type="application/pdf" id="preview_{{$aufgabe->id}}" class="pdf_inhalt" style="overflow: hidden; width: 100%; " data-toggle="modal" data-target="#preview_{{$aufgabe->id}}Modal"  ></canvas>
                                <script>
                                    $(document).ready(function(){
                                        previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}', 1, 1);
                                    });
                                </script>
                            @endif
                            </a>
                        </td>
                        <td  >
                            <a href="./freigabe/{{$aufgabe->id}}"><span
                                        class="">#{{$aufgabe->id}}</span></a>
                        </td>
                        <td >
                            <a href="./freigabe/{{$aufgabe->id}}"><span
                                        class="">{{$aufgabe->jobbezeichnung}}</span></a>
                            @if(session('userdata')['rechte']['typ']  == 'ersteller' && !($aufgabe->fuer_kunde_frei == 1 && $aufgabe->in_bearbeitung == 0) )
                                    <a href="{{url('neuesprojekt').'/' . $aufgabe->id}}" ><sup><img
                                            src="{{asset('images/einstellungen.svg')}}" class="einstellungen float-right" style="height: 12px;" /></sup></a>
                            @else
                                <a href="{{url('neuesprojekt').'/' . $aufgabe->id}}"  class="float-right"><i class="las la-check"></i></a>
                            @endif
                        </td>
                        <td>
                            {{--
                            @if(session('userdata')['rechte']['typ'] == 'ersteller')
                                {{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe->taetigkeit)}} {{(empty($aufgabe['last_action']['aktion'])? '': $aufgabe['last_action']['aktion']. ' | ' . $aufgabe['last_action']['name'])}}
                            @elseif(session('userdata')['rechte']['typ'] == 'kunde')
                                {{(empty($aufgabe->taetigkeit_kunde)? 'Angelegt': $aufgabe->taetigkeit_kunde)}} {{(empty($aufgabe->taetigkeit_kunde)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @elseif(session('userdata')['rechte']['typ'] == 'dienstleister')
                                {{(empty($aufgabe->taetigkeit_dienstleister)? 'Angelegt': $aufgabe->taetigkeit_dienstleister)}} {{(empty($aufgabe->taetigkeit_dienstleister)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @endif
                            --}}

                            <a href="./freigabe/{{$aufgabe->id}}">{{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe['last_action']['aktion'])}}
                                @if(!empty($aufgabe['last_action']['id']))
                                    | {{ $aufgabe['last_action']['user'] ? $aufgabe['last_action']['user'] : ''}}
                                @endif</a>

                        </td>
                        <td class="">
                            {{number_format($aufgabe->count, 0 , ',','.')}}
                        </td>
                        <td>
                            <a href="./freigabe/{{$aufgabe->id}}"><span style="display: none;">{{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('YmdHi') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('YmdHi'))}}</span>
                                {{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('d.m.Y H:i') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('d.m.Y H:i'))}}</a>
                        </td>

                        <td onclick="toggle_fav('{{ $aufgabe['id'] }}');" class=" text-center p-2">
                            <span style="display:none;">{{$aufgabe['j_favorit']}}</span>
                            <img src="{{($aufgabe['j_favorit'] ? asset('images/fav_set.svg') : asset('images/fav_unset.svg'))}}" >
                        </td>
                        {{--
                        <td  class=" text-center p-2">
                            <div class="btn btn-primary w-100" data-toggle="modal" data-target="#StopModal" onclick=" jQuery('#idstop').val('{{$aufgabe['id']}}'); jQuery('#stopjobname').html('{{$aufgabe->jobbezeichnung}}');   return false;">Stop</div>
                        </td>
                        --}}
                    </tr>
                    <?PHP $job_id = $aufgabe->j_id; ?>
                    @endif
                @endforeach
                </tbody>
            </table>

        </div>
        <div style="height: 40px;"></div>

    </div>
</div>
</div>
    <script>

        $(document).ready(function () {
            $('#dashboardtable').DataTable({
                "order": [[ 6, "desc" ], [ 5, "desc" ]],
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Keine aktuellen Projekte vorhanden."
                }
            });
        });


        function klappandtoogle(elementid) {


            if (!$('#pfeil' + elementid).hasClass('rotate')) {


                $('#pfeil' + elementid).toggleClass('rotate');
                $('#content' + elementid).slideToggle();
            } else {
                $('#pfeil' + elementid).toggleClass('rotate-reset');
                $('#content' + elementid).slideToggle();
            }

        }

        // popovers initialization - on hover
        $('[data-toggle="popover"]').popover({
            html: true,
            trigger: 'click',
            placement: 'left',
            container: 'body',
            content: function () { return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />'; }
        });



        $(document).on("click", ".popover .closer" , function(){
            $(this).parents(".popover").popover('hide');
        });

    </script>



@endsection
