@extends('layouts.app')
@section('content')
    <?PHP
    $load_dienstleister_name = '';
    $load_dienstleister_vorname = '';
    $load_dienstleister_mail = '';
    $load_dienstleister_id = '';
    $load_dienstleister_anrede = '';

    $load_agentur_name = '';
    $load_agentur_vorname = '';
    $load_agentur_mail = '';
    $load_agentur_id = '';
    $load_agentur_anrede = '';

    $load_kunde_name = '';
    $load_kunde_vorname = '';
    $load_kunde_mail = '';
    $load_kunde_id = '';
    $load_kunde_anrede = '';
    $load_agentur_user = null;
    $load_kunde_user = null;
    $load_dienstleister_user = null;




    ?>
    <div class="stammdaten col-12">
        <div class="hinweismodal" style="display: none;">
            <div onclick="jQuery('.absoften').hide();jQuery('.hinweismodal').hide();"
                 class="close"><img src="{{asset('images/cross.svg')}}"></div>
            Hinweis
            <div class="line"></div>


            <div class="hinweistext">
                <h2>Benutzer wirklich löschen?</h2>
                Sie möchten einen Benutzer löschen, diese Aktion ist nicht umgekehrbar.
            </div>
            <div class="clear"></div>
            <form id="del_form" method="post">
                {{csrf_field()}}
                <input type="hidden" value="" name="del_id" id="del_id">
                <button
                    onclick="jQuery('.absoften').hide();jQuery('.hinweismodal').hide();document.getElementById('del_form').submit()"
                    name="loeschen" id="loeschen">
                    Benutzer löschen
                </button>

            </form>
            <button onclick="jQuery('.absoften').hide();jQuery('.hinweismodal').hide();" id="abbrechen">Abbrechen
            </button>

        </div>

        <div class="titel">
            <h1>Benutzer erstellen/bearbeiten</h1>
        </div>
        @if(!empty(session('error')))
            <div class="error">
                <?PHP echo session('error'); ?>
            </div>
        @endif
        @if(!empty(session('succsess')) && empty(session('error_message')))
            <div class="succsess">
                <?PHP echo session('succsess'); ?>
            </div>
        @endif
        <?PHP session()->forget('error'); ?>
        @if(!empty(session('error_message')))
            <div class="error">
                {{session('error_message')}}
            </div>
            <?PHP session()->forget('error_message'); ?>
        @endif
        <div class="agentur">
            <div class="stammdatencontent col-12" id="agenturstamdaten"
                 @if($aktiv['agentur'] != 0)style="display: block;" @endif>
                <h3>team go direct Dialogmarketing GmbH</h3>
                <select id="auserselect" onchange="location.href= '{{url('/stammdaten')}}?typ=agentur&id=' + $('#auserselect option:selected').val();" class="col-12 col-md-6 form-control m-1">
                    <option value=''>Ansprechpartner auswählen</option>
                    @foreach($userinfos['agentur'] AS $user)
                        <option value="{{$user['id']}}">{{$user['vorname']}} {{$user['name']}}</option>
                    @endforeach

                </select>
                <span class="adduser"
                      onclick="location.href='{{url('/stammdaten/?typ=agentur')}}'"><img src="{{asset('images/icons/hinzufuegen_black.svg')}}"></span>
                @foreach( $userinfos['agentur'] AS $user)
                    <?PHP

                    if ($user['id'] == $selected) {
                        if (empty(session('succsess'))) {
                            $load_agentur_user = $user['id'];
                            $style = 'active';

                            $load_agentur_name = $user['name'];
                            $load_agentur_anrede = $user['anrede'];
                            $load_agentur_vorname = $user['vorname'];
                            $load_agentur_mail = $user['email'];
                            $load_agentur_id = $user['id'];
                        }

                    } else {
                        $style = '';

                    }

                    ?>
                @endforeach


                <div class="clear"></div>
                <div class="formular" id="neuermitarbeiter">

                    <form method="post" id="agenturform">
                        {{ csrf_field() }}

                        <div class="form-group">
                           <div class="form-check float-left">

                            <input type="radio" class="form-check-input" name="geschlecht" value="Frau"
                                   id="frau" {{($load_agentur_anrede == 'Frau' || old('geschlecht') == 'Frau') ? 'checked=checked' :''}}/>
                            <label for="frau">Frau&nbsp;&nbsp;
                            </label>

                        </div>
                        <div class=" form-check float-left">
                            <input type="radio" class="form-check-input" name="geschlecht"
                                   value="Herr" {{($load_agentur_anrede == 'Herr' || old('geschlecht') == 'Herr') ? 'checked=checked' :''}} />
                            <label for="herr">Herr</label>
                        </div>

                        <div class="clearfix"></div>
                        <div class="d-felx formularzeile col p-0">
                            <input type="hidden" name="id" id="auser_id" value="{{$load_agentur_id}}"/>
                            <input type="hidden" name="typ" value="agentur"/>
                            <input type="hidden" name="id_agentur" value="1"/>

                            <div class=" col-12 col-md-3 flex-row float-left  p-1 m-1 form-control">
                                <input type="text" name="vorname" placeholder="Vorname" class="col"
                                       value="@if(empty(old('vorname'))){{$load_agentur_vorname}}@else{{old('vorname')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3  flex-row float-left  p-1 m-1  form-control">
                                <input type="text" name="name" placeholder="Nachname" class="col"
                                       value="@if(empty(old('name'))){{$load_agentur_name}}@else{{old('name')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3  flex-row float-left  p-1 m-1  form-control">
                                <input type="text" name="email" id="email" class="col" placeholder="E-Mail"
                                       value="@if(empty(old('email'))){{$load_agentur_mail}}@else{{old('email')}}@endif"/>
                            </div>

                            <div class="  flex-row  col-12 col-md-2  float-left  p-1">
                                @if(session('userdata')['superuser_user'] == 1 && !empty($load_agentur_user))
                                    <button class="transbutton"
                                            onclick="jQuery('#del_id').val(jQuery('#auser_id').val());jQuery('.absoften').show();jQuery('.hinweismodal').show(); return false;"
                                            name="loeschen"><img src="{{asset('images/eimer.svg')}}"></button>
                                @endif
                                @if(empty($load_agentur_user))
                                    <button  class="btn btn-primary">Benutzer erstellen</button>
                                @else
                                    <button class="btn btn-primary">speichern</button>
                                @endif
                            </div>
                            <div class="clearfix"></div>

                        </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="kunde">
            <div class="stammdatencontent" id='kundestammdaten' @if($aktiv['kunde'] != 0)style="display: block;" @endif>
                <h3>Kunde</h3>
                {{--
                 <small>HINWEIS: Neue Kunden können nur von Anja und Thomas angelegt werden.</small>
                 --}}

                <div class="clear"></div>
                <div class="formular">
                    <form method="post" id="kuserform">
                        {{ csrf_field() }}
                        <select name="id_kunde" onchange="return_kuser_stamm(this.value, 'kuserselect')"
                                id="id_kunde" class="col-12 col-md-6 form-control m-1">
                            <option value="">Kunde auswählen</option>
                            @foreach($kunden AS $kunde)
                                <option
                                    value="{{$kunde->id}}" {{(($kunde->id == $aktiv['kunde'] || $kunde->id == old('id_kunde')   )? 'selected=selected' : '')}} >{{$kunde->kunde}}</option>
                            @endforeach
                            <?PHP session()->forget('id_kunde'); ?>
                        </select>
                        <div>
                            <select id="kuserselect" class="col-12 col-md-6 form-control m-1"
                                    onchange="location.href= '{{url('/stammdaten')}}?typ=kunde&id=' + $('#kuserselect option:selected').val() +'&id_kunde=' + $('#id_kunde').val();">
                                <option value=''>Mitarbeiter auswählen</option>
                            </select>
                            <span class="adduser"
                                  onclick="location.href='{{url('/stammdaten/?typ=kunde')}}'"><img src="{{asset('images/icons/hinzufuegen_black.svg')}}"></span>
                        </div>
                        <br>

                        <div class="allusers" id="kuser">
                            <div>
                                <ul style="{{(!empty($aktiv['kunde'])?'':'display: none;')}}">
                                    @if(!empty($userinfos['kunde']))
                                        @foreach( $userinfos['kunde'] AS $user)
                                            <?PHP
                                            if ($user['id'] == $selected) {
                                                if (empty(session('succsess'))) {
                                                    $style = 'active';

                                                    $load_kunde_user = $user['id'];
                                                    $load_kunde_name = $user['name'];
                                                    $load_kunde_anrede = $user['anrede'];
                                                    $load_kunde_vorname = $user['vorname'];
                                                    $load_kunde_mail = $user['email'];
                                                    $load_kunde_id = $user['id'];
                                                }


                                            } else {
                                                $style = '';

                                            }
                                            ?>
                                        @endforeach
                                    @endif

                                </ul>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div class="forminhalt" id="neuerkunde">
                            <div class=" form-check float-left">
                                <input type="radio" name="geschlecht" class="form-check-input" value="Frau"
                                       id="frau" {{($load_kunde_anrede == 'Frau' || old('geschlecht') == 'Frau') ? 'checked=checked' :''}} />
                                <label for="frau">Frau&nbsp;&nbsp;</label>
                            </div>
                            <div class=" form-check float-left">
                                <input type="radio" name="geschlecht" class="form-check-input" value="Herr"
                                       id="herr" {{($load_kunde_anrede == 'Herr'  || old('geschlecht') == 'Herr') ? 'checked=checked' :''}} />
                                <label for="herr">Herr</label>
                            </div>

                            <div class="clearfix"></div>
                            <input type="hidden" name="id" id="kuser_id" value="@if(empty(old('id'))){{$load_kunde_id}}@else{{old('id')}}@endif"/>
                            <input type="hidden" name="typ" value="kunde"/>
                            <div class=" col-12 col-md-3 float-left  p-1 m-1  form-control">
                                <input type="text" name="vorname" placeholder="Vorname" class="col"
                                       value="@if(empty(old('vorname'))){{$load_kunde_vorname}}@else{{old('vorname')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3 float-left  p-1 m-1  form-control">
                                <input type="text" name="name" placeholder="Nachname" class="col"
                                       value="@if(empty(old('name'))){{$load_kunde_name}}@else{{old('name')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3 float-left  p-1 m-1  form-control">
                                <input type="text" name="email" placeholder="E-Mail" class="col"
                                       value="@if(empty(old('email'))){{$load_kunde_mail}}@else{{old('email')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-2 float-left  p-1">
                                @if(session('userdata')['superuser_user'] == 1 && !empty($load_kunde_user))
                                    <button class="transbutton"
                                            onclick="jQuery('#del_id').val(jQuery('#kuser_id').val());jQuery('.absoften').show();jQuery('.hinweismodal').show(); return false;"
                                            name="loeschen"><img src="{{asset('images/eimer.svg')}}"></button>
                                @endif
                                @if(empty($load_kunde_user))
                                    <button  class="btn btn-primary"
                                        onclick="if($('#id_kunde option:selected').val() == '')  {alert ('Bitte Kunden wählen.');return false;}">
                                        Benutzer erstellen
                                    </button>
                                @else
                                    <button  class="btn btn-primary">speichern</button>
                                @endif
                            </div>
                            <div class="clearfix"></div>
                    </form>
                </div>
            </div>
        </div>
        <div class="dienstleister">
            <div class="stammdatencontent" id="dienstleisterstammdaten"
                 @if($aktiv['dienstleister'] != 0)style="display: block;" @endif>
                <h3>Dienstleister</h3>
                {{--
<small>HINWEIS: Neue Dienstleister können nur von Anja und Thomas angelegt werden.</small>
--}}
                <div class="clear"></div>
                <div class="formular">
                    <form method="post">
                        <div class="form-group">


                        {{ csrf_field() }}

                        <select onchange="return_duser_stamm(this.value, 'duserselect')"
                                id="id_dienstleister" name="id_dienstleister" class="col-12 col-md-6 form-control m-1">
                            <option value="">Dienstleister auswählen</option>
                            @foreach($dienstleister AS $dleister)

                                <option
                                    value="{{$dleister->id}}" {{(($dleister->id == $aktiv['dienstleister'] || $dleister->id == old('id_dienstleister')  )? 'selected=selected' : '')}} >{{$dleister->dienstleister}}</option>

                            @endforeach
                            <?PHP session()->forget('id_dienstleister'); ?>
                        </select>
                        <div>
                            <select id="duserselect" class="col-12 col-md-6 form-control m-1"
                                    onchange="location.href= '{{url('/stammdaten')}}?typ=dienstleister&id=' + $('#duserselect option:selected').val()+'&id_dienstleister=' + $('#id_dienstleister').val();">
                                <option value=''>Mitarbeiter auswählen</option>
                            </select>
                            <span class="adduser"
                                  onclick="location.href='{{url('/stammdaten/?typ=dienstleister')}}'"><img src="{{asset('images/icons/hinzufuegen_black.svg')}}"></span>
                        </div>
                        <br>

                        <div class="allusers" id="duser">
                            <ul style="{{(!empty($aktiv['dienstleister'])?'':'display: none;')}}">
                                @if(!empty($userinfos['dienstleister']))
                                    @foreach( $userinfos['dienstleister'] AS $user)
                                        <?PHP
                                        if ($user['id'] == $selected) {
                                            if (empty(session('succsess'))) {
                                                $style = 'active';
                                                $load_dienstleister_user = $user['id'];
                                                $load_dienstleister_name = $user['name'];
                                                $load_dienstleister_anrede = $user['anrede'];
                                                $load_dienstleister_vorname = $user['vorname'];
                                                $load_dienstleister_mail = $user['email'];
                                                $load_dienstleister_id = $user['id'];
                                                $load_dienstleister_aktiv = $user['aktiv'];
                                            }


                                        } else {
                                            $style = '';

                                        }
                                        ?>

                                    @endforeach
                                @endif
                                <?PHP session()->forget('succsess'); ?>
                            </ul>
                        </div>
                        <div id="neuerdienstleister">
                            <div class=" form-check float-left">

                                <input type="radio" name="geschlecht" value="Frau" class="form-check-input"  id="frau" {{($load_dienstleister_anrede == 'Frau'  || old('geschlecht') == 'Frau') ? 'checked=checked' :''}} />
                                <label for="frau">Frau&nbsp;&nbsp;</label>
                            </div>
                            <div class=" form-check float-left">
                                <input type="radio" name="geschlecht" id="herr" class="form-check-input" value="Herr" {{($load_dienstleister_anrede == 'Herr' || old('geschlecht') == 'Herr') ? 'checked=checked' :''}}/>
                                <label for="herr">Herr</label>
                            </div>
                            <div class="clearfix"></div>
                            <input type="hidden" name="id" id="duser_id" value="@if(empty(old('id'))){{$load_dienstleister_id}}@else{{old('id')}}@endif"/>
                            <input type="hidden" name="typ" value="dienstleister"/>
                            <div class=" col-12 col-md-3 float-left p-1 m-1  form-control">
                                <input type="text" name="vorname" placeholder="Vorname" class="col"
                                       value="@if(empty(old('vorname'))){{$load_dienstleister_vorname}}@else{{old('vorname')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3 float-left  p-1 m-1  form-control">
                                <input type="text" name="name" placeholder="Nachname" class="col"
                                       value="@if(empty(old('name'))){{$load_dienstleister_name}}@else{{old('name')}}@endif"/>
                            </div>
                            <div class=" col-12 col-md-3 float-left p-1 m-1  form-control">
                                <input type="text" name="email" placeholder="E-Mail"  class="col"
                                       value="@if(empty(old('email'))){{$load_dienstleister_mail}}@else{{old('email')}}@endif"/>
                            </div>

                            <div class=" col-12 col-md-2  float-left  p-1">
                                @if(session('userdata')['superuser_user'] == 1 && !empty($load_dienstleister_user))
                                    <button class="transbutton"
                                            onclick="jQuery('#del_id').val(jQuery('#duser_id').val());jQuery('.absoften').show();jQuery('.hinweismodal').show(); return false;"
                                            name="loeschen"><img src="{{asset('images/eimer.svg')}}"></button>
                                @endif
                                @if(empty($load_dienstleister_user))
                                    <button  class="btn btn-primary"
                                        onclick="if($('#id_dienstleister option:selected').val() == '')  {alert('Bitte Dienstleister wählen.');return false;}">
                                        Benutzer erstellen
                                    </button>
                                @else
                                    <button class="btn btn-primary">
                                        speichern
                                    </button>
                                @endif
                            </div>
                            <div class="clearfix"></div>
                        </div>

                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
    <script>


        @if( (!empty($_GET['typ']) && $_GET['typ'] == 'kunde'))
        $('#neuerkunde').show();
        return_kuser_stamm($('#id_kunde option:selected').val(), 'kuserselect');
        @else
        $('#neuerkunde').hide();
        @endif
        @if((!empty($_GET['typ']) && $_GET['typ'] == 'agentur'))
        $('#neuermitarbeiter').show();

        @else
        $('#neuermitarbeiter').hide();
        @endif


        @if((!empty($_GET['typ']) && $_GET['typ'] == 'dienstleister'))
        $('#neuerdienstleister').show();
        return_duser_stamm($('#id_dienstleister option:selected').val(), 'duserselect');
        @else
        $('#neuerdienstleister').hide();
        @endif





        $(document).ready(function () {
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });
        });


    </script>
    <?PHP  session()->forget('stammdaten');  ?>
@endsection
