@extends('layouts.app')
@section('content')
    <div class="jobanlage col-12">
        <div id="DeleteModal" class="modal fade col">
            <div class="modal-dialog  modal-dialog-centered " role="document">
                <div class="modal-content ">
                    <div class="modal-body text-center freigabe-padding">
                        <h1>Soll der Bestandteil wirklich vom Projekt entfernt werden?</h1>
                        <div class=" ">
                            <input type="hidden" id="del_id_dokument" name="del_id_dokument"
                                   value="">
                            <input type="hidden" id="remove_id_dokument" name="remove_id_dokument"
                                   value="">
                        </div>
                        <div class="clearfix"></div>
                        <div class=" mt-4 justify-content-center ">
                            <button
                                    onclick="$('body').removeClass('modal-open');$('.modal-backdrop').remove();$('#DeleteModal').hide();return false;"
                                    class="btn  btn-zurueck">zurück
                            </button>
                            &nbsp;
                            &nbsp;
                            <button
                                    onclick="$('body').removeClass('modal-open');$('.modal-backdrop').remove(); $('#dok_' + $('#del_id_dokument').val()).remove();delete_bestandteil($('#del_id_dokument').val(), $('#remove_id_dokument').val());$('#DeleteModal').hide();"
                                    class="btn bnt-best">bestätigen
                            </button>
                        </div>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->
        <div id="DeleteKosten" class="modal fade col">
            <form method="post" name="delkosten" action="{{url('/delverarbeitung')}}">
                {{csrf_token()}}
                <input type="hidden" name="id_job" value="{{(!empty($jobdetails->id)? $jobdetails->id : '')}}">
                <div class="modal-dialog  modal-dialog-centered " role="document">
                    <div class="modal-content ">
                        <div class="modal-body text-center freigabe-padding">
                            <h1>Soll die Auflage wirklich vom Projekt entfernt werden?</h1>
                            <div class=" ">
                                <input type="hidden" id="del_idkosten" name="del_idkosten"
                                       value="">
                            </div>
                            <div class="clearfix"></div>
                            <div class=" mt-4 justify-content-center ">
                                <button
                                        onclick="$('body').removeClass('modal-open');$('.modal-backdrop').remove();$('#DeleteKosten').hide();return false;"
                                        class="btn  btn-zurueck">zurück
                                </button>
                                &nbsp;
                                &nbsp;
                                <button
                                        onclick=""
                                        class="btn bnt-best">bestätigen
                                </button>
                            </div>
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </form>
        </div><!-- /.modal -->
        <form method="post" enctype="multipart/form-data">
            <ul id="del_bestandteile">

            </ul>
            <!-- Modal HTML Markup -->
            <div id="ModalLoginForm" class="modal fade">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Hinweis</h4>
                        </div>
                        <div class="modal-body">
                            <h2>Intervall Einstellungen</h2>
                            <input type="hidden" name="intervalltyp" id="intervalltyp"
                                   value="@if(!empty($autojob->intervall)){{$autojob->intervall}}@endif">
                            @csrf
                            <div class="form-group">
                                <label class="control-label">Wiederholungen</label>
                                <div>
                                    <select id="wiederholungen" onchange="" class="form-control col-12 ">
                                        <option value=''>Intervall wählen</option>
                                        @if(!empty($wiederholungen))
                                            @foreach($wiederholungen AS $wkey => $wiederholung)
                                                <option value="{{$wkey}}" {{(($wkey == (!empty($autojob->intervall) ? $autojob->intervall : '')) ? 'selected=selected' : '')}}>{{$wiederholung}}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div id="DAY"
                                 style="{{(((!empty($autojob->intervall) ? $autojob->intervall : '') == 'DAY') ? 'display: block;' : 'display: none;')}}">
                                <div class="form-group p-0">
                                    <div class="float-left m-0 p-0 pr-1">
                                        <div>
                                            <label class="switch mt-2">
                                                <input name='ohne_we' id='ohne_we'
                                                       {{((!empty($autojob->nicht_an_we_fe) && $autojob->nicht_an_we_fe == 1))?'checked=checked':''}} type='checkbox'>
                                                <span class="slider round"></span>
                                            </label> Wochenende/Feiertage ausschließen
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="WEEK"
                                 style="{{(((!empty($autojob->intervall) ? $autojob->intervall : '') == 'WEEK') ? 'display: block;' : 'display: none;')}}">
                                <div class="form-group p-0">
                                    <div class=" float-left m-0 p-0 pr-1">
                                        <div class="btn-group-toggle" data-toggle="buttons">
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="mo" id="mo"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '1') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="1">
                                                Mo
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="di" id="di"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '2') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="2">
                                                Di
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="mi" id="mi"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '3') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="3">
                                                Mi
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="do" id="do"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '4') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="4">
                                                Do
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="fr" id="fr"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '5') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="5">
                                                Fr
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="sa" id="sa"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '6') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="6">
                                                Sa
                                            </label>
                                            <label class="btn btn-sm btn-day">
                                                <input type="checkbox" name="so" id="so"
                                                       {{(!empty($autojob->wochentag) && strpos($autojob->wochentag, '7') !== false ? 'checked=checked':'')}} autocomplete="off"
                                                       value="7">
                                                So
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="MONTH"
                                 style="{{(((!empty($autojob->intervall) ? $autojob->intervall : '') == 'MONTH') ? 'display: block;' : 'display: none;')}}">
                                <div class="form-group p-0">
                                    <div class="col-md-6 float-left m-0 p-0 pr-1">
                                        <label class="control-label">Beginn Zeitraum</label>
                                        <div>
                                            <input type="text" name="start_intervall" id="start_intervall"
                                                   placeholder="Tag auswählen"
                                                   class="form-control"
                                                   value="{{(!empty($autojob->start_intervall) ? $autojob->start_intervall :  old('start_intervall')) }}"/>
                                            <div class="input-group-addon">
                                                <span class="glyphicon glyphicon-th"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6  m-0 float-left p-0">
                                        <label class="control-label">Bis spätestens</label>
                                        <div>
                                            <input type="text" name="end_intervall" id="end_intervall"
                                                   placeholder="Tag auswählen"
                                                   class="form-control"
                                                   value="{{(!empty($autojob->end_intervall) ? $autojob->end_intervall :  old('end_intervall')) }}"/>
                                            <div class="input-group-addon">
                                                <span class="glyphicon glyphicon-th"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="clearfix"></div>
                            <div class="form-group mt-3">
                                <div>
                                    <button id="festlegen" class="btn btn-primary float-right"
                                            onclick="$('#ModalLoginForm').modal('hide'); return false;">Festlegen
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->

            <button type="submit" class="btn btn-primary float-right sticky-top ">Projekt speichern</button>
            @if(!empty($jobdetails->id))
                <button onclick="confirmRedirect('{{ url('duplicate/'.$jobdetails->id)}}');return false;"
                        class="btn btn-secondary float-right sticky-top mr-2">Projekt
                    kopieren
                </button>
            @endif
            @if(empty($jobdetails->id))
                <h1>Neues Projekt erstellen</h1>
            @else
                <h1>Projekt bearbeiten</h1>
                @if(!empty($jobdetails->copiedFrom))
                    <small>{{$jobdetails->copiedFrom}}</small>
                @endif
            @endif
            @if(!empty(session('error')))
                <div class="error">{{session('error')}}</div>
            @endif
            <?PHP
            session()->forget('error');
            ?>
            <div class="clear"></div>
            {{csrf_field()}}
            <div class="projekthead">
                <div class="form-group col-12 col-md-6 m-0 p-0 float-left">
                    <div class="mr-md-1">
                        ProjektID<br>
                        <input class="form-control col-12" placeholder="wird generiert"
                               value="{{(!empty($jobdetails->id) ? '#'.$jobdetails->id : '' )}}" readonly/>
                        <input type="hidden" name="id_job" id="id_job"
                               value="{{(!empty($jobdetails->id) ? $jobdetails->id : '' )}}"/>
                    </div>
                </div>
                <div class="form-group col-12 col-md-6 m-0 p-0 float-left">
                    Projektname<br>
                    <input name="jobbezeichnung" id="jobbezeichnung" class="form-control col-12 m-0"
                           placeholder="Jobbezeichnung"
                           value="{{(!empty($jobdetails->jobbezeichnung) ? $jobdetails->jobbezeichnung :  old('jobbezeichnung')) }}" {{$disable_fields}}/>
                </div>
                <div class="clear"></div>
                <div class="col-12 col-md-6  p-0 float-left">
                    <div class="mr-md-1">
                        Vorschaubild hochladen
                        <div class="input-group">
                            <div class="custom-file">
                                <input type="file" name="preview" class="custom-file-input" id="customFileLang"
                                       onchange="$('#filename').html($('#customFileLang').val().substr($('#customFileLang').val().lastIndexOf('\\')+1))"
                                       {{!empty($disable_fields) ? 'disabled' : ''}} lang="de">
                                <label class="custom-file-label text-left" id="filename"
                                       for="customFileLang">{{(!empty($jobdetails->preview_name) ? $jobdetails->preview_name :  'Datei...') }}</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 p-0 float-left ">
                    Geplante Adressmenge
                    <div class="input-group">
                        <input type="text" name="gesamtmenge"
                               placeholder="Bitte tragen Sie die geplante Adressmenge ein"
                               class="form-control"
                               value="{{(!empty($jobdetails->gesamtmenge) ? $jobdetails->gesamtmenge :  old('gesamtmenge')) }}" {{$disable_fields}}/>
                    </div>
                </div>
                <div class="clear"></div>
                <div class="form-group col-12 col-md-6  p-0 float-left">
                    <div class="mr-md-1">
                        {{config('app.CUSTOMER_CRM')}} - Preview:
                        <div class="mr-md-1">
                            @if(!empty($jobdetails->id))
                                <a href="{{ url('/showsfpreview/' . $jobdetails->id)}}"
                                   target="_blank">{{(!empty($jobdetails->sf_preview_name) ? $jobdetails->sf_preview_name :  '') }}</a>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>

            <div class="verarbeitung whitebg mt-3 p-2">
                <h3>Adressen / Verarbeitung / Kosten @if(!empty($jobdetails->id))
                        <i class="las la-plus-square" onclick="add_va();"></i>
                    @endif</h3>

                <div class="clearfix">
                    <div id="va_div">

                    </div>
                </div>

                <ul class="col-md-6 p-0 ">
                    <li class="col verarbeitung_head p-0">
                        <ul class="d-flex col p-0 ">
                            <li class="col-4 ">Auflage</li>
                            <li class="col-4 text-center">Verarbeitungszeit<br/>(Adressen/PAL)</li>
                            <li class="col-2  text-center">Kosten (Stück)</li>
                            <li class="col-2 text-center">Aktion</li>
                        </ul>
                    </li>
                    @if(!empty($jobdetails->id) &&!empty($verarbeitungen = \App\Http\Controllers\JobController::getverarbeitung($jobdetails->id)))
                        @foreach($verarbeitungen AS $verarbeitung)
                            <input name="del_va[]" type="hidden" id="del_va{{$verarbeitung->id}}" value="">
                            <li class="col p-0">
                                <ul class="d-flex col p-0" id="va_{{$verarbeitung->id}}">
                                    <li class="col-4">{{$verarbeitung->auflage}}</li>
                                    <li class="col-4 text-center">{{$verarbeitung->zeit}}</li>
                                    <li class="col-2 text-right">{{number_format($verarbeitung->kosten/100, 2, ',', '')}}</li>
                                    <li class="col-2 text-center"><i class="las la-trash black"
                                                                     onclick="delete_auflagen('{{$verarbeitung->id}}');"></i>
                                    </li>
                                </ul>
                            </li>
                        @endforeach
                    @endif


                </ul>
            </div>
            {{--


            <h1>Template</h1>
            <select>
                <option>Bitte Template wählen</option>
            </select>
            <div class="clear"></div>
            --}}
            <div class="projektagentur">
                <div class="agentur">
                    <div class="titel">
                        <h3>Agentur</h3>
                    </div>
                    <ul class="users  p-0 ">
                        <div class="form-group col m-0 p-0">
                            <select id="auserselect" onchange="return_auser_bt(this.value);"
                                    class="form-control col-12 col-md-6 float-left mr-1 mb-1">
                                <option value=''>Ansprechpartner auswählen</option>
                                @foreach($agenturuser AS $agentur)
                                    <option value="{{$agentur->id}}">{{$agentur->vorname}} {{$agentur->name}}</option>
                                @endforeach

                            </select>
                            <button class="form-control col-6 " id="a_button"
                                    onclick="return_auser_ausgabe($('#auserselect option:selected').val(), 'ausers', $('#auserselect option:selected').val(), $('#auserselect option:selected').text());$('#auserselect').val('').change(); return false;">
                                Hinzufügen
                            </button>
                        </div>
                        <div class="clearfix"></div>
                        <ul id="ausers" class="p-0">
                            @if(!empty($jobdetails->agentur ))
                                    <?PHP $count_afreigaben = 0; ?>
                                @foreach($jobdetails->agentur AS $a_user)
                                    <li class="list-group p-0">
                                        <input name='{{$a_user->id}}_anlegen' type='hidden' value='1'>
                                        <ul class="d-flex flex-row  p-0">

                                            <li class=" col-2 active p-2 m-1">{{$a_user->vorname}} {{$a_user->name}}</li>
                                            <li class="p-2">
                                                Ansehen
                                                <label class="switch" data-toggle="tooltip" data-placement="top"
                                                       title="Das Ansehen des Jobs und der Daten (PDF/CSV) ermöglichen">
                                                    <input name='{{$a_user->id}}_ansehen'
                                                           {{(isset($agenturinfos[$a_user->id]->ansehen)&&($agenturinfos[$a_user->id]->ansehen == 1)?'checked=checked':'')}} {{(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>
                                                    <span class="slider round"></span>
                                                </label>
                                                @if(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1))
                                                    <input name='{{$a_user->id}}_ansehen' type="hidden"
                                                           value="{{$agenturinfos[$a_user->id]->ansehen}}">
                                                @endif
                                            </li>
                                            {{--

                                            <li class="p-2">
                                                Upload
                                                <label class="switch">

                                                    <input name='{{$a_user->id}}_upload'
                                                           {{(isset($agenturinfos[$a_user->id]->upload)&&($agenturinfos[$a_user->id]->upload == 1)?'checked=checked':'')}}  {{(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1)?'disabled=disabled':'')}}type='checkbox'>
                                                    <span class="slider round"></span>
                                                </label>
                                                @if(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1))
                                                    <input name='{{$a_user->id}}_upload' type="hidden"
                                                           value="{{$agenturinfos[$a_user->id]->upload}}"> @endif
                                            </li>
                                            --}}
                                            <li class="p-2">
                                                Freigabe
                                                <label class="switch" data-toggle="tooltip" data-placement="top"
                                                       title="Freigabe von einzelnen Dokumenten ermöglichen">

                                                    <input name='{{$a_user->id}}_freigabe'
                                                           {{(isset($agenturinfos[$a_user->id]->freigabe)&&($agenturinfos[$a_user->id]->freigabe == 1)?'checked=checked':'')}}  {{(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>
                                                    <span class="slider round"></span>
                                                </label>
                                                @if(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1))
                                                    <input name='{{$a_user->id}}_freigabe' type="hidden"
                                                           value="{{$agenturinfos[$a_user->id]->freigabe}}">
                                                @endif
                                                    <?PHP if ($agenturinfos[$a_user->id]->freigabe) $count_afreigaben++; ?>
                                            </li>
                                            <li class="p-2">
                                                Editor
                                                <label class="switch" data-toggle="tooltip" data-placement="top"
                                                       title="Nutzung des Editors ermöglichen">

                                                    <input name='{{$a_user->id}}_editor'
                                                           {{(isset($agenturinfos[$a_user->id]->editor)&&($agenturinfos[$a_user->id]->editor == 1)?'checked=checked':'')}}  {{(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>
                                                    <span class="slider round"></span>
                                                </label>
                                                @if(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1))
                                                    <input name='{{$a_user->id}}_freigabe' type="hidden"
                                                           value="{{$agenturinfos[$a_user->id]->freigabe}}">
                                                @endif
                                                    <?PHP if ($agenturinfos[$a_user->id]->freigabe) $count_afreigaben++; ?>
                                            </li>
                                            <li class="p-2">
                                                Benachrichtigung
                                                <label class="switch" data-toggle="tooltip" data-placement="top"
                                                       title="Benachrichtigung über jegliche Veränderungen erhaten. Ansonsten nur Anlage und Freigabeanforderungen.">

                                                    <input name='{{$a_user->id}}_benachrichtigung'
                                                           {{(isset($agenturinfos[$a_user->id]->benachrichtigung)&&($agenturinfos[$a_user->id]->benachrichtigung == 1)?'checked=checked':'')}} {{(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'><span
                                                            class="slider round"></span>
                                                </label>
                                                @if(isset($agenturinfos[$a_user->id]->gesperrt)&&($agenturinfos[$a_user->id]->gesperrt == 1))
                                                    <input name='{{$a_user->id}}_benachrichtigung' type="hidden"
                                                           value="{{$agenturinfos[$a_user->id]->benachrichtigung}}">
                                                @endif
                                            </li>
                                            @if($a_user->id != session('userdata')['id'])
                                                <li class="p-2">
                                                    Sperre
                                                    <label class="switch">

                                                        <input name='{{$a_user->id}}_gesperrt'
                                                               {{((isset($agenturinfos[$a_user->id]->gesperrt) && ($agenturinfos[$a_user->id]->gesperrt == 1) )?'checked=checked':'')}}  type='checkbox'><span
                                                                class="slider_red round"></span>
                                                    </label>
                                                </li>

                                            @endif

                                        </ul>

                                    </li>
                                    <div style="clear:both;"></div>
                                @endforeach
                                @if($count_afreigaben < 2)
                                    <div class="error">Es sind nicht genügend User mit Freigaberechten angelegt
                                        (mindestens 2)!
                                    </div>
                                @endif
                            @endif
                        </ul>
                    </ul>
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="projektkundencontainer">
                <div class="kunde ">
                    <div class="titel">
                        <h3>Kunde</h3>
                    </div>
                    <div class="form-group col m-0 p-0">
                        @if(!empty($jobdetails->id_kunde))
                            <input type="hidden" name="id_kunde"
                                   value="{{$jobdetails->id_kunde}}">
                        @endif
                        <select name="id_kunde" id="id_kunde" class="form-control col-12 col-md-6 mb-1"
                                onchange="return_kuser(this.value, 'kuserselect')" {{(!empty($jobdetails->id_kunde)) ? 'disabled=disabled' : ''}}>

                            <option value="">Kunde auswählen</option>

                            @foreach($kunden AS $kunde)
                                @if(!empty($kunde->id))
                                    <option value="{{$kunde->id}}"
                                            {{((($jobdetails->id_kunde == $kunde->id))? 'selected=selected' : '')}}   @if(old('id_kunde') == $kunde->id) selected @endif>{{$kunde->kunde}}</option>
                                @endif
                            @endforeach
                        </select>
                        <div class="users">
                            <div>
                                <select id="kuserselect" onchange="return_kuser_bt(this.value);"
                                        class="form-control col-12 col-md-6 float-left mr-1 mb-1">
                                    <option value=''>Ansprechpartner auswählen</option>


                                </select>
                                <button class="ku_button form-control col-6  m-0"
                                        onclick="return_user_ausgabe($('#kuserselect option:selected').val(), 'kusers', $('#kuserselect option:selected').val(), $('#kuserselect option:selected').text());$('#kuserselect').val('').change(); return false;">
                                    Hinzufügen
                                </button>
                            </div>
                            <div class="clearfix"></div>
                            <ul id="kusers" class="p-0">
                                @if(!empty($jobdetails->id))
                                        <?PHP $count_kfreigaben = 0; ?>
                                    @foreach($jobdetails->kunden AS $kuser)
                                        @if(!empty($kuser) && $kuser->id != 155)
                                            <li class="list-group p-0">
                                                <ul class="d-flex flex-row  p-0">
                                                    <input name='{{$kuser->id}}_anlegen' type='hidden' value='1'>
                                                    <li class="col-2 active p-2 m-1">{{$kuser->vorname}} {{$kuser->name}}</li>
                                                    <li class="p-2">
                                                        Ansehen

                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="Das Ansehen des Jobs und der Daten (PDF/CSV) ermöglichen">
                                                            <input name='{{$kuser->id}}_ansehen'
                                                                   {{(($kundeninfos[$kuser->id]->ansehen == 1)?'checked=checked':'')}}  {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_ansehen' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->ansehen}}">
                                                        @endif
                                                    </li>
                                                    {{--

                                                     <li class="p-2">
                                                         Upload
                                                         <label class="switch">
                                                             <input name='{{$kuser->id}}_upload'
                                                                    {{(($kundeninfos[$kuser->id]->upload == 1)?'checked=checked':'')}}  {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                             <span class="slider round"></span>
                                                         </label>
                                                         @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                             <input name='{{$kuser->id}}_upload' type="hidden"
                                                                    value="{{$kundeninfos[$kuser->id]->upload}}"> @endif
                                                     </li>
                                                      --}}
                                                    <li class="p-2">
                                                        Download
                                                        <label class="switch">
                                                            <input name='{{$kuser->id}}_download'
                                                                   {{(($kundeninfos[$kuser->id]->download == 1)?'checked=checked':'')}}  {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_download' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->download}}">
                                                        @endif
                                                    </li>

                                                    <li class="p-2">
                                                        Freigabe
                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="Freigabe von einzelnen Dokumenten ermöglichen">
                                                            <input name='{{$kuser->id}}_freigabe'
                                                                   {{(($kundeninfos[$kuser->id]->freigabe == 1)?'checked=checked':'')}}   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_freigabe' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->freigabe}}">
                                                        @endif
                                                            <?PHP if ($kundeninfos[$kuser->id]->freigabe) $count_kfreigaben++; ?>
                                                    </li>
                                                    <li class="p-2">
                                                        Fastlane
                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="2. Freigabe durch selben User ermöglichen.">
                                                            <input name='{{$kuser->id}}_fastlane'
                                                                   {{(($kundeninfos[$kuser->id]->fastlane == 1)?'checked=checked':'')}}   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_fastlane' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->fastlane}}">
                                                        @endif
                                                    </li>
                                                    <li class="p-2">
                                                        Editor
                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="Nutzung des Editors ermöglichen">
                                                            <input name='{{$kuser->id}}_editor'
                                                                   {{(($kundeninfos[$kuser->id]->editor == 1)?'checked=checked':'')}}   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_fastlane' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->fastlane}}">
                                                        @endif
                                                    </li>
                                                    <li class="p-2">
                                                        @if((config('app.mandant') != 'DIRECTHUB'))
                                                            MC Freigabe
                                                        @else
                                                            CRM Freigabe
                                                        @endif

                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="@if((config('app.mandant') != 'DIRECTHUB'))
                                                        MC Freigabe
                                                    @else
                                                        CRM Freigabe
                                                    @endif darf erteilt werden.">
                                                            <input name='{{$kuser->id}}_sffreigabe'
                                                                   {{(($kundeninfos[$kuser->id]->sf_freigabe == 1)?'checked=checked':'')}}   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_sffreigabe' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->fastlane}}">
                                                        @endif
                                                    </li>
                                                    <li class="p-2">
                                                        Benachrichtigung
                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="Benachrichtigung über jegliche Veränderungen erhaten. Ansonsten nur Anlage und Freigabeanforderungen.">
                                                            <input name='{{$kuser->id}}_benachrichtigung'
                                                                   {{(($kundeninfos[$kuser->id]->benachrichtigung == 1)?'checked=checked':'')}}   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>
                                                            <span class="slider round"></span>
                                                        </label>
                                                        @if(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1))
                                                            <input name='{{$kuser->id}}_benachrichtigung' type="hidden"
                                                                   value="{{$kundeninfos[$kuser->id]->benachrichtigung}}">
                                                        @endif
                                                    </li>
                                                    <li class="p-2">
                                                        Sperre
                                                        <label class="switch" data-toggle="tooltip" data-placement="top"
                                                               title="User hat keinen Zugriff mehr auf das Projekt, kann sich aber noch im TS4SF anmelden.">
                                                            <input name='{{$kuser->id}}_gesperrt'
                                                                   {{(isset($kundeninfos[$kuser->id]->gesperrt)&&($kundeninfos[$kuser->id]->gesperrt == 1)?'checked=checked':'')}}  type='checkbox'><span
                                                                    class="slider_red round"></span>
                                                        </label>
                                                    </li>
                                                </ul>
                                            </li>

                                            <div style="clear:both;"></div>
                                        @endif
                                    @endforeach
                                    @if($count_kfreigaben < 2)
                                        <div class="error">Es sind nicht genügend User mit Freigaberechten angelegt
                                            (mindestens 2)!
                                        </div>
                                    @endif
                                @endif

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
            <div class="projektbestandteile">

                <h3>Bestandteile</h3>
                @if($disable_fields === '')
                    <div class="form-group col m-0 p-0">

                        <div class=" m-0 mb-1" style="width: 50%;">
                            <input type="text" name="filter" id="filter" placeholder="Filter"
                                   class="form-control"
                                   value="" {{$disable_fields}}/>
                        </div>
                        <select id="dokumentenselecttmp" style="display: none;" {{$disable_fields}}>
                            <option value=''>Dokument auswählen</option>
                            @if(!empty($jobdetails->dokumente))
                                @foreach($jobdetails->dokumente AS $document)
                                    <option value='{{$document->id}}'>{{$document->name}}</option>
                                @endforeach
                            @endif
                        </select>
                        <select id="dokumentenselect" class="form-control col-12 col-md-6 float-left mr-1 mb-1"
                                onfocus="filterselect('dokumentenselect', $('#filter').val());" {{$disable_fields}}>
                            <option value=''>Dokument auswählen</option>
                            @if(!empty($jobdetails->dokumente))
                                @foreach($jobdetails->dokumente AS $document)
                                    <option value='{{$document->id}}'
                                            id="opt{{$document->id}}">{{$document->name}}</option>
                                @endforeach
                            @endif
                        </select>

                        <button class="form-control col-6  m-1" id="doc_hinzufuegen"
                                onclick="add_bestandteil($('#dokumentenselect option:selected' ).val() , $( '#dokumentenselect option:selected' ).text()); $('#doc_hinzufuegen').removeClass('bluebutton');  $(this).attr('disabled', true);  return false;"
                                disabled="disabled">
                            Hinzufügen
                        </button>

                    </div>
                @else
                    <div class="border-bottom"></div>
                @endif
                <ul id="kategorielist">
                    <li>
                        <ul id="subkategorielist" class="list-group">

                            @if(!empty($jobdetails->dokumentzuordnung))
                                <li class=" float-left mb-1" id="dok_editor">
                                        <?PHP $sf_preview = json_decode($jobdetails->gui_merge, ARRAY_FILTER_USE_BOTH); ?>
                                    <h3>Editor-Dokument</h3>
                                    <div class="mb-2"></div>
                                    <div class="w-100">
                                        <div class="mr-4 float-left bg-grey d-flex align-items-center justify-content-center" style="width: 104px; height: 143px;">
                                            Editor
                                        </div>
                                    </div>

                                    <h3>Preview:</h3>
                                    <label class="switch">
                                        <input name='0_sfpreview' onclick=""
                                               {{( !empty($sf_preview) &&  in_array('0', $sf_preview ) ? 'checked=checked': '')}}
                                               type='checkbox' {{ !empty($disable_fields) ? 'disabled' : '' }} >

                                        <span class="slider round"></span>
                                    </label>
                                    MC Preview <input type="text" size="2"
                                                      name="0_order"
                                                      value="{{!empty($sf_preview) ? array_search('0', $sf_preview)  :'' }}" {{$disable_fields}}>


                                </li>

                                @foreach($jobdetails->dokumentzuordnung AS $doczuordnung)
                                    <li class=" float-left" id="dok_{{$doczuordnung->id}}"
                                        id_doc="{{$doczuordnung->id_document}}">
                                        @if(!empty($doczuordnung->id_document))
                                                <?PHP
                                                $dokument = (\App\Models\Document::find($doczuordnung->id_document));
                                                $tags = \App\Models\Tag2Dokument::Where('id_dokument', $doczuordnung->id_document)->get();
                                                ?>
                                            <h3>{{$dokument->name}}
                                                <small>({{\Carbon\Carbon::parse($doczuordnung->created_at)->format('d.m.Y H:i')}}
                                                    )</small><i class="las la-trash black" data-toggle=modal
                                                                data-target=#DeleteModal
                                                                onclick="$('#del_id_dokument').val('{{$doczuordnung->id}}');$('#remove_id_dokument').val('{{$doczuordnung->id_document}}');"></i>
                                            </h3>
                                            @if(!empty($tags))
                                                <div class="mb-2">
                                                    @foreach($tags AS $tag)
                                                            <?PHP $tagname = \App\Models\Tags::find($tag->id_tag) ?>
                                                        <span class="bg-dark  text-white p-1 rounded mr-1">{{$tagname->tag}}</span>
                                                    @endforeach
                                                </div>
                                            @endif
                                            <input type="hidden" name="edit_bestand[]" value="{{$doczuordnung->id}}"/>
                                            <div class="w-100">
                                                <div class="float-left" style="width: 130px; ">
                                                    @if(!empty($dokument->id))
                                                        @if(strpos($dokument->name ,'.doc') !== false)
                                                            <div
                                                                    class="anlage_documenticon_text align-items-center ">
                                                                W
                                                            </div>
                                                        @elseif(strpos($dokument->name ,'.xls')  !== false || strpos($dokument->name ,'.csv')  !== false  )
                                                            <div
                                                                    class="anlage_documenticon_tabelle align-items-center ">
                                                                X
                                                            </div>
                                                        @elseif(strpos($dokument->name ,'.pdf') !== false)
                                                            <div
                                                                    class="anlage_documenticon_pdf align-items-center ">
                                                                @if(!empty($dokument->preview_img) && substr(base64_encode($dokument->preview_img), 0,1) != 'J')
                                                                    <img
                                                                            src="data:image/jpg;base64,{{base64_encode($dokument->preview_img)}}"
                                                                            class="w-100" class="preview"
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$dokument->id}}Modal"
                                                                            data-container="body"
                                                                            title="{{$dokument->id}}"/>
                                                                @elseif(!empty($dokument->inhalt))
                                                                    <canvas data="" type="application/pdf"
                                                                            id="preview_{{$dokument->id}}"
                                                                            class="pdf_inhalt"
                                                                            style="overflow: hidden; width: 100%; "
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$dokument->id}}Modal"></canvas>
                                                                    <script>
                                                                        $(document).ready(function () {
                                                                            @if(!empty($dokument->id))
                                                                            getdirdokpreview({{$dokument->id}}, 'preview_{{$dokument->id}}', 1, 1, '../');
                                                                            @else
                                                                            getdirdokpreview({{$dokument->id}}, 'preview_{{$dokument->id}}', 1, 1, './');
                                                                            @endif
                                                                        });
                                                                    </script>
                                                                @else
                                                                    <div
                                                                            class="anlage_documenticon_pdf align-items-center ">
                                                                        <img
                                                                                src="{{asset('images/PDF-Symbol.png')}}">
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        @endif

                                                    @endif
                                                </div>
                                                <div class=" ml-1">
                                                    @if($dokument->typ == 1)
                                                        @if((\App\Http\Controllers\JobController::getanzahl($doczuordnung->id_document, $jobdetails->id) > 0 || $doczuordnung->aus_bestand == 1) )
                                                            Bestand {{\App\Http\Controllers\JobController::getanzahl($doczuordnung->id_document, $jobdetails->id)}}
                                                            Stück
                                                            (@foreach(\App\Http\Controllers\JobController::getBestandProjekte($doczuordnung->id_document) AS $zjob)
                                                                <a href="{{url('neuesprojekt/'.$zjob->id_job)}}"
                                                                   target="_blank"><span
                                                                            class="bluebutton text-white p-1 rounded mr-1">{{$zjob->jobbezeichnung}}</span></a>
                                                            @endforeach)<br/>
                                                        @endif
                                                        <label class="switch">
                                                            <input name='{{$doczuordnung->id}}_bestand'
                                                                   {{(($doczuordnung->aus_bestand == 1)?'checked=checked':'')}}   type='checkbox' {{ !empty($disable_fields) ? 'disabled' : '' }} >

                                                            <span class="slider round"></span>
                                                        </label>
                                                        Aus Bestand nehmen<br/>
                                                    @endif
                                                    <h3>Dokument sichtbar für:</h3>
                                                    <label class="switch">
                                                        <input name='{{$doczuordnung->id}}_anzeigekunde'
                                                               {{(($doczuordnung->anzeige_kunde == 1)?'checked=checked':'')}} {{ !empty($disable_fields) ? 'disabled' : '' }} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    Kunde (Freigabe)
                                                    @if(strpos($dokument->name ,'.pdf') !== false)
                                                            <?PHP $sf_preview = json_decode($jobdetails->gui_merge, ARRAY_FILTER_USE_BOTH); ?>

                                                        <label class="switch">
                                                            <input name='{{$doczuordnung->id}}_sfpreview' onclick=""
                                                                   {{( !empty($sf_preview) &&  in_array($doczuordnung->id, $sf_preview ) ? 'checked=checked': '')}}
                                                                   type='checkbox' {{ !empty($disable_fields) ? 'disabled' : '' }} >

                                                            <span class="slider round"></span>
                                                        </label>
                                                        MC Preview <input type="text" size="2"
                                                                          name="{{$doczuordnung->id}}_order"
                                                                          value="{{(!empty($sf_preview)  ? array_search($doczuordnung->id, $sf_preview) : '')}}" {{$disable_fields}}>
                                                    @endif

                                                </div>
                                                <div class="clearfix"></div>
                                                <hr>
                                        @endif
                                    </li>
                                    <div class="clearfix"></div>
                                @endforeach

                            @endif
                        </ul>
                    </li>

                </ul>

            </div>
            <div class="projektdienstleister " style="display: none;">
                <div class="dienstleister  ">
                    <div class="titel">
                        <h3>Dienstleister</h3>
                    </div>
                    <div class="form-group col m-0 p-0">


                        @if(!empty($jobdetails->dienstleister[0]->id_dienstleister))
                            <input type="hidden"
                                   name="id_dienstleister"
                                   value="{{$jobdetails->dienstleister[0]->id_dienstleister}}">
                        @endif
                        <select name="id_dienstleister" id="id_dienstleister" class="form-control col-12 col-md-6 mb-1"
                                onchange="return_duser(this.value, 'duserselect')" {{(!empty($jobdetails->dienstleister[0]->id_dienstleister)) ? 'disabled=disabled' : ''}}>
                            <option>Dienstleister auswählen</option>
                            @foreach($dienstleister AS $dleister)
                                <option value="{{$dleister->id}}"
                                        @if(!empty($jobdetails->dienstleister[0]->id_dienstleister))
                                            {{(($jobdetails->dienstleister[0]->id_dienstleister == $dleister->id)? 'selected=selected' : '')}}
                                        @endif    @if(old('id_dienstleister') == $dleister->id) selected @endif>{{$dleister->dienstleister}}</option>
                            @endforeach
                        </select>

                        <div class="users">
                            <div class="">
                                <select id="duserselect" onchange="return_duser_bt(this.value);"
                                        class="form-control  col col-md-6 float-left mr-1 mb-1">
                                    <option value=''>Ansprechpartner auswählen</option>


                                </select>
                                <button class="du_button form-control col-6 float-left m-0"
                                        onclick="return_user_ausgabe($('#duserselect option:selected').val(), 'dusers', $('#duserselect option:selected').val(), $('#duserselect option:selected').text());$('#duserselect').val('').change();  return false;">
                                    Hinzufügen
                                </button>
                            </div>
                            <div class="clearfix"></div>
                            <ul id="dusers" class="p-0">
                                @if(!empty($jobdetails->id))
                                    @foreach($jobdetails->dienstleister AS $duser)

                                        <li>
                                            <ul class="d-flex flex-row  p-0 ">
                                                <input name='{{$duser->id}}_anlegen' type='hidden' value='1'>
                                                <li class=" col-2 active p-2 m-1">{{$duser->vorname}} {{$duser->name}}</li>
                                                <li class="p-2">
                                                    Ansehen

                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_ansehen'
                                                               {{(($dienstleisterinfos[$duser->id]->ansehen == 1)?'checked=checked':'')}}  {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    @if(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1))
                                                        <input name='{{$duser->id}}_ansehen' type="hidden"
                                                               value="{{$dienstleisterinfos[$duser->id]->ansehen}}">
                                                    @endif
                                                </li>
                                                <li class="p-2">
                                                    Upload
                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_upload'
                                                               {{(($dienstleisterinfos[$duser->id]->upload == 1)?'checked=checked':'')}}  {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    @if(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1))
                                                        <input name='{{$duser->id}}_upload' type="hidden"
                                                               value="{{$dienstleisterinfos[$duser->id]->upload}}">
                                                    @endif
                                                </li>
                                                <li class="p-2">
                                                    Download
                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_download'
                                                               {{(($dienstleisterinfos[$duser->id]->download == 1)?'checked=checked':'')}}  {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    @if(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1))
                                                        <input name='{{$duser->id}}_download' type="hidden"
                                                               value="{{$dienstleisterinfos[$duser->id]->download}}">
                                                    @endif
                                                </li>
                                                <li class="p-2">
                                                    Freigabe
                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_freigabe'
                                                               {{(($dienstleisterinfos[$duser->id]->freigabe == 1)?'checked=checked':'')}}   {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    @if(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1))
                                                        <input name='{{$duser->id}}_freigabe' type="hidden"
                                                               value="{{$dienstleisterinfos[$duser->id]->freigabe}}">
                                                    @endif
                                                </li>
                                                <li class="p-2">
                                                    Benachrichtigung
                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_benachrichtigung'
                                                               {{(($dienstleisterinfos[$duser->id]->benachrichtigung == 1)?'checked=checked':'')}}   {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'disabled=disabled':'')}} type='checkbox'>

                                                        <span class="slider round"></span>
                                                    </label>
                                                    @if(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1))
                                                        <input name='{{$duser->id}}_benachrichtigung' type="hidden"
                                                               value="{{$dienstleisterinfos[$duser->id]->benachrichtigung}}">
                                                    @endif
                                                </li>
                                                <li class="p-2">
                                                    Sperre
                                                    <label class="switch">
                                                        <input name='{{$duser->id}}_gesperrt'
                                                               {{(isset($dienstleisterinfos[$duser->id]->gesperrt)&&($dienstleisterinfos[$duser->id]->gesperrt == 1)?'checked=checked':'')}}  type='checkbox'><span
                                                                class="slider_red round"></span>
                                                    </label>
                                                </li>
                                            </ul>
                                        </li>
                                        <div style="clear:both;"></div>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <script type="text/javascript">
        update_select();

        function delete_auflagen(id) {
            //alert($('#va_' + id ).css('text-decoration'));
            if ($('#va_' + id).css('text-decoration').indexOf('none') != -1) {
                $('#va_' + id).css('text-decoration', 'line-through');
                $('#del_va' + id).val(id);
            } else {
                $('#va_' + id).css('text-decoration', 'none');
                $('#del_va' + id).val('');
            }
        }


        $(function () {

// $( ".datum" ).datepicker("option" , $.datepicker.regional[ 'de' ] );
            $(".datum").datepicker({
                dateFormat: 'dd.mm.yy',
                prevText: '&#x3c;zurück',
                prevStatus: '',
                prevJumpText: '&#x3c;&#x3c;',
                prevJumpStatus: '',
                nextText: 'vor&#x3e;',
                nextStatus: '',
                nextJumpText: '&#x3e;&#x3e;',
                nextJumpStatus: '',
                currentText: 'heute',
                currentStatus: '',
                todayText: 'heute',
                todayStatus: '',
                todayHighlight: true,
                clearText: '-',
                clearStatus: '',
                closeText: 'schließen',
                closeStatus: '',
                monthNames: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
                    'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
                monthNamesShort: ['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun',
                    'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],
                dayNames: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'],
                dayNamesShort: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
                dayNamesMin: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
                showMonthAfterYear: false,
                buttonImageOnly: false,
                firstDay: 1

            });
        });

        $("#is_auto").change(function () {
            if (this.checked) {
                $('.autooptionen').slideDown();
            } else {
                $('.autooptionen').slideUp();
            }
        });


        $("#wiederholungen").change(function () {
            $('#DAY').slideUp();
            $('#WEEK').slideUp();
            $('#MONTH').slideUp();

            $('#' + this.value).slideDown();
            $('#intervalltyp').val(this.value);

        });

        $('#dokumentenselect').change(function () {
            if ($('#dokumentenselect').val() != '') {
                $('#doc_hinzufuegen').addClass('bluebutton');
                $('#doc_hinzufuegen').attr('disabled', false);
                $('#doc_hinzufuegen').removeAttr('disabled');
            } else {
                $('#doc_hinzufuegen').removeClass('bluebutton');
                $('#doc_hinzufuegen').attr('disabled', true);
            }

        });

        $("#festlegen").click(function () {

            if ($("#wiederholungen").val() == 'DAY') {
                if ($('#ohne_we').prop('checked')) we = 'ohne Wochenende / Feiertage';
                else we = 'mit Wochenenden / Feiertagen';
                $("#intervall_beschreibung").val('täglich: ' + we);
            } else if ($("#wiederholungen").val() == 'WEEK') {

                if (1) {
                    tage = '';
                    if ($('#mo').prop('checked')) tage += 'montags';
                    if ($('#di').prop('checked') && tage != '') tage += ', dienstags';
                    else if ($('#di').prop('checked') && tage == '') tage += 'dienstags';
                    if ($('#mi').prop('checked') && tage != '') tage += ', mittwochs';
                    else if ($('#mi').prop('checked') && tage == '') tage += 'mittwochs';
                    if ($('#do').prop('checked') && tage != '') tage += ', donnerstags';
                    else if ($('#do').prop('checked') && tage == '') tage += 'donnerstags';
                    if ($('#fr').prop('checked') && tage != '') tage += ', freitags';
                    else if ($('#fr').prop('checked') && tage == '') tage += 'freitags';
                    if ($('#sa').prop('checked') && tage != '') tage += ', samstags';
                    else if ($('#sa').prop('checked') && tage == '') tage += 'samstags';
                    if ($('#so').prop('checked') && tage != '') tage += ', sonntags';
                    else if ($('#so').prop('checked') && tage == '') tage += 'sonntags';
                } else {
                    tage = '';
                }
                $("#intervall_beschreibung").val('wöchentlich: ' + tage);
            } else if ($("#wiederholungen").val() == 'MONTH') {


                $("#intervall_beschreibung").val('monatlich: ' + $("#start_intervall").val() + '(Start) ' + $("#end_intervall").val() + '(Ende)');
            } else {
                $("#intervall_beschreibung").val('');
            }
        });


        function filterselect(dropdown, filter) {

            $('#' + dropdown).html($('#' + dropdown + 'tmp').html());
            //console.log($('#' + dropdown).html());
            var liste = $('#' + dropdown + ' > option');

            liste.each(function (index) {

                if ($(this).text().toLowerCase().indexOf(filter.toLowerCase()) == -1 && filter != '' && this.value != '') $('#' + dropdown + ' option[value="' + this.value + '"]').remove();
                //;
            });

        }

        function delete_bestandteil(id_zuordnung, id_doc) {
            $("#dokumentenselect option[value='" + id_doc + "']").prop("disabled", false);
            $("#dokumentenselecttmp option[value='" + id_doc + "']").prop("disabled", false);
            $('#del_bestandteile').append('<li><input type="hidden" name="delbestandteil[]" value="' + id_zuordnung + '" /></li>');
            update_select();
        }


        function update_select() {
            $("#subkategorielist>li").each(function (index) {
                //console.log( index + ": " + $( this ).attr('id_doc') );
                $("#dokumentenselect option[value='" + $(this).attr('id_doc') + "']").attr('disabled', 'disabled');
                $("#dokumentenselecttmp option[value='" + $(this).attr('id_doc') + "']").attr('disabled', 'disabled');
                $("#dokumentenselect option[value='" + $(this).attr('id_doc') + "']").addClass('strikeout');
                $("#dokumentenselecttmp option[value='" + $(this).attr('id_doc') + "']").addClass('strikeout');

            });
        }


        function add_bestandteil(id_dok, name) {
            $('#subkategorielist').prepend('<li id_doc="' + id_dok + '"> <h3>' + name + '</h3>' +
                '<div>\n' +
                '<input type="hidden" name="adddokument[]" value="' + id_dok + '" />\n' +

                '           <h3>Dokument sichtbar für:</h3>\n' +
                '           <label class="switch">\n' +
                '               <input name="' + id_dok + '_addanzeigekunde"  type="checkbox">\n' +
                '                   <span class="slider round"></span>\n' +
                '           </label>\n' +
                '           Kunde (Freigabe)\n' +
                '</li> <hr>');
            update_select();
        }

        /*
                    '<label class="switch">\n' +
            '               <input name="' + id_dok + '_addbestand"  type="checkbox">\n' +
            '                   <span class="slider round"></span>\n' +
            '           </label>\n' +
            '           Aus Bestand nehmen<br />\n' +
         */


        $(document).ready(function () {
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            @if(!empty($jobdetails->id_kunde))
            return_kuser({{$jobdetails->id_kunde}}, 'kuserselect');
            @endif
            @if(!empty($jobdetails->dienstleister[0]->id_dienstleister))
            return_duser({{$jobdetails->dienstleister[0]->id_dienstleister}}, 'duserselect');
            @endif

            $("#id_kunde").prop('readlonly', true);


            if ($('#is_auto').prop('checked')) {
                $('.autooptionen').slideDown();
            }

        });

        function add_va() {
            $('#va_div').append('<div class="m-0"><input type="hidden" value="{{!empty($jobdetails->id) ? $jobdetails->id : ''}}"><div class="input-group float-left" ><input placeholder="Auflage" name="auflage[]" class="form-control col-3 mr-1"><input placeholder="Verarbeitunsgzeit (Tage)" name="verarbeitungszeit[]" class="form-control mr-1  col-3"><input placeholder="Kosten (Cent)" name="cent[]" class="form-control  col-3"></div></div>');
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        function confirmRedirect(url) {
            if (confirm("Soll das Projekt wirklich kopiert werden?")) {
                console.log(url);
                window.location.assign(url);
            }
        }

    </script>
@endsection
