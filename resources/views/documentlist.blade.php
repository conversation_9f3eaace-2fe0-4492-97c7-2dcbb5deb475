@extends('layouts.app')
@section('content')
    <script src="https://unpkg.com/bootstrap-table@1.18.3/dist/extensions/fixed-columns/bootstrap-table-fixed-columns.min.js"></script>
    <link href="https://unpkg.com/bootstrap-table@1.18.3/dist/extensions/fixed-columns/bootstrap-table-fixed-columns.min.css"
          rel="stylesheet">
    <div class="col-12 p-0 m-0 ">

        <div class=" p-0 m-0">

            <div class="col p-0 m-0 ">
                <div class="title align-middle p-3">
                    <h3>Dokumente</h3>
                </div>
                <div class="float-right m-3 changeview">
                    <a href="{{url('documentlist')}}" class="active"><i class="las la-list "></i></a>
                    <a href="{{url('dokumente')}}"><i class="las la-border-all"></i></a>
                </div>
                @if(!empty(session('error')))
                    <div class="error">{{session('error')}}</div>
                @endif
                <?PHP
                session()->forget('error');
                ?>
                <div class="filter">
                    <ul>
                        <li class="sortByName"><i class="las la-filter"></i>&nbsp;Sort: <span class="black">A-Z</span><i
                                    class="las la-angle-down space"></i></li>
                        <li onclick="$('#docsuche').hide();$('#tagsuche').toggle();"><i class="las la-border-all"></i>&nbsp;Tag:
                            <span class="black">Tag wählen</span><i
                                    class="las la-angle-down space"></i></li>
                        <li onclick="$('#tagsuche').hide();$('#docsuche').toggle();"><i class="las la-info-circle"></i>&nbsp;Dokumenten-Typ:
                            <span class="black">Alle</span><i
                                    class="las la-angle-down space"></i></li>
                        <li><i class="las la-border-all"></i>&nbsp;Suche: <span class="black"><input
                                        name="volltextsuche"
                                        id="volltextsuche"
                                        onkeyup="filterSelection(this.value);"></span>
                        </li>
                    </ul>

                </div>
                <div class="clearfix"></div>
                <div class="subfilter mt-2" id="azsuche" style="display: none;">
                    <ul>
                        <li class="btn btn-small btn-dark" onclick="filterSelection('a-z');">a-z</li>
                        <li class="btn btn-small btn-dark m-1" onclick="filterSelection('a-z');">z-a</li>
                    </ul>
                </div>
                <div class="subfilter" id="tagsuche" style="display: none;">
                    <?PHP $tagssuche = \App\Models\Tags::Where('aktiv', 1)->get(); ?>
                    <ul>
                        <li class="btn btn-small btn-dark" onclick="filterSelection('all');">alles</li>
                        @foreach($tagssuche AS $tagsuche)
                            <li class="btn btn-small btn-dark m-1"
                                onclick="filterSelection('{{$tagsuche->tag}}');">{{$tagsuche->tag}}</li>
                        @endforeach

                    </ul>
                </div>
                <div class="subfilter" id="docsuche" style="display: none;">
                    <?PHP $docssuche = \App\Models\Doctypen::Where('aktiv', 1)->get(); ?>
                    <ul>
                        <li class="btn btn-small btn-dark" onclick="filterSelection('all');">alles</li>
                        @foreach($docssuche AS $docsuche)
                            <li class="btn btn-small btn-dark m-1"
                                onclick="filterSelection('{{$docsuche->typ}}');">{{$docsuche->typ}}</li>
                        @endforeach

                    </ul>
                </div>
                <div></div>
                <div class="d-flex flex-row p-0 m-0">
                    <div class="col">
                        <div id="StopModal" class="modal fade">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        Hinweis
                                    </div>
                                    <div class="modal-body">
                                        <h4>Möchten Sie wirklich dieses Projekt stoppen oder pausieren?</h4>
                                        <h4 id="stopjobname" style="color: #1b4b72;"></h4>
                                        <div class="float-right ">
                                            <input type="hidden" id="idstop" name="idstop" value="">


                                        </div>
                                        <div class="modal-footer mt-4">
                                            <button
                                                    onclick="$('.modal-backdrop').hide();$('#StopModal').hide();return false;"
                                                    class="btn btn-primary float-right ">Nein, zurück
                                            </button>
                                            <button onclick="$('.modal-backdrop').hide();$('#StopModal').hide();"
                                                    class="btn btn-primary float-right ">Ja, Projekt stoppen
                                            </button>
                                        </div>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->
                        <div id="FreigabeModal" class="modal fade col">
                            <form action="{{asset('jobfreigabe/')}}" method="post" name="FreigabeModalForm"
                                  id="FreigabeModalForm">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten dieses Projekt
                                                für {{config('app.CUSTOMER_CRM_ARTIKEL')}} {{config('app.CUSTOMER_CRM')}}
                                                freigeben?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_freigabe" name="id_freigabe">

                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeModal').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>
                                                &nbsp;
                                                &nbsp;
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeModal').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FreigabeWedding" class="modal fade col">
                            <div class="modal-dialog  modal-dialog-centered " role="document">
                                <div class="modal-content ">
                                    <div class="modal-body text-center freigabe-padding">
                                        <h1>Hochzeitsdokument ist noch nicht generiert.</h1>
                                        <div class="clearfix"></div>
                                        <div>Bitte geben Sie zunächst alle zur Freigabe verfügbaren Dokumente frei.
                                        </div>
                                        <div class=" mt-4 justify-content-center ">
                                            <button
                                                    onclick="$('.modal-backdrop').hide();$('#FreigabeWedding').hide();return false;"
                                                    class="btn  btn-normal">zurück
                                            </button>
                                        </div>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->
                        <div id="FreigabeDoc" class="modal fade col">
                            <form action="{{asset('docfreigabe/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten eine Dokumentenfreigabe erteilen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_docfreigabe" name="id_docfreigabe" value="">
                                                <input type="hidden" id="id_job" name="id_job">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeDoc').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button onclick="$('.modal-backdrop').hide();$('#FreigabeDoc').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FreigabeDatei" class="modal fade col">
                            <form action="{{asset('dateifreigabe/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten eine Dateifreigabe erteilen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_dateifreigabe" name="id_dateifreigabe"
                                                       value="">
                                                <input type="hidden" id="id_job" name="id_job">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeDatei').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button onclick="$('.modal-backdrop').hide();$('#FreigabeDatei').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <input type="hidden" id="akt_pdfpage" name="akt_pdfpage" value="1"/>
                        <div class="col-12 col-md-12 ">
                            <div>
                                <a href="{{url('documentlist/new/')}}">
                                    <i class="las la-plus-circle"></i> Dokument hinzufügen
                                </a>
                            </div>

                            <table class="documenttable table " id="documenttable">
                                <thead>
                                <tr>
                                    <th class="no-icon"></th>
                                    <th class="sorting">Name</th>
                                    <th class="tagsshow no-icon">Tags <i class="las la-arrow-left"></i></th>
                                    <th style="display: none;" class="tagsshow">Tags <i class="las la-arrow-right"></i>
                                    </th>
                                    <th>Dokument-Typ</th>
                                    <th>Upload-Datum</th>
                                    <th>Projektname</th>
                                </tr>
                                </thead>
                                @foreach($dokumente AS $dokument)

                                        <?PHP
                                        $tag_string = implode('</li><li>', $tags[$dokument->id]);
                                        $tag_string_clean = implode(' ', $tags[$dokument->id]);
                                        $doc_string_clean = ((!empty($dokument->typ) && !empty($array_typen[$dokument->typ])) ? $array_typen[$dokument->typ] : '');
                                        $doc_string_clean .= (!empty($dokument->name) ? ' ' . $dokument->name : '');
                                        $job_usage = $dokument->get_jobs(['jobs.id', 'jobs.jobbezeichnung']);
                                        $job_id_clean = "";
                                        foreach ($job_usage AS $job_use) {
                                            $job_id_clean .= ' #' . $job_use->id;
                                        }
                                        ?>

                                    <tr class="filterDiv {{$tag_string_clean}} {{$doc_string_clean}} {{$job_id_clean}} {{( !empty($id_dok) && $dokument->id == $id_dok ?? '' ? 'activerow': '')}}">
                                        <td>
                                            <a href="{{url('documentlist/'.$dokument->id)}}">
                                                <div class="  ">
                                                    @if(!empty($dokument->id))
                                                        @if(strpos($dokument->name ,'.doc') !== false)
                                                            <div
                                                                    class="documenticon_text ">
                                                                W
                                                            </div>
                                                        @elseif(strpos($dokument->name ,'.xls')  !== false || stripos($dokument->name ,'.csv')  !== false  )
                                                            <div
                                                                    class="documenticon_tabelle align-items-center ">
                                                                X
                                                            </div>
                                                        @elseif(strpos($dokument->name ,'.pdf') !== false)
                                                            <div
                                                                    class="small_documenticon_pdf align-items-center ">
                                                                @if(!empty($dokument->preview_img))
                                                                    <img
                                                                            src="data:image/jpg;base64,{{base64_encode($dokument->preview_img)}}"
                                                                            class="w-100" class="preview"
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$dokument->id}}Modal"
                                                                            data-container="body"
                                                                            title="{{$dokument->id}}"/>
                                                                @elseif(!empty($dokument->inhalt))
                                                                    <canvas data="" type="application/pdf"
                                                                            id="preview_{{$dokument->id}}"
                                                                            class="pdf_inhalt"
                                                                            style="overflow: hidden; width: 100%; "
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$dokument->id}}Modal"></canvas>
                                                                    <script>
                                                                        $(document).ready(function () {
                                                                            @if(!empty($id_dok ?? ''))
                                                                            getdirdokpreview({{$dokument->id}}, 'preview_{{$dokument->id}}', 1, 1, '..');
                                                                            @else
                                                                            getdirdokpreview({{$dokument->id}}, 'preview_{{$dokument->id}}', 1, 1, './');
                                                                            @endif
                                                                        });
                                                                    </script>
                                                                @else
                                                                    <div
                                                                            class="small_documenticon_pdf align-items-center ">
                                                                        <img
                                                                                src="{{asset('images/PDF-Symbol.png')}}">
                                                                    </div>
                                                                @endif
                                                                <div class="clearfix"></div>
                                                            </div>
                                                        @endif

                                                    @endif
                                                </div>
                                            </a>

                                        </td>
                                        <td>
                                            <div class="name text-nowrap " data-toggle="tooltip" data-placement="top"
                                                 data-html="true" title="{{str_replace("_","_ ",$dokument->name)}}">
                                                {{$dokument->name }}
                                            </div>
                                        </td>
                                        <td class="tagsshow">@if(!empty($tag_string))
                                                <ul>
                                                    <li>{!! $tag_string !!}</li>
                                                </ul>
                                            @endif</td>
                                        <td class="tagsshow tagcount" style="display:none;">
                                            <ul>
                                                <li>{!! count($tags[$dokument->id]) !!}</li>
                                            </ul>
                                        </td>
                                        <td>{{$doc_string_clean}}</td>
                                        <td data-sort="{{!empty($dokument->created_at) ? \Carbon\Carbon::parse($dokument->created_at)->format('Ymd') : '-'}}">{{!empty($dokument->created_at) ? \Carbon\Carbon::parse($dokument->created_at)->format('d.m.Y') : '-'}}</td>
                                        <td>

                                            @if(!empty($job_usage))
                                                @foreach($job_usage AS $used_job)
                                                    #{{$used_job->id}} {{$used_job->jobbezeichnung}} <br/>
                                                @endforeach
                                            @endif
                                        </td>

                                    </tr>

                                @endforeach

                            </table>
                            <div class="clearfix"></div>
                            <div class="bottom-spacer"></div>
                        </div>


                    </div>

                </div>

            </div>
            <div class="float-right position-absolute bg-white p-0  layover"
                 style="display: none; width: {{(!empty($id_dok ?? '') && is_numeric($id_dok ?? '')) ? '1040' : '520'}}px; min-height: 100vh;"
                 id="sidebar_right">
                <form method="post" enctype="multipart/form-data">
                    {{csrf_field()}}
                    @if(!empty($akt_dokument))
                        <div class="float-left w-50 align-items-center p-3">

                            <h3 style="color: #4D7CFE;" data-toggle="tooltip" data-placement="top" data-html="true"
                                title="{{str_replace("_","_ ",$akt_dokument->name)}}">{{((strlen($akt_dokument->name) > 32) ? substr($akt_dokument->name, 0,32)."..." : $akt_dokument->name) }}</h3>

                            <div>
                                Upload: {{(!empty($akt_dokument->created_at) ?\Carbon\Carbon::parse($akt_dokument->created_at)->format('d.m.Y') :'')}}
                                |
                                User: {{!empty($akt_dokument->id_benutzer) ? app('App\Http\Controllers\UserController')->getUserName($akt_dokument->id_benutzer) : ''}}</div>
                            <a href="{{url('directdocdownload').'/' . $akt_dokument->id }}">
                                <div class="btn btn-primary btn-sm">Download</div>
                            </a>
                            @if(!empty($akt_dokument->id))
                                @if(strpos($akt_dokument->name ,'.doc') !== false)
                                    <div style="margin: auto auto;"
                                         class="documenticon_text align-items-center ">
                                        W
                                    </div>
                                @elseif(strpos($akt_dokument->name ,'.xls')  !== false || stripos($akt_dokument->name ,'.csv')  !== false  )
                                    <div style="margin: auto auto;"
                                         class="documenticon_tabelle align-items-center ">
                                        X
                                    </div>
                                @elseif(strpos($akt_dokument->name ,'.pdf') !== false)
                                    <div style="margin: auto auto;"
                                         class="w-50 align-items-center align-content-center">
                                        @if(!empty($akt_dokument->preview_img) )
                                            <img
                                                    src="data:image/jpg;base64,{{base64_encode($akt_dokument->preview_img)}}"
                                                    class="w-100" class="preview"
                                                    data-toggle="modal"
                                                    data-target="#akt_preview_{{$akt_dokument->id}}Modal"
                                                    data-container="body"
                                                    title="{{$akt_dokument->id}}"/>
                                        @elseif(!empty($akt_dokument->inhalt))
                                            <canvas data="" type="application/pdf"
                                                    id="akt_preview_{{$akt_dokument->id}}"
                                                    class="pdf_inhalt"
                                                    style="overflow: hidden; width: 100%; margin:auto auto; "
                                                    data-toggle="modal"
                                                    data-target="#akt_preview_{{$akt_dokument->id}}Modal"></canvas>
                                            <script>
                                                $(document).ready(function () {
                                                    @if(!empty($akt_dokument->id))
                                                    getdirdokpreview({{$akt_dokument->id}}, 'akt_preview_{{$akt_dokument->id}}', 1, 1, '../');
                                                    @else
                                                    getdirdokpreview({{$akt_dokument->id}}, 'akt_preview_{{$akt_dokument->id}}', 1, 1, './');
                                                    @endif
                                                });
                                            </script>
                                        @else
                                            <div style="margin: auto auto;"
                                                 class="small_documenticon_pdf align-items-center ">
                                                <img
                                                        src="{{asset('images/PDF-Symbol.png')}}">
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                <h5><i class="las la-info-circle"></i>&nbsp;Dokument-Information</h5>
                                <div class="tags">
                                    <h6>Tags hinzufügen</h6>
                                    <div class="border-grey w-100 mb-3 h-auto" style="min-height: 45px;">
                                            <?PHP
                                            $tag_string = implode(', ', $tags[$akt_dokument->id]);
                                            ?>
                                        <i class="las la-angle-down float-right"
                                           style="font-size: 25px;margin-top: 5px;"
                                           onclick="$('#all_tags').slideToggle()"></i>
                                        <ul class="d-flex p-0 m-0" id="ultags">
                                            @foreach($tags[$akt_dokument->id] AS $value => $tag)
                                                <li class="m-1 tag p-1"><input type="hidden" name="tags[]"
                                                                               value="{{$value}}"/>{{$tag}}&nbsp;<span
                                                            onclick="($(this).parent()).remove()">x</span></li>
                                            @endforeach
                                        </ul>


                                    </div>
                                    <div id="all_tags" style="display: none;">
                                        @foreach($array_tags AS $value => $tag)
                                            <li class="m-1 tag p-2 border-grey w-50"
                                                onclick="add_tag({{$value}} ,'{{$tag}}' );">{{$tag}}</li>
                                        @endforeach

                                    </div>
                                    <h6 class="mt-3">Dokumenten-Typ</h6>
                                    <select id="doktyp" name="doktyp" onchange="" class="form-control col-12 ">
                                        <option value=''>bitte wählen</option>
                                        @if(!empty($doktypen))
                                            @foreach($doktypen AS  $doktyp)
                                                <option value="{{$doktyp->id}}" {{(($doktyp->id == $akt_dokument->typ)  ? 'selected=selected' : '')}}>{{$doktyp->typ}}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                                <div class="mt-4"></div>
                                <h5 class="w-75 float-left"><i class="las la-info-circle"></i>&nbsp;Neue Vorproduktion
                                </h5>
                                <div class="form-group p-0 float-right w-25 text-right align-content-right">
                                    <div class="float-right m-0 p-0 pr-1">
                                        <div>
                                            <label class="switch mt-2">
                                                <input name='vp' id='vp' type='checkbox'>
                                                <span class="slider round"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                                <div id="vp_main" style="display: none;">


                                    <div class="w-50 float-left p-2">
                                        Vordroduktion
                                        <input class="w-100 border-grey" value="" name="anzahl"
                                               placeholder="Anzahl Vorproduktion">
                                    </div>

                                    <div class="w-50 float-left p-2">
                                        Datum Vorproduktion
                                        <div class="input-group date">
                                            <input type="text" name="datum" placeholder="Datum auswählen"
                                                   class="form-control  datum"
                                                   value="{{(!empty($jobdetails->jobende)  && ($jobdetails->jobende  != '0000-00-00')  ? Carbon\Carbon::parse($jobdetails->jobende)->format('d.m.Y') :  old('jobende')) }}"/>
                                            <div class="input-group-addon">
                                                <span class="glyphicon glyphicon-th"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                                <div>
                                    <div class="w-50 float-left ">
                                        <button type="submit" class="btn btn-primary text-uppercase">Änderungen
                                            speichern
                                        </button>
                                    </div>
                                    <div class="w-50 float-right align-content-right">
                                        <button type="reset" class="btn bg-grey text-uppercase float-right"
                                                onclick="location.href='{{url('documentlist/')}}'">Abbrechen
                                        </button>
                                    </div>

                                </div>
                        </div>
                    @endif
                    @endif
                    @if(!empty($id_dok ?? '') && !is_numeric($id_dok ?? ''))
                        <div class="float-left w-100 align-items-center p-3">
                            <h5><i class="las la-image"></i></i>&nbsp;Dokument hochladen</h5>
                            {{--<<div class="text-uppercase border-grey w-100  text-center" style="height: 135px; padding-top: 60px;">
                                 i class="las la-cloud-upload-alt" style="font-size: 20px;"></i>Datei auswählen oder hinzufügen--}}
                            <input type="file" class="form-control" name="filedata"><br>
                            {{-- </div> --}}
                            <h5><i class="las la-info-circle"></i>&nbsp;Dokument-Information</h5>
                            <div class="tags">
                                <h6>Tags hinzufügen</h6>
                                <div class="border-grey w-100 mb-3 h-auto" style="min-height: 45px;">
                                    <i class="las la-angle-down float-right" style="font-size: 25px;margin-top: 5px;"
                                       onclick="$('#all_tags').slideToggle()"></i>
                                    <ul class="d-flex p-0 m-0" id="ultags">
                                    </ul>
                                </div>
                                <div id="all_tags" style="display: none;">
                                    @foreach($array_tags AS $value => $tag)
                                        <li class="m-1 tag p-2 border-grey w-50"
                                            onclick="add_tag({{$value}} ,'{{$tag}}' );">{{$tag}}</li>
                                    @endforeach
                                </div>
                                <h6 class="mt-3">Dokumenten-Typ</h6>
                                <select id="doktyp" name="doktyp" onchange="" class="form-control col-12 ">
                                    <option value=''>bitte wählen</option>
                                    @if(!empty($doktypen))
                                        @foreach($doktypen AS  $doktyp)
                                            <option value="{{$doktyp->id}}">{{$doktyp->typ}}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="mt-4"></div>
                            <h5 class="w-75 float-left"><i class="las la-info-circle"></i>&nbsp;Neue Vorproduktion</h5>
                            <div class="form-group p-0 float-right w-25 text-right align-content-right">
                                <div class="float-right m-0 p-0 pr-1">
                                    <div>
                                        <label class="switch mt-2">
                                            <input name='vp' id='vp' type='checkbox'>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                            <div id="vp_main" style="display: none;">


                                <div class="w-50 float-left p-2">
                                    Vordroduktion
                                    <input class="w-100 border-grey" value="" name="anzahl"
                                           placeholder="Anzahl Vorproduktion">
                                </div>

                                <div class="w-50 float-left p-2">
                                    Datum Vorproduktion
                                    <div class="input-group date">
                                        <input type="text" name="datum" placeholder="Datum auswählen"
                                               class="form-control  datum"
                                               value=""/>
                                        <div class="input-group-addon">
                                            <span class="glyphicon glyphicon-th"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div class="p-3">
                                <div class="w-50 float-left ">
                                    <button type="submit" class="btn btn-primary text-uppercase">Änderungen speichern
                                    </button>
                                </div>
                                <div class="w-50 float-right align-content-right">
                                    <button type="reset" class="btn bg-grey text-uppercase float-right"
                                            onclick="location.href='{{url('documentlist/')}}'">Abbrechen
                                    </button>
                                </div>

                            </div>

                        </div>
                    @endif


                </form>

                @if(!empty($id_dok ?? '') && is_numeric($id_dok ?? ''))
                    <div class="bg-grey float-right w-50  h-100 p-3">
                        <div>
                            <div class="zahlenblock_dokument w-100">
                                <i class="las la-calculator float-left sfzahlen-icon"></i>
                                <span class="zahl_big">{{number_format($bestand,0, ',','.')}}</span><br/>
                                Aktueller Bestand
                            </div>
                        </div>
                        <div>
                            <div class="zahlenblock_dokument w-100">
                                <i class="las la-long-arrow-alt-down float-left sfzahlen-icon"></i>
                                <span class="zahl_big">{{number_format($verbrauch,0, ',','.')}}</span><br/>
                                Aktueller Gesamtverbrauch
                            </div>
                        </div>
                        <div>

                            <div class="zahlenblock_dokument w-100">
                                <div class="float-right">{{( !empty($vorproduktion_datum->datum) ? \Carbon\Carbon::parse($vorproduktion_datum->datum)->format('d.m.Y') : '')}}</div>
                                <i class="las la-sync float-left sfzahlen-icon"></i>
                                <span class="zahl_big">{{number_format($vorproduktion,0, ',','.')}}</span><br/>
                                Beauftragte neue Vorproduktion
                            </div>
                        </div>
                        <div>
                            <div class="projekte_dokument w-100 h-100">
                                <span>Aktuelle Projektverwendung</span>
                                <table class="table " id="dashboardtable">
                                    <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Hinzugefügt</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @if(!empty($jobs))
                                        @foreach($jobs AS $job)
                                            <tr>
                                                <td>{{$job->jobbezeichnung}}</td>
                                                <td>{{((!empty($job->created_at)) ? ($job->created_at)->format('d.m.Y H:i') : '')}}</td>
                                            </tr>
                                        @endforeach
                                    @endif
                                    </tbody>

                                </table>
                            </div>
                        </div>


                    </div>
                @endif

                <div class="clearfix"></div>
                <div class="bottom-spacer"></div>
            </div>

        </div>


        <script>

            filterSelection("all");

            $('.tagsshow').click(function () {
                $('.tagsshow').toggle();
            });

            $(function () {

// $( ".datum" ).datepicker("option" , $.datepicker.regional[ 'de' ] );
                $(".datum").datepicker({
                    dateFormat: 'dd.mm.yy',
                    prevText: '&#x3c;zurück',
                    prevStatus: '',
                    prevJumpText: '&#x3c;&#x3c;',
                    prevJumpStatus: '',
                    nextText: 'vor&#x3e;',
                    nextStatus: '',
                    nextJumpText: '&#x3e;&#x3e;',
                    nextJumpStatus: '',
                    currentText: 'heute',
                    currentStatus: '',
                    todayText: 'heute',
                    todayStatus: '',
                    todayHighlight: true,
                    clearText: '-',
                    clearStatus: '',
                    closeText: 'schließen',
                    closeStatus: '',
                    monthNames: ['Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
                        'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'],
                    monthNamesShort: ['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun',
                        'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],
                    dayNames: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'],
                    dayNamesShort: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
                    dayNamesMin: ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
                    showMonthAfterYear: false,
                    buttonImageOnly: false,
                    firstDay: 1

                });
            });

            @if(!empty($id_dok ?? ''))
            $('#sidebar_right').animate({width: 'toggle'}, 350);
            @endif

            $('.sortByName').click(function () {
                let order = $sortableTable.order();
                let direction = 'asc';
                if(order[0][1] == 'asc'){
                    direction = 'desc';
                }
                $sortableTable.order([[1, direction]]).draw();
            });


            $(document).ready(function () {
                $sortableTable = $('#documenttable').DataTable({
                    "order": [[1, 'asc']],
                    searching: false,
                    paging: false,
                    info: false,
                    "language": {
                        "emptyTable": "Keinen Dokumente zugewiesen."
                    }
                });
            });

            function add_tag(id_typ, tag) {
                $('#ultags').append('<li  class="m-1 tag p-1"><input type="hidden" name="tags[]" value="' + id_typ + '"/>' + tag + '&nbsp;<span onclick="($(this).parent()).remove()">x</span></li>');

            }


            $('#vp').on("click", function () {

                if ($('#vp').prop('checked')) $('#vp_main').slideDown();
                else $('#vp_main').slideUp();
            });

            function klappandtoogle(elementid) {


                if (!$('#pfeil' + elementid).hasClass('rotate')) {


                    $('#pfeil' + elementid).toggleClass('rotate');
                    $('#content' + elementid).slideToggle();
                } else {
                    $('#pfeil' + elementid).toggleClass('rotate-reset');
                    $('#content' + elementid).slideToggle();
                }

            }

            // popovers initialization - on hover
            $('[data-toggle="popover"]').popover({
                html: true,
                trigger: 'click',
                placement: 'left',
                container: 'body',
                content: function () {
                    return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />';
                }
            });

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            })

            $(document).on("click", ".popover .closer", function () {
                $(this).parents(".popover").popover('hide');
            });
            @if(!empty($id_doc)) $('#sidebar_right').animate({width: 'toggle'}, 350);@endif


            function filterSelection(c) {
                var x, i;
                x = document.getElementsByClassName("filterDiv");
                if (c == "all") c = "";
                // Add the "show" class (display:block) to the filtered elements, and remove the "show" class from the elements that are not selected
                for (i = 0; i < x.length; i++) {
                    w3RemoveClass(x[i], "show_cell");
                    if (x[i].className.toLocaleLowerCase().indexOf(c.toLocaleLowerCase()) > -1) w3AddClass(x[i], "show_cell");
                }
            }

            // Show filtered elements
            function w3AddClass(element, name) {
                var i, arr1, arr2;
                arr1 = element.className.split(" ");
                arr2 = name.split(" ");
                for (i = 0; i < arr2.length; i++) {
                    if (arr1.indexOf(arr2[i]) == -1) {
                        element.className += " " + arr2[i];
                    }
                }
            }

            // Hide elements that are not selected
            function w3RemoveClass(element, name) {
                var i, arr1, arr2;
                arr1 = element.className.split(" ");
                arr2 = name.split(" ");
                for (i = 0; i < arr2.length; i++) {
                    while (arr1.indexOf(arr2[i]) > -1) {
                        arr1.splice(arr1.indexOf(arr2[i]), 1);
                    }
                }
                element.className = arr1.join(" ");
            }


            function sortListDir(element) {
                var list, i, switching, b, shouldSwitch, dir, switchcount = 0;
                list = document.getElementById(element);
                switching = true;
                // Set the sorting direction to ascending:
                dir = "asc";
                // Make a loop that will continue until no switching has been done:
                while (switching) {
                    // Start by saying: no switching is done:
                    switching = false;
                    b = list.getElementsByTagName("LI");
                    // Loop through all list-items:
                    for (i = 0; i < (b.length - 1); i++) {
                        // Start by saying there should be no switching:
                        shouldSwitch = false;
                        /* Check if the next item should switch place with the current item,
                        based on the sorting direction (asc or desc): */
                        if (dir == "asc") {
                            if (b[i].innerHTML.toLowerCase() > b[i + 1].innerHTML.toLowerCase()) {
                                /* If next item is alphabetically lower than current item,
                                mark as a switch and break the loop: */
                                shouldSwitch = true;
                                break;
                            }
                        } else if (dir == "desc") {
                            if (b[i].innerHTML.toLowerCase() < b[i + 1].innerHTML.toLowerCase()) {
                                /* If next item is alphabetically higher than current item,
                                mark as a switch and break the loop: */
                                shouldSwitch = true;
                                break;
                            }
                        }
                    }
                    if (shouldSwitch) {
                        /* If a switch has been marked, make the switch
                        and mark that a switch has been done: */
                        b[i].parentNode.insertBefore(b[i + 1], b[i]);
                        switching = true;
                        // Each time a switch is done, increase switchcount by 1:
                        switchcount++;
                    } else {
                        /* If no switching has been done AND the direction is "asc",
                        set the direction to "desc" and run the while loop again. */
                        if (switchcount == 0 && dir == "asc") {
                            dir = "desc";
                            switching = true;
                        }
                    }
                }
            }


        </script>

@endsection
