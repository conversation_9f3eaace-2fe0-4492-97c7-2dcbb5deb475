@extends('layouts.app')
@section('content')

    <?PHP

    if(empty($_GET['pdfpage'])) $pdfpage = 1;
    else $pdfpage = $_GET['pdfpage'];

    if(empty($_GET['pdfzoom'])) $pdfzoom = 1;
    else $pdfzoom = $_GET['pdfzoom'];


    function array_set_pointer_to_key(&$array, $key)
    {
        reset($array);
        $c = 0;
        $l = count($array);
        while (key($array) !== $key) { // jeden Key überprüfen
            if (++$c >= $l) return false; // Array-Ende erreicht
            next($array); // Pointer um 1 verschieben
        }
        return true; // Key gefunden
    }


    function get_next_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = next($array);
        if(empty($return_val)) $return_val = array_shift($array);
        return $return_val;

    }
    function get_prev_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = prev($array);
        if(empty($return_val)) $return_val = end($array);
        return $return_val;

    }
    ?>
    <div class="title align-middle">
        <h3 class="p-2"><a href="{{url('freigabe').'/'. $job->id.'/doc/'.$id_doc}}"><i
                    class="las la-angle-left border-grey"></i> #{{$job->id}} - {{$job->jobbezeichnung}}</a>


    </div>
    <div class="weddingdetails">
        @if(!empty($error))
            <br/>
            <div class="error">
                {{$error}}
            </div>
            <br/>
        @endif

        <div class="pfadnavi ">
            <div class="float-left float-md-right row  ">


            </div>
            <div class="clear"></div>
        </div>
        <div id="content" class="col-12 float-left">
            <h3 class="filealias">

            </h3>
            <form method="post" id="suchform" onsubmit="return false;">
                {{csrf_field()}}
                <div class="suchform ">
                    <div class="m-2 form-group row">
                        <input type="hidden" name="pdfpage" id="pdfpage">
                        <input type="hidden" name="page" id="hiddenpage"
                               value="{{(($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) ? $_POST['page'] : 1)}}">
                    </div>
                    <div class="clearfix"></div>
                    <div class="">
                        {{--
                        <button
                            onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage - 1 : 1}}; submit();">
                            <
                        </button>
                        @if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif / <span id="max_pages"> </span>
                        <button
                            onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage + 1 : 2}} ; submit();"
                            id="pdf_next">>
                        </button>
 --}}

                        <div id="seitenzahlen">
                            <button
                                onclick="$('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1);   if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1);  $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val())  , parseFloat($('#zoom').val()) ,'{{url('/')}}' ,'{{(!empty($id_file) ? $id_file : '')}}')); return false;">
                                <
                            </button>

                            <input type="text" id="akt_pdfpage" name="akt_pdfpage" value="@if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif"  style="width: 50px; text-align: right; margin-right: 10px;" onblur="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), parseFloat($('#zoom').val()),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')">/ <span id="max_pages"> </span>

                            <button
                                onclick=" $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val())  , parseFloat($('#zoom').val()),'{{url('/')}}' , '{{(!empty($id_file) ? $id_file : '')}}' ));  return false;"
                                id="pdf_next">>
                            </button>
                            <button onclick="print_id({{$id_doc}}, $('#akt_pdfpage').val());  return false;" style="width: 60px !important;">drucken</button>
                            Zoom:
                            <button
                                onclick=" $('#zoom').val((parseFloat($('#zoom').val()) -0.1).toFixed(1)); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) -0.1,'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}'));return false;"
                                id="pdf_next">-
                            </button>

                            <input type="text" id="zoom" name="zoom" value="{{$pdfzoom}}"  style="width: 50px; text-align: center; margin-right: 10px;" onblur="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val(),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')" onkeyup="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val(),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')">

                            <button
                                onclick="$('#zoom').val((parseFloat($('#zoom').val()) +0.1).toFixed(1)); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) +0.1,'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')); return false;"
                                id="pdf_next">+
                            </button>

                        </div>


                        <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">
                        <div class="pdfview">
                            <div class="pdfviewer" style="overflow: hidden !important;">
                                <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                <canvas data="" type="" id="pdf_inhalt" class="pdf_inhalt" style="overflow: hidden;" >
                                </canvas>
                                <script>
                                    $(document).ready(function () {
                                        previewmakerdoc({{$id_doc}}, 'pdf_inhalt', {{$pdfpage}}, {{$pdfzoom}}, '{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}');
                                    });
                                </script>

                            </div>
                        </div>
                    </div>

                <div class="clear"></div>
            </form>
        </div>

    </div>

    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $(document).ready(function () {
            var resize = $(".splitt_left");
            var containerWidth = $("#content").width();

            $(resize).resizable({
                handles: 'e',
                minWidth: 200,
                resize: function (event, ui) {
                    var currentWidth = ui.size.width;
                    // this accounts for padding in the panels +
                    // borders, you could calculate this using jQuery
                    var padding = 220;
                    // this accounts for some lag in the ui.size value, if you take this away
                    // you'll get some instable behaviour
                    $(this).width(currentWidth);
                    // set the content panel width

                    //console.log(containerWidth - currentWidth - padding);
                    $('.splitt_right').width(containerWidth - currentWidth - padding - 20);
                }
            });

        });

        $(document).ready(function () {
            $('#adressdaten').DataTable({
                sorting: false,
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Adressdatei nicht ausgewählt."
                }
            });
        });
        /*
                var resizeOptsleft = {
                    handles: "ew" ,autoHide:true
                };
                var resizeOptsright = {
                    handles: "we" ,autoHide:true
                };
                $( ".splitt_left" ).resizable();
                $( ".splitt_left" ).draggable();
               $( ".splitt_right" ).draggable();
               $( ".splitt_right" ).resizable();

        */

        jQuery(function () {
            var sticky_navigation_offset_top = jQuery('#splitt_left').offset().top;
            var sticky_navigation = function () {
                var scroll_top = jQuery(window).scrollTop();
                if (scroll_top > sticky_navigation_offset_top) {
                    jQuery('#splitt_left').addClass('sticky');

                } else {
                    jQuery('#splitt_left').removeClass('sticky');
                }
            };
            sticky_navigation();
            jQuery(window).scroll(function () {
                sticky_navigation();
            });
            jQuery('a[href="#"]').click(function (event) {
                event.preventDefault();
            });
        });


        var canvas = document.getElementById("pdf_inhalt");
        var context = canvas.getContext('2d');
        var dragging = false;
        var lastX;
        var marginLeft = 0;
        var marginTop = 0;

        for (var i = 0; i < 1000; i++) {
            context.beginPath();
            context.arc(Math.random() * 10000, Math.random() * 250, 20.0, 0, 2 * Math.PI, false);
            context.stroke();
        }

        canvas.addEventListener('mousedown', function(e) {
            var evt = e || event;
            dragging = true;
            lastX = evt.clientX;
            lastY = evt.clientY;
            e.preventDefault();
        }, false);

        canvas.addEventListener('mouseup', function(e) {
            var evt = e || event;
            dragging = false;
            lastX = evt.clientX;
            lastY = evt.clientY;
            e.preventDefault();
        }, false);

        window.addEventListener('mousemove', function(e) {
            var evt = e || event;
            if (dragging) {
                var deltaX = evt.clientX - lastX;
                var deltaY = evt.clientY - lastY;
                lastX = evt.clientX;
                lastY = evt.clientY;
                marginLeft += deltaX;
                marginTop += deltaY;
                canvas.style.marginLeft = marginLeft + "px";
                canvas.style.marginTop = marginTop + "px";
            }
            e.preventDefault();
        }, false);

        $( function() {
            $( ".draggable" ).draggable({
                helper: 'clone'
            });
            $( ".droppable" ).droppable({
                drop: function( event, ui ) {
                    $( this )
                        .val( $( this )
                            .val() + ';'+  $(ui.draggable).html());
                    //console.log($(ui.draggable));
                }
            });
        } );

        function add_makerdata(){

            $('.add_makerdata').html($('.add_makerdata').html() + '<li>\
                <label class="col"></label>\
        <input type="text" name="seiteneu[]" value="" placeholder="Seite" class="col-lg-1"/>\
        <input type="text" name="xneu[]" value="" placeholder="x" class="col-lg-2"/>\
        <input type="text" name="yneu[]" value="" placeholder="y" class="col-lg-2"/>\
        <input type="text" name="spaltenneu[]" value="Test" placeholder="Spalte(n)" class="col-lg-5 droppable" />\
        <input type="text" name="sizeneu[]" value="" placeholder="Size" class="col-lg-1"/>\
        </li>');
        }


    </script>

@endsection

