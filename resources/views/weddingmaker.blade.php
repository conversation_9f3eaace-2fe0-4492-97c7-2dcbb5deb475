@extends('layouts.app')
@section('content')

    <?PHP

    if(empty($_GET['pdfpage'])) $pdfpage = 1;
    else $pdfpage = $_GET['pdfpage'];

    if(empty($_GET['pdfzoom'])) $pdfzoom = 1;
    else $pdfzoom = $_GET['pdfzoom'];

    function array_set_pointer_to_key(&$array, $key)
    {
        reset($array);
        $c = 0;
        $l = count($array);
        while (key($array) !== $key) { // jeden Key überprüfen
            if (++$c >= $l) return false; // Array-Ende erreicht
            next($array); // Pointer um 1 verschieben
        }
        return true; // Key gefunden
    }


    function get_next_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = next($array);
        if(empty($return_val)) $return_val = array_shift($array);
        return $return_val;

    }
    function get_prev_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = prev($array);
        if(empty($return_val)) $return_val = end($array);
        return $return_val;

    }
    ?>
    <div class="weddingdetails">
        @if(!empty($error))
            <br/>
            <div class="error">
                {{$error}}
            </div>
            <br/>
        @endif

        <div class="pfadnavi ">
            <div class="float-left float-md-right row  ">


            </div>
            <div class="clear"></div>
        </div>
        <div id="content" class="col-6 float-left">
            <h3 class="filealias">

            </h3>
            <form method="post" id="suchform" onsubmit="return false;">
                {{csrf_field()}}
                <div class="suchform ">
                    <div class="m-2 form-group row">
                        <input type="hidden" name="pdfpage" id="pdfpage">
                        <input type="hidden" name="page" id="hiddenpage"
                               value="{{(($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) ? $_POST['page'] : 1)}}">
                    </div>
                    <div class="clearfix"></div>
                    <div class="col">
                        {{--
                        <button
                            onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage - 1 : 1}}; submit();">
                            <
                        </button>
                        @if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif / <span id="max_pages"> </span>
                        <button
                            onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage + 1 : 2}} ; submit();"
                            id="pdf_next">>
                        </button>
 --}}

                        <div id="seitenzahlen">
                            <button
                                onclick="$('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1);  if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1);  $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val())   ,parseFloat($('#zoom').val()) ,'{{url('/')}}' ,'{{(!empty($id_file) ? $id_file : '')}}')); return false;">
                                <
                            </button>

                            <input type="text" id="akt_pdfpage" name="akt_pdfpage" value="@if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif"  style="width: 50px; text-align: right; margin-right: 10px;" onblur="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), parseFloat($('#zoom').val()),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')">/ <span id="max_pages"> </span>

                            <button
                                onclick=" $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()),'{{url('/')}}' , '{{(!empty($id_file) ? $id_file : '')}}' ));  return false;"
                                id="pdf_next">>
                            </button>
                            <button onclick="print_id({{$id_doc}}, $('#akt_pdfpage').val());  return false;" style="width: 60px !important;">drucken</button>
                            Zoom:
                            <button
                                onclick=" $('#zoom').val((parseFloat($('#zoom').val()) -0.1).toFixed(1)); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) -0.1,'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}'));return false;"
                                id="pdf_next">-
                            </button>

                            <input type="text" id="zoom" name="zoom" value="{{$pdfzoom}}"  style="width: 50px; text-align: center; margin-right: 10px;" onblur="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val(),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')" onkeyup="previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val(),'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')">

                            <button
                                onclick="$('#zoom').val((parseFloat($('#zoom').val()) +0.1).toFixed(1)); $.debounce(1000,previewmakerdoc({{$id_doc}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) +0.1,'{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}')); return false;"
                                id="pdf_next">+
                            </button>

                        </div>

                        <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">
                        <div class="pdfview">
                            <div class="pdfviewer" style="overflow: hidden !important;">
                                <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                <canvas data="" type="" id="pdf_inhalt" class="pdf_inhalt" style="overflow: hidden;" >
                                </canvas>
                                <script>
                                    $(document).ready(function () {
                                        previewmakerdoc({{$id_doc}}, 'pdf_inhalt', {{$pdfpage}}, {{$pdfzoom}}, '{{url('/')}}', '{{(!empty($id_file) ? $id_file : '')}}');
                                    });
                                </script>

                            </div>
                        </div>
                    </div>

                <div class="clear"></div>
            </form>
        </div>

    </div>
    <div class="col-6 float-right h-100 whitebg">
        <form method="post" enctype="multipart/form-data">
            {{csrf_field()}}
            <input type="hidden" name="pdfpage" id="pdfpagemaker"  value="{{$pdfpage}}">
            <input type="hidden" name="zoom" id="pdfzoom"  value="{{$pdfzoom}}">
        <div class="verfuegbare_spalten">
            <ul >
                @foreach($spalten AS $spalte )
                    <li class="draggable ">{{$spalte}}</li>
                @endforeach
            </ul>
            <div class="clearfix"></div>
        </div>
        <div class="p-3">
            <div class="pl-4">
                <h5><i class="las la-table"></i>&nbsp;Vermaßung per Excel uploaden</h5>
                <input type="file" class="form-control" name="filedata">
            </div>
        </div>



        <ul class="add_makerdata p-0">
            <?PHP $count = 1;?>
        @foreach($weddingmaker AS $maker )
                <li class="wm_zeile d-lg-flex">

                            <label class="col">{{$count++}}.</label>

                            <input type="text" name="{{$maker->id}}_seite" value="{{$maker->seite}}" placeholder="Seite" class="col-lg-1"  data-toggle="tooltip" data-placement="top" title="Seite"/>

                                <input type="text" name="{{$maker->id}}_x" value="{{$maker->x}}" placeholder="x (mm)" class="text-right" size="5" data-toggle="tooltip" data-placement="top" title="x-Abstand von links (mm)" />
                                <input type="text" name="{{$maker->id}}_y" value="{{$maker->y}}" placeholder="y (mm)"  class="text-right" size="5" data-toggle="tooltip" data-placement="top" title="y-Abstand von oben (mm)"/>

                            <input type="text" name="{{$maker->id}}_spalten" value="{{$maker->spalten}}" placeholder="Spalte(n)" class="col-lg-4 droppable" data-toggle="tooltip" data-placement="top" title="Variablen mit Leerzeichen verbunden" />
                            <input type="text" name="{{$maker->id}}_max" value="{{($maker->max > 0 ? $maker->max : '')}}" placeholder="max." class="droppable" data-toggle="tooltip" data-placement="top" size="3" title="maximale Länge der Zeile" />

                            <input type="text" name="{{$maker->id}}_trenner" value="{{$maker->trenner}}" placeholder=""   data-toggle="tooltip" data-placement="top" title="Dieses Trennzeichen an der Stelle _#_ einfügen" class="text-center" size="1" />
                            <input type="text" name="{{$maker->id}}_size" value="{{$maker->size}}" placeholder="Pt." class="text-right" size="3" data-toggle="tooltip" data-placement="top" title="Schriftgröße (Pt)" />
                            <input type="text" name="{{$maker->id}}_farbe" value="{{$maker->farbe}}" placeholder="C,M,Y,K" size="8" data-toggle="tooltip" data-placement="top" title="Abweichender CMYK-Farbwert"  />

                    <div class="form-group col-lg-2">
                        <div>
                            <select name="{{$maker->id}}_font" class="form-control" >
                                @if(!empty($schriften))
                                    @foreach($schriften AS $schrift)
                                        <option value="{{$schrift['bezeichnung']}}" {{(($schrift['bezeichnung'] == $maker->font ) ? 'selected=selected' : '')}}>{{$schrift['bezeichnung']}}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                </li>



        @endforeach
                <li class="wm_zeile d-lg-flex">
                    <label class="col"></label>
                    <input type="text" name="seiteneu[]" value="" placeholder="Seite" class="col-lg-1"  data-toggle="tooltip" data-placement="top" title="Seite"/>

                    <input type="text" name="xneu[]" value="" placeholder="x (mm)" class="text-right" size="5" data-toggle="tooltip" data-placement="top" title="x-Abstand von links (mm)"/>
                    <input type="text" name="yneu[]" value="" placeholder="y (mm)" class="text-right" size="5" data-toggle="tooltip" data-placement="top" title="y-Abstand von oben (mm)"/>

                    <input type="text" name="spaltenneu[]" value="" placeholder="Spalte(n)" class="col-lg-5 droppable" data-toggle="tooltip" data-placement="top" title="Variablen mit Leerzeichen verbunden" />
                    <input type="text" name="maxneu[]" value="" placeholder="max." class="droppable" data-toggle="tooltip" size="3" data-placement="top" title="maximale Länge der Zeile" />

                        <input type="text" name="trennerneu[]" value="" placeholder=""  titel="Trenner"  data-toggle="tooltip" data-placement="top" title="Dieses Trennzeichen an der Stelle _#_ einfügen" class="text-center" size="1" />
                        <input type="text" name="sizeneu[]" value="" placeholder="Pt." class="text-right"  size="5" data-toggle="tooltip" data-placement="top" title="Schriftgröße (Pt)" />
                        <input type="text" name="farbeneu[]" value="" placeholder="C,M,Y,K" size="8" data-toggle="tooltip" data-placement="top" title="Abweichender CMYK-Farbwert" />

                    <div class="form-group">
                        <div>
                            <select name="font[]" class="form-control" >
                                @if(!empty($schriften))
                                    @foreach($schriften AS $schrift)
                                        <option value="{{$schrift['bezeichnung']}}">{{$schrift['bezeichnung']}}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                </li>
        </ul>
{{--
        <div class="text-center" onclick="add_makerdata();"><div class="btn btn-default" ><i class="las la-plus-square"></i></div></div>
--}}
            <div class="float-left pl-4">&nbsp;
                <label class="switch mt-2">
                    <input name='wedding_ok'   type='checkbox'>
                    <span class="slider round"></span>
                </label><span class="text-danger">&nbsp;abgeschlossen</span>
            </div>

            <button class="btn btn-primary float-right" onclick="$('#pdfpagemaker').val($('#akt_pdfpage').val());$('#pdfzoom').val($('#zoom').val());">Speichern</button>
            <div class="btn btn-primary float-right mr-3" onclick="exportXlsx({{(!empty($maker->id_dokument) ? $maker->id_dokument : '')}});">Export</div>
        </form>
    </div>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $(document).ready(function () {
            var resize = $(".splitt_left");
            var containerWidth = $("#content").width();

            $(resize).resizable({
                handles: 'e',
                minWidth: 200,
                resize: function (event, ui) {
                    var currentWidth = ui.size.width;
                    // this accounts for padding in the panels +
                    // borders, you could calculate this using jQuery
                    var padding = 220;
                    // this accounts for some lag in the ui.size value, if you take this away
                    // you'll get some instable behaviour
                    $(this).width(currentWidth);
                    // set the content panel width

                    //console.log(containerWidth - currentWidth - padding);
                    $('.splitt_right').width(containerWidth - currentWidth - padding - 20);
                }
            });

        });

        $(document).ready(function () {
            $('#adressdaten').DataTable({
                sorting: false,
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Adressdatei nicht ausgewählt."
                }
            });
        });
        /*
                var resizeOptsleft = {
                    handles: "ew" ,autoHide:true
                };
                var resizeOptsright = {
                    handles: "we" ,autoHide:true
                };
                $( ".splitt_left" ).resizable();
                $( ".splitt_left" ).draggable();
               $( ".splitt_right" ).draggable();
               $( ".splitt_right" ).resizable();

        */

        jQuery(function () {
            var sticky_navigation_offset_top = jQuery('#splitt_left').offset().top;
            var sticky_navigation = function () {
                var scroll_top = jQuery(window).scrollTop();
                if (scroll_top > sticky_navigation_offset_top) {
                    jQuery('#splitt_left').addClass('sticky');

                } else {
                    jQuery('#splitt_left').removeClass('sticky');
                }
            };
            sticky_navigation();
            jQuery(window).scroll(function () {
                sticky_navigation();
            });
            jQuery('a[href="#"]').click(function (event) {
                event.preventDefault();
            });
        });


        var canvas = document.getElementById("pdf_inhalt");
        var context = canvas.getContext('2d');
        var dragging = false;
        var lastX;
        var marginLeft = 0;
        var marginTop = 0;

        for (var i = 0; i < 1000; i++) {
            context.beginPath();
            context.arc(Math.random() * 10000, Math.random() * 250, 20.0, 0, 2 * Math.PI, false);
            context.stroke();
        }

        canvas.addEventListener('mousedown', function(e) {
            var evt = e || event;
            dragging = true;
            lastX = evt.clientX;
            lastY = evt.clientY;
            e.preventDefault();
        }, false);

        canvas.addEventListener('mouseup', function(e) {
            var evt = e || event;
            dragging = false;
            lastX = evt.clientX;
            lastY = evt.clientY;
            e.preventDefault();
        }, false);

        window.addEventListener('mousemove', function(e) {
            var evt = e || event;
            if (dragging) {
                var deltaX = evt.clientX - lastX;
                var deltaY = evt.clientY - lastY;
                lastX = evt.clientX;
                lastY = evt.clientY;
                marginLeft += deltaX;
                marginTop += deltaY;
                canvas.style.marginLeft = marginLeft + "px";
                canvas.style.marginTop = marginTop + "px";
            }
            e.preventDefault();
        }, false);

        $( function() {
            $( ".draggable" ).draggable({
                helper: 'clone'
            });
            $( ".droppable" ).droppable({
                drop: function( event, ui ) {
                    $( this )
                        .val( $( this )
                            .val() + ';'+  $(ui.draggable).html());
                    //console.log($(ui.draggable));
                }
            });
        } );

        function add_makerdata(){

            $('.add_makerdata').html($('.add_makerdata').html() + '<li>\
                <label class="col"></label>\
        <input type="text" name="seiteneu[]" value="" placeholder="Seite" class="col-lg-1"/>\
        <input type="text" name="xneu[]" value="" placeholder="x" class="col-lg-2"/>\
        <input type="text" name="yneu[]" value="" placeholder="y" class="col-lg-2"/>\
        <input type="text" name="spaltenneu[]" value="Test" placeholder="Spalte(n)" class="col-lg-5 droppable" />\
        <input type="text" name="sizeneu[]" value="" placeholder="Size" class="col-lg-1"/>\
        </li>');
        }

        function exportXlsx(id) {
            $.get('/getmakercsv/' + id , {},
                function (data) {
                //console.log(data);
                    let a = document.createElement('a');
                    a.download = "export.xlsx";
                    a.href = "data:application/vnd.ms-excel;base64," + (data);
                    a.click();
                }
            );
        }

    </script>

@endsection

