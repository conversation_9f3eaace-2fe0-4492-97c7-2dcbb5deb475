@extends('layouts.app')
@section('content')
    <?PHP

    if (empty($_GET['pdfpage'])) $pdfpage = 1;
    else $pdfpage = $_GET['pdfpage'];

    if (empty($_GET['pdfzoom'])) $pdfzoom = 1;
    else $pdfzoom = $_GET['pdfzoom'];

    if (empty($showDataSetNr)) {
        $showDataSetNr = 1;
    }

    function array_set_pointer_to_key(&$array, $key)
    {
        reset($array);
        $c = 0;
        $l = count($array);
        while (key($array) !== $key) { // jeden Key überprüfen
            if (++$c >= $l) return false; // Array-Ende erreicht
            next($array); // Pointer um 1 verschieben
        }
        return true; // Key gefunden
    }


    function get_next_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = next($array);
        if (empty($return_val)) $return_val = array_shift($array);
        return $return_val;

    }
    function get_prev_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = prev($array);
        if (empty($return_val)) $return_val = end($array);
        return $return_val;

    }
    ?>
    <div class="filedetails">
        @if(!empty($error))
            <br/>
            <div class="error">
                {{$error}}
            </div>
            <br/>
        @endif
        @php

            $file = \App\Models\Datei::find($id_file);
        @endphp

        <div class="title align-middle">
            <h3 class="p-2"><a href="{{url('freigabe/' . $file->id_job . '/reg/marketingcloud')}}"><i
                            class="las la-angle-left border-grey"></i></a> #{{$file->id_job}} - {{$file->step_id}}
            </h3>

        </div>
        <div class="clear"></div>
        <div id="content">
            <h3 class="filealias">

            </h3>
            <form method="post" id="suchform" onsubmit="return false;">
                {{csrf_field()}}
                <div class="">
                    <div class="m-2 form-group row">
                        <input type="hidden" name="pdfpage" id="pdfpage">
                        <input type="hidden" name="page" id="hiddenpage"
                               value="{{(($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) ? $_POST['page'] : 1)}}">
                    </div>
                    <div class="clearfix"></div>
                    <div class="d-flex flex-row">

                        <div class="col-3">
                    <div class="col h-auto d-inline-block">
                        <div>
                            <label for="showDataSetNr">Datensatz</label>
                            <input type="text" id="showDataSetNr" name="showDataSetNr"
                                   value="{{$showDataSetNr != '' ? $showDataSetNr: 1}}"
                                   style="width: 50px; text-align: center; margin-right: 10px;">
                        </div>
                        <div class="form-check form-check-inline">
                            <label class="form-check-label" for="showDataSetNr">Variablen hervorheben</label>
                            &nbsp;<input type="checkbox" class="form-check-input" name="showVars" value="1"
                                         id="showVars">

                        </div>

                        <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">


                    </div>
                    <div class="clear"></div>

                            <div class="m-3 btn btn-primary"
                                 onclick="window.open('{{ url('/mcpdf/' . $id_doc .'/file/' .$id_file ) }}/' + $('#showDataSetNr').val() + ( $('#showVars').is(':checked') ? '/showVars/' + $('#showVars').val() : ''))
                         ">
                                Preview anzeigen
                            </div>
                        </div>
                        <div class="col-9">
                            <canvas
                                    data=""
                                    type="application/pdf"
                                    id="preview_{{$id_doc}}"
                                    style="overflow: hidden;"
                                    ></canvas>
                        </div>
                        <script>
                            $(document).ready(function () {
                                previewdocpdf({{ $id_doc }}, 'preview_{{$id_doc}}', 1, 1, ''  );

                            });
                        </script>
                    </div>
                </div>
            </form>
        </div>
    </div>

@endsection

