@extends('layouts.app')
@section('content')
    <div class="filedetails">
        @if(!empty($error))
            <br/>
            <div class="error">
                {{$error}}
            </div>
            <br/>
        @endif

        <div class="pfadnavi">
            <div class="float-left">
                <h4>#{{$filedetails['id_job']}} - {{$filedetails['name']}}</h4>
            </div>
            <div class="float-left float-md-right row ">
                <?PHP  $id_job = $filedetails['id_job']; ?>
                <div class="col-12 m-0   ">


                </div>

            </div>


            <div class="clear"></div>

        </div>
        <div>

            <div class="suchform">

                <form method="post">
                    <div class="m-2 ">
                        {{csrf_field()}}
                        <div class="form-group row p-2">
                            <input name="suche" id="suche" value="{{ request('suche') }}" placeholder="Suchbegriff" class="form-control col col-md-3 float-left m-0">
                            <input name="id_adressdaten" id="id_adressdaten" value="{{ $id_job }}" type="hidden">
                            <select name="feld" class="form-control col-12 col-md-3 float-left m-1">
                                <option value="*">Bitte Spalte auswählen...</option>
                                @foreach($filedetails['header'] AS $col)
                                    @if($col != 'qx_zeilen_nr')
                                    <option value="{{$col}}" {{($col == request('feld')? 'selected': '')}}>
                                        {{$col}}
                                    </option>
                                    @endif

                                @endforeach
                            </select>
                            <button name="page" value="1" class="form-control col-12 col-md-2 float-left ml-md-3  m-1 btn  btn-primary">Suche</button>

                        </div>

                        <div class="clearfix"></div>
                    </div>
                   <div class="datencontainer overflow-hidden">
                        <div class="d-flex justify-content-between">
                            <div class="pages float-left">
                                <?PHP if ($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) $page = $_POST['page']; else $page = 1; ?>
                                <button name="page" value="{{(($page-1) > 1 ? ($page-1):  1)}}"><</button>
                                <button name="page"
                                        value="{{(($page+1) <= ceil(($filedetails['searchcount'])/6) ? ($page+1) :  ceil(($filedetails['searchcount'])/6))}}">
                                    >
                                </button>

                                Seite {{(!empty($_POST['page'])  ? round($_POST['page']) : 1)}}
                                von {{ceil(($filedetails['searchcount'])/6)}}

                            </div>
                            <h3 class="filealias float-left">
                                {{$filedetails['name']}}
                            </h3>
                            <div class="datensaetze">
                                {{$filedetails['searchcount']}} / {{$filedetails['datacount']}} Datensätze
                            </div>
                        </div>
                        <div class="clearfix "></div>

                    </div>
                    <div class="whitebox p-2">
                        <div class="datentabelle table-responsive ">
                            <table id="Filedata" data-toggle="table" class="table table-striped table-bordered">
                                <tr>
                                    <th>
                                        Zeilennr.
                                    </th>

                                    @foreach($filedetails['header'] AS $headkey => $col)
                                        @if($col != 'qx_zeilen_nr')
                                            <th>
                                                {{$col}}
                                            </th>
                                        @endif
                                    @endforeach
                                </tr>

                                @if(!empty($filedetails['data']))
                                    @foreach($filedetails['data'] AS $key => $zeile)
                                        <tr class='datenzeile'
                                            ondblclick="location.href = '{{url('/file/'.$filedetails['id'].'/daten/'.$zeile['qx_zeilen_nr'])}}?page={{(!empty($_POST['page']))? $_POST['page'] : 1}}&id_adressdaten={{$filedetails['id']}}&suche={{request('suche')}}&feld={{request('feld')}}';"
                                            onclick="$('.datenzeile').css('background-color', 'transparent'); $(this).css('background-color', '#f8eb81');">

                                            <td>
                                                {{intval($zeile['qx_zeilen_nr'])}}
                                            </td>

                                            @foreach($zeile AS $spaltenkey =>  $spalte)

                                                @if($spaltenkey != 'qx_zeilen_nr')
                                                    <td>
                                                        {{$spalte}}
                                                    </td>
                                                @endif
                                            @endforeach
                                        </tr>
                                    @endforeach
                                @endif
                            </table>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>

@endsection
