@extends('layouts.app')
@section('content')
    <div class="jobansicht">
        <?PHP $id_job = $jobinfos->id;?>
            <div id="ModalStop" class="modal fade">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Hinweis</h4>
                        </div>
                        <di class="p-3">
                        <h3 id="stopjobname">{{$jobinfos->jobnummer}}</h3>
                        stoppen?
                    <br>
                    <div class="float-right">
                        <input type="hidden" id="idstop" name="idstop" value="">
                        <button class="btn btn-primary float-right m-4" >Projekt stoppen</button>
                    </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div><!-- /.modal -->
        <div class="row flex-row whitebackground m-2 p-2  ">
            <div class="col-12 col-md-6 float-left">
                <h3>
                    {{$jobinfos->jobnummer}}
                </h3>
            </div>

            <div class=" float-right col-12 col-md-6  text-left text-md-right ">
                <a href="{{ url('jobdetails') }}/{{$jobinfos->id}}">
                    <button class="btn btn-primary">Zur Projektdetailansicht</button>
                </a>
                @if(session('userdata')['rechte']['typ']  == 'ersteller' )
                    <div class="float-right">
                        <a href="{{url('neuesprojekt').'/' . $jobinfos->id}}"><img
                                src="{{asset('images/einstellungen.svg')}}" class="einstellungen"/></a>
                    </div>
                @endif
            </div>
        </div>
        <div class="row d-flex p-1 m-2" >
            <div class=" col-12 col-md-8 float-left">
                <div>
                    <div class="von float-left">
                        Projektstart<br/>
                        <span class="zeiten">{{$jobinfos->von}}</span>
                    </div>
                    <div class="bis float-right">
                        Projektende<br/>
                        <span class="zeiten">{{$jobinfos->bis}}</span>
                    </div>
                    <div class="clear"></div>
                    <progress value="{{$jobinfos->fortschritt}}" max="{{$jobinfos->gesamt}}"
                              class="progressbar"></progress>
                </div>

            </div>
            <div class=" col-12 col-md-4 float-right">
                Projektdaten werden gelöscht in<br>
                <span class="zeiten">{{$jobinfos->bisloeschung}} Tagen</span>
                <div class="clear"></div>
                <progress value="{{$jobinfos->bisloeschunggesamt - $jobinfos->tageloeschung}}"
                          max="{{$jobinfos->bisloeschunggesamt}}" class="progressbar"></progress>
            </div>
        </div>


        <div class="clear"></div>
        <div class="">
            <div>
                <div class=" row flex-row    d-flex m-1 ">
                    <div class="whitebackground col-12 col-md-4 p-2  float-left tborder overflow-hidden">
                        <div>
                            <h3>Projektname</h3>
                        </div>
                        {{$jobinfos->jobbezeichnung}}
                        <div>
                            @if(!empty($jobinfos->preview_img))
                                <img src="data:image/jpg;base64,{{base64_encode($jobinfos->preview_img)}}" class="w-100" class="preview"  data-container="body"  title="{{$jobinfos->jobbezeichnung}}" data-toggle="popover" data-img="data:image/jpg;base64,{{base64_encode($jobinfos->preview_img)}}" />
                            @endif
                        </div>
                    </div>
                    <div class="whitebackground col-12 col-md-4 p-2  float-left tborder overflow-hidden">
                        <div>
                            <h3>Bestandteile / Versionen</h3>
                        </div>
                        <div>
                            @foreach($jobinfos->kategorien AS $kategorie)
                                <div>
                                    {{$kategorie->bezeichnung}}
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="whitebackground col-12 col-md-4 p-2 float-left tborder overflow-hidden">
                        <div>
                            <h3>Status/letzte Aktivität</h3>
                        </div>
                            @if($jobinfos['last_akt']['aktion'])
                            <div>{{$jobinfos['last_akt']['filename']}}</div>
                        @endif
                            <div>

                            {{(!empty($jobinfos['last_akt']['datetime']) ? \Carbon\Carbon::create($jobinfos['last_akt']['datetime'])->format('d.m.Y H:i') : \Carbon\Carbon::create($jobinfos->jobanlage)->format('d.m.Y H:i'))}}
                            </div>
                            @if($jobinfos['last_akt']['aktion'])
                            <div>{{$jobinfos['last_akt']['aktion']}}</div>
                            <div>{{$jobinfos['last_akt']['user']}}</div>
                                @else
                            <div>Angelegt</div>
                            @endif

                    </div>
                    <div class="clearfix"></div>

                </div>
            </div>
        </div>
            <div class="clear"></div>
            <div class="m-3">
                <div class="whitebackground p-4">
                    AUTO-MAILER STATUS

                    <div class="btn-sm btn-primary float-right " data-toggle="modal" data-target="#ModalStop" >Projekt stopen</div>
                    <div class="form-group  m-0 p-1 mr-3 float-right">
                        <label class="switch mt-0 float-left mr-2">
                            <input name='is_ki' {{((!empty($autojob->is_ki) && $autojob->is_ki == 1))?'checked=checked':''}} type='checkbox'   readonly>
                            <span class="slider round"></span>
                        </label> KI-Projekt
                    </div>
                </div>
            </div>
            <div class="clear"></div>
            <div class="  m-3">
                <div class="d-md-flex justify-content-between d-none">
                    <div class="col-md-2" >
                        In Bearbeitung
                        <h3>{{number_format($sumuploads,0,',', '.')}}</h3>
                    </div>
                    <div class="col-md-2">
                        PAL erfolgt
                        <h3>{{number_format($sumuploads,0,',', '.')}}</h3>
                    </div>
                    <div class="col-md-2">
                        Gelieferte Adressmenge
                        <h3>{{$jobinfos->gesamtmenge}}</h3>
                    </div>
                </div>
                <progress  value="{{$sumuploads}}" max="{{$jobinfos->gesamtmenge}}"
                          class="yellowpg"></progress>

            </div>
        <div class="clear"></div>
            <div class=" whitebackground m-3">
                <div class="d-md-flex justify-content-between d-none">
                    <div class=col-md-2 >
                        Start Adresslieferung
                    </div>
                    <div class=col-md-2>
                        Voraussichtlicher Endtermin
                    </div>
                    <div class=col-md-2>
                        Voraussichtlicher Endtermin
                    </div>
                    <div class=col-md-2>
                        Mindestmenge pro Druck-Step
                    </div>
                    <div class=col-md-2>
                        Letzte Uploads
                    </div>
                </div>
                <hr>
                <div class="d-md-flex justify-content-between">
                    <div class=col-md-2 >
                        <h4 class="d-md-none">Start Adresslieferung</h4>
                        {{(!empty($autojob->von) ? \Carbon\Carbon::parse($autojob->von)->format('d.m.Y') : '')}}
                    </div>
                    <div class=col-md-2>
                        <h4 class="d-md-none">Voraussichtlicher Endtermin</h4>
                        {{(!empty($autojob->von) ? \Carbon\Carbon::parse($autojob->bis)->format('d.m.Y') : '')}}
                    </div>
                    <div class=col-md-2>
                        <h4 class="d-md-none">Voraussichtlicher Endtermin</h4>
                        {!! (!empty($autojob->intervallausgabe) ? $autojob->intervallausgabe  :'')!!}
                    </div>
                    <div class=col-md-2>
                        <h4 class="d-md-none">Mindestmenge pro Druck-Step</h4>
                        {{(!empty($autojob->mindestmenge) ? $autojob->mindestmenge: '')}} Stück
                    </div>
                    <div class=col-md-2>
                        <h4 class="d-md-none">Letzte Uploads</h4>
                        @if(!empty($autojob["uploads"]))
                        @foreach($autojob["uploads"] AS $upload)
                            {{number_format($upload['count'],0,',','.')}} Stück | {{\Carbon\Carbon::parse($upload['datetime'])->format('d.m.Y')}}<br>
                        @endforeach
                            @endif

                    </div>
                </div>

            </div>
        <div class="benutzer">
        </div>
        <div class="whitebackground d-flex row m-2">
            <div class="kunden{{((session('userdata')['rechte']['typ']  == 'ersteller')? 'col-12 col-md-4 p-2 dgbackground wborder' : (session('userdata')['rechte']['typ']  == 'kunde'? 'col-12 col-md-6 p-2 dgbackground wborder' : 'd-none')) }} ">
                @if(session('userdata')['rechte']['typ']  != 'dienstleister' )
                    @if(!empty($jobinfos['kunden']))<h3>{{($jobinfos['kunden']['kunde'])}}</h3>@endif
                    <ul class="p-0 m-0">
                        @if(!empty($jobinfos['kunden']['kundenuser']))
                            @foreach($jobinfos['kunden']['kundenuser'] AS $kundenbenutzer)
                                <li class="benutzerblock whitebackground p-2 mb-1">
                                    <div
                                        class="benutzername">{{$kundenbenutzer['vorname']}} {{$kundenbenutzer['name']}}</div>
                                </li>
                            @endforeach
                        @endif
                    </ul>
                @endif
            </div>
            <div class="agentur{{((session('userdata')['rechte']['typ']  == 'ersteller')? 'col-12 col-md-4 p-2 dgbackground wborder': 'col-12 col-md-6 p-2 dgbackground wborder')}} ">
                <h3>team go direct</h3>
                <ul class="p-0 m-0">
                    @foreach($jobinfos->agentur AS $agentur)
                        <li class="benutzerblock whitebackground p-2 mb-1">
                            @if($agentur['aktiv'] == 1 && $agentur['rechte']['gesperrt'] == 0)
                            <div class="benutzername">{{$agentur['vorname']}} {{$agentur['name']}}</div>
                                @else
                                <div class="benutzername"><del>{{$agentur['vorname']}} {{$agentur['name']}}</del></div>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="dienstleister{{((session('userdata')['rechte']['typ']  == 'ersteller')? 'col-12 col-md-4 p-2 dgbackground wborder' : (session('userdata')['rechte']['typ']  == 'dienstleister'? 'col-12 col-md-6 p-2 dgbackground wborder' : 'd-none')) }} ">
                @if(session('userdata')['rechte']['typ']  != 'kunde' )
                    @if(!empty($jobinfos['dienstleisterinfo']))<h3>{{($jobinfos['dienstleisterinfo']['dienstleister'])}}</h3>@endif
                    <ul class="p-0 m-0">
                        @foreach($jobinfos->dienstleister AS $dienstleister)
                            <li class="benutzerblock whitebackground p-2 mb-1">
                                <div class="benutzername" >{{$dienstleister['vorname']}} {{$dienstleister['name']}}</div>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </div>

        </div>
        <div class="clear"></div>
        <div class="dateien">

@endsection
