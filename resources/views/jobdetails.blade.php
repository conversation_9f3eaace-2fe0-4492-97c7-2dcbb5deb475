@extends('layouts.app')
@section('content')
    <div class="jobdetails col-12 ">
        @if(!empty(session('error')))
            <div class="error">
                <?PHP echo session('error');
                session()->forget('error');
                ?>
            </div>
        @endif
        @if(!empty(session('succsess')))
            <div class="succsess">
                <?PHP
                echo session('succsess');
                session()->forget('succsess');
                ?>
            </div>
        @endif
        <div class="jobnummer col-12 p-2 row d-flex justify-content-between m-0">
            <?PHP $job_id = $jobinfos['id']; ?>

            <div class="small float-md-left col">
                {{$jobinfos['jobnummer']}}<a href="{{url('./job/'.$job_id)}}"> <&nbsp;zurück zur Projektansicht</a>
            </div>
            <div class="col-12 mt-sm-2  col-md">

                @if(session('userdata')['rechte']['typ'] == 'ersteller' )
                    <a href="{{url('neuesprojekt').'/' . $jobinfos['id']}}"><img
                            src="{{asset('images/einstellungen.svg')}}" class="einstellungen float-md-right"/></a>
                @endif

                @if(session('userdata')['rechte']['jobs'][$job_id]['upload'] == 1)
                    <div class="float-md-right">
                        <button onclick="jQuery('.absoften').show();jQuery('.uploadmodal').show(); " type="submit"
                                class="btn btn-primary d-flex-right">{!! file_get_contents(asset('images/upload.svg')) !!}
                            Datei uploaden
                        </button>
                    </div>
                @endif
            </div>
        </div>
        <div class="uploadmodal bg-red col-10 col-md-6" style="display: none;">
            <div onclick="jQuery('.absoften').hide();jQuery('.uploadmodal').hide();"
                 class="close">{!! file_get_contents(asset('images/cross.svg'))  !!}</div>
            Hinweis
            <div class="line"></div>
            <h2>Datei Upload</h2>
            @if(session('userdata')['rechte']['jobs'][$job_id]['upload'] == 1)
                <form action="{{ route('upload.store') }}" method="POST" enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <input type="hidden" id="jobid" name="jobid" value="{{$job_id}}">
                    <div class="beschreibung">Bitte wählen Sie ein Bestandteil aus:</div>
                    <div class="form-group select-wrapper">
                        <select class="form-control" id="katid" name="katid">
                            <option value="">Bestandteil...</option>
                            @foreach($jobinfos['kategorien'] As $kategorie)
                                <?PHP $id_kategorie = $kategorie['id']; ?>

                                <option value="{{$kategorie['id']}}">{{$kategorie['bezeichnung']}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="beschreibung">Bitte wählen Sie eine Datei:</div>
                    <div class="  p-0 m-0" >

                        <div class="custom-file float-left">
                            <div  id="upload-file-info" >Datei ...</div>
                        </div>

                        <label class="float-right  m-0 btn btn-primary" for="customFile" onclick="$('#upload-file-info').html('Datei ...');$('#uploadbtn').addClass('btn-basic');$('#uploadbtn').removeClass('btn-primary'); $('#uploadbtn').prop('disabled', true); ">
                            Datei auswählen</label>
                        <input type="file" class="form-control custom-file-input inputfile m-0 d-none" id="customFile"
                               name="filedata" placeholder="Upload Image">

                    </div>
                    <div class="clearfix"></div>
                    <button type="submit" class="btn btn-basic  m-0 float-right mt-2" id="uploadbtn" disabled="disabled">Upload</button>


                </form>
            @endif

        </div>
        <div class="achtungmodal  col-10 col-md-6" style="display: none;">
            <div onclick="jQuery('.absoften_rot').hide();jQuery('.achtungmodal').hide();"
                 class="close">{!! file_get_contents(asset('images/cross.svg'))  !!}</div>
            Hinweis
            <div class="line"></div>
            <h2>Datei Upload</h2>
           <div>
               <b>ACHTUNG</b>: Die hochgeladenen Datei enthält mehr als ein Tabellenblatt. Es wird nur das erste Tabellenblatt verwendet!
           </div>
        </div>
        <?PHP session()->remove('error');?>


            <div class="col-md-6 m-2 mt-4 p-0 float-left d-none d-md-block">
                <h3>Adressdaten</h3>
            </div>
            <div class="col-md-5 m-2 mt-4 p-0 float-left d-none d-md-block">
                <h3>PDF/Andrucke</h3>
            </div>
        @foreach($jobinfos['kategorien'] As $kategorie)

            <?PHP $id_kategorie = $kategorie['id']; ?>
            <div class="row filematching ">
                <div class="col-md-6 ">
                    <div class="d-block d-lg-none d-md-none d-sm-block d-xl-none" style="height: 30px; background-color: #eeeeee; border-radius: 0;"></div>
                    <div class="dateiuebersicht bg-white">
                        <div class="ueberschrift overflow-hidden">{{$kategorie['bezeichnung']}} <span class="d-block d-md-none small">(Adressdaten)</span></div>
                        <div class="clear"></div>
                        <ul class="alist col m-0 p-0">

                            @if(!empty($jobinfos['dateien']))
                                @foreach($jobinfos['dateien'] AS $file)
                                    <?PHP $id_file = $file['id'];
                                    $id_aktivitaet = 0;
                                    $break_akt = array(6,7,8);
                                    if ($file['id_kategorie'] != $id_kategorie) continue;
                                    foreach($jobinfos['aktivitaeten'][$file['id']] AS $akt_key =>  $cur_akt){

                                            if(in_array( $cur_akt['id_taetigkeit'], $break_akt)){
                                                $id_aktivitaet = $akt_key;
                                                break;
                                            }
                                    }

                                    $aktivitaet = $jobinfos['aktivitaeten'][$file['id']][$id_aktivitaet];

                                    $rolle = session('userdata')['rechte']['typ'];
                                    ?>
                                    <li class="col">
                                        <div class="dateiname @if(!empty($aktivitaet)) {{$aktivitaet->taetigkeit}} @endif "style="background-color: {{($rolle != 'ersteller'? ($rolle == 'kunde'? $aktivitaet['farbe_kunde'] : $aktivitaet['farbe_dienstleister'] ): $aktivitaet['farbe'] )}} !important;">
                                            @if(session('userdata')['rechte']['jobs'][$job_id]['ansehen'] == 1)
                                                <a href="{{url('file/'.$file->id)}}">{!! file_get_contents(asset('images/icons/dokument.svg'))!!} {{$file->alias_name}}</a>@else{!! file_get_contents(asset('images/icons/dokument.svg'))!!} {{$file->alias_name}}@endif
                                            <div class="klappen float-right" id="pfeil{{$id_file}}"
                                                 onclick="klappandtoogle({{$id_file}});">{!! file_get_contents(asset('images/spitzeoben.svg')) !!}</div>
                                        </div>
                                        <ul class="aktivitaeten  m-0 p-0" id="content{{$file['id']}}"
                                            style="display: none;">

                                            @foreach($jobinfos['aktivitaeten'][$file['id']] AS $aktivitaet)

                                                <li class="row">

                                                    <div
                                                        class=" col-4">{{\Carbon\Carbon::createFromTimeString($aktivitaet['datetime'])->format('d.m.y H:i')}}</div>
                                                    <div
                                                        class=" col-2">{{($rolle != 'ersteller'? ($rolle == 'kunde'? $aktivitaet['taetigkeit_kunde'] : $aktivitaet['taetigkeit_dienstleister']  ): $aktivitaet['taetigkeit'] )}}</div>
                                                    <div class=" col text-right"><?PHP
                                                        $user = \App\Models\User::find($aktivitaet['id_benutzer']);
                                                        $name = $user->vorname . " " . $user->name;
                                                        ?>
                                                        {{$name}}
                                                    </div>

                                                </li>
                                                <div class="clear"></div>
                                            @endforeach
                                        </ul>
                                    </li>
                                @endforeach
                            @endif
                        </ul>
                    </div>
                </div>

                <div class="col-md-6  ">

                    <div class="d-block d-lg-none d-md-none d-sm-block d-xl-none" style="height: 40px;"></div>
                    <div class=" dateiuebersicht bg-white pdf">
                        <div class="ueberschrift overflow-hidden">{{$kategorie['bezeichnung']}} <span class="d-block d-md-none small">(PDF/Andrucke)</span></div>
                        <div class="clear"></div>
                        <div>
                            <ul class="alist col m-0 p-0">

                                @if(!empty($jobinfos['bilddateien']))



                                    @foreach($jobinfos['bilddateien'] AS $key2 => $file2)

                                        <?PHP

                                        $id_file2 = $file2['id'];
                                        $id_aktivitaet = 0;
                                        $break_akt = array(6,7,8);
                                        if ($file2['id_kategorie'] != $id_kategorie) continue;
                                        foreach($jobinfos['aktivitaeten'][$file2['id']] AS $akt_key =>  $cur_akt){

                                            if(in_array( $cur_akt['id_taetigkeit'], $break_akt)){
                                                $id_aktivitaet = $akt_key;
                                                break;
                                            }
                                        }

                                        $aktivitaet = $jobinfos['aktivitaeten'][$file2['id']][$id_aktivitaet];
                                        #dd($aktivitaet);
                                        $rolle = session('userdata')['rechte']['typ'];
                                        ?>

                                        <li class="col">


                                            @if(!empty($aktivitaet))


                                                <div
                                                    class="dateiname @if(!empty($aktivitaet)) {{$aktivitaet->taetigkeit}} @endif"
                                                    style="background-color: {{($rolle != 'ersteller'? ($rolle == 'kunde'? $aktivitaet['farbe_kunde'] : $aktivitaet['farbe_dienstleister'] ): $aktivitaet['farbe'] )}} !important;">@if(session('userdata')['rechte']['jobs'][$job_id]['ansehen'] == 1)
                                                        <a href="{{url('file/'.$id_file2.'/daten/0')}}">{!! file_get_contents(asset('images/icons/dokument.svg'))!!} {{$file2->alias_name}}</a>@else{!! file_get_contents(asset('images/icons/dokument.svg'))!!} {{$file2->alias_name}}@endif
                                                    <div class="klappen" id="pfeil{{$id_file2}}"
                                                         onclick="klappandtoogle({{$id_file2}});">{!! file_get_contents(asset('images/spitzeoben.svg')) !!}</div>
                                                </div>

                                                <ul class="aktivitaeten m-0 p-0" id="content{{$id_file2}}"
                                                    style="display: none;">
                                                    @foreach($jobinfos['aktivitaeten'][$file2['id']] AS $aktivitaet)

                                                        <li class="row">

                                                            <div
                                                                class=" col-4">{{\Carbon\Carbon::createFromTimeString($aktivitaet['datetime'])->format('d.m.y H:i')}}</div>
                                                            <div
                                                                class=" col-2">{{($rolle != 'ersteller'? ($rolle == 'kunde'? $aktivitaet['taetigkeit_kunde'] : $aktivitaet['taetigkeit_dienstleister']  ): $aktivitaet['taetigkeit'] )}}</div>
                                                            <div class=" col text-right"><?PHP
                                                                $user = \App\Models\User::find($aktivitaet['id_benutzer']);
                                                                $name = $user->vorname . " " . $user->name;
                                                                ?>
                                                                {{$name}}
                                                            </div>

                                                        </li>
                                                    @endforeach
                                                </ul>
                                            @endif
                                        </li>
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
        <div class="clearfix" style="height: 100px;"></div>
        <script>
            $("#customFile").change(function () {
                document.getElementById('upload-file-info').innerHTML = document.getElementById('customFile').files[0].name;
            });


            $("#customFile").change(function () {
                if ($("#katid").val() && $("#customFile").val()) {
                    $("#uploadbtn").removeClass('btn-basic');
                    $("#uploadbtn").addClass('btn-primary');
                    $("#uploadbtn").prop('disabled', false);


                } else {
                    $("#uploadbtn").addClass('btn-basic');
                    $("#uploadbtn").removeClass('btn-primary');
                    $("#uploadbtn").prop('disabled', true);
                }

            });

            $("#katid").change(function () {
                if ($("#katid").val() && $("#customFile").val()) {
                    $("#uploadbtn").removeClass('btn-basic');
                    $("#uploadbtn").addClass('btn-primary');
                    $("#uploadbtn").prop('disabled', false);


                } else {
                    $("#uploadbtn").addClass('btn-basic');
                    $("#uploadbtn").removeClass('btn-primary');
                    $("#uploadbtn").prop('disabled', true);
                }
            });


            function klappandtoogle(elementid) {


                if (!$('#pfeil' + elementid).hasClass('rotate')) {
                    $('#pfeil' + elementid).toggleClass('rotate');
                    $('#content' + elementid).slideToggle();
                    $(document.body).animate({
                        scrollTop: $('#content' + elementid).offset().top
                    }, 2000);

                } else {
                    $('#pfeil' + elementid).toggleClass('rotate-reset');
                    $('#content' + elementid).slideToggle();
                }

            }


            setTimeout(function () {
                klappandtoogle({{$jobinfos['last_file']}})
            }, 500);

        </script>
@endsection
