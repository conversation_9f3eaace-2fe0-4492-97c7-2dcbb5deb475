@extends('layouts.app')
@section('content')
    <div class="col-12 p-0 m-0 ">
        <div class="d-flex flex-row p-0 m-0">
            <div class="col p-0 m-0 ">
                <div class="title align-middle">
                    <div class="">
                        <div class="p-2">
                            <a href="{{url('projekte')}}" style="font-size: 20px; color: #000;">
                                <i
                                        class="las la-angle-left border-grey"></i> #{{$job->id}}
                                - {{$job->jobbezeichnung}}
                            </a><br>
                            @if($job->copiedFrom)
                                <small class="ml-5">{{$job->copiedFrom}}</small>
                            @endif
                        </div>
                    </div>
                </div>
                @if((session('userdata')['rechte']['typ'] == 'ersteller') )
                        <?PHP $reg = 'autozahlen'; ?>
                    <div class="tagtabs">
                        <ul class="float-left">
                            <a href="{{url('freigabe/' . $job->id.'/tag/0')}}">
                                <li class="float-left {{(empty($reg)) ?'aktiv' : ''}}">Alle</li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/kunde' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='kunde') ?'aktiv' : ''}}">eprimo</li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/agentur' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='agentur') ?'aktiv' : ''}}">TGD</li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/salesforce' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='salesforce') ?'aktiv' : ''}}">{{config('app.CUSTOMER_CRM')}}</li>
                            </a>
                            <a href="{{url('autozahlen/' . $job->id )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='autozahlen') ?'aktiv' : ''}}">
                                    Automatisiert
                                </li>
                            </a>
                        </ul>
                    </div>
                    <div class="clearfix w-100"></div>
                @endif
                <div class="col m-1">
                    <div>
                        <h4>Übersicht</h4>
                        <div>
                            <div class="zahlenblock">
                                <i class="las la-list-ol float-left sfzahlen-icon"></i>
                                <span class="zahl_big">{{$summe_salesforce}}</span><br/>
                                Adressen aus {{config('app.CUSTOMER_CRM_AUS')}} {{config('app.CUSTOMER_CRM')}}
                            </div>
                        </div>
                        <div>
                            <div class="zahlenblock">
                                <i class="las la-envelope float-left postzahlen-icon"></i>
                                <span class="zahl_big">{{count($jobfiles)}}</span><br/>
                                Einlieferungen
                            </div>
                        </div>
                        {{--

                    <div>
                        <div class="zahlenblock">
                            <i class="las la-envelope float-left postzahlen-icon"></i>
                            <span class="zahl_big">21.930</span><br/>
                            Postauflieferungen
                        </div>
                    </div>
                    <div>
                        <div class="zahlenblock">
                            <i class="las la-times float-left nz-icon"></i>
                            <span class="zahl_big">760</span><br/>
                            Nicht zustellbar
                        </div>
                    </div>
                    --}}
                    </div>
                </div>
                <div class="clearfix w-100"></div>

                <div class="col-md-6 col-12 float-left">
                    @if(!empty($jobfiles))

                        @foreach($jobfiles AS $key => $jobfile)

                                <?PHP $pal = \App\Models\Verarbeitung::getPAL($jobfile->id_job, $jobfile->created_at, $jobfile->datensaetze);
                                $kosten = \App\Models\Verarbeitung::getKosten($jobfile->id_job, $jobfile->created_at, $jobfile->datensaetze);
                                ?>

                            <ul class="m-0 p-0">

                                <li>
                                    <div class="lieferbox {{( !empty($jobfile->stopzeit) ? 'gestoppt': '')}}">
                                        <div class="m-3">
                                            <h5 onclick="slidetoggle({{$jobfile->id}});"><i
                                                        class="{{  $key < 1 ? 'las la-angle-down': 'las la-angle-up' }}"
                                                        id="angle{{$jobfile->id}}"></i> #{{$job->id}}
                                                - {{$jobfile->step_id}} ({{($jobfile->created_at)->format('d.m.Y H:i')}}
                                                ) | {{number_format($jobfile->datensaetze, 0, ',', '.')}}
                                                Datensätze{{( !empty($jobfile->stopzeit) ? ' -  von ' . App('App\Http\Controllers\UserController')->getUserName($jobfile->id_stop_user) . ' am '.\Carbon\Carbon::parse($jobfile->stopzeit)->format('d.m.Y H:i').' gestoppt' : '')}} @if(empty($jobfile->stopzeit) )
                                                    | PAL: {{$pal}} | Kosten:
                                                    {{$kosten}}
                                                @endif</h5>
                                        </div>
                                        <div class="lieferinhalt"
                                             style="{{  $key < 1 ? 'display: block': 'display: none' }};"
                                             id="toggle{{$jobfile->id}}">

                                            <h4 class="p-3 mb-0 pb-0">vom Kunden</h4>
                                            <ul class="d-md-flex p-1 ml-3 m-0 autofiles">
                                                <li class="align-self-start col-md-6 col-12">
                                                    @if(empty($jobfile->geloescht_am))
                                                        <a href="{{url('file/'.$jobfile->id )}}"
                                                           target="_blank">{{$jobfile->org_name}}</a>
                                                        ({{number_format($jobfile->dateigroesse/(1024), 1, ',', '')}}KB)
                                                    @else
                                                        <span style="text-decoration: line-through">{{$jobfile->org_name}}</span>
                                                        <br/>
                                                        [gelöscht: {{\Carbon\Carbon::parse($jobfile->geloescht_am)->format('d.m.Y')}}
                                                        ]
                                                    @endif
                                                </li>
                                                <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($jobfile->created_at)->format('d.m.Y H:i')}}</li>
                                                <li class="align-self-start col-md-1 col-12">{{number_format($jobfile->datensaetze, 0, ',', '.')}}</li>

                                                <li class="align-self-start col-md-3 col-12">
                                                    <ul class="status p-0 m-0">
                                                        <li>
                                                            <div class="dot"></div>
                                                            übernommen
                                                        </li>
                                                    </ul>
                                                </li>


                                            </ul>
                                            <h4 class="p-3  mb-0 pb-0">an Dienstleister</h4>
                                            @if(!empty($jobfile->id_job))
                                                    <?PHP $druckdateien = App\Models\Druckdateien::Where('id_job', $jobfile->id_job)->Where('step_id', $jobfile->step_id)->orderBy('created_at', 'DESC')->get(['id', 'file_name', 'uebergabe_dienstleister', 'created_at', 'dateigroesse', 'geloescht_am', 'created_at']); ?>
                                                @foreach($druckdateien AS $druckdatei)
                                                    <ul class="d-md-flex  p-1 ml-3  m-0 autofiles">
                                                        <li class="align-self-start col-md-6 col-12">
                                                            @if(empty($druckdatei->geloescht_am))
                                                                <a href="{{url((strpos($druckdatei->file_name, '.pdf') !== false ? 'druckpreview/' : 'csvdateiview/').$druckdatei->id )}}"
                                                                   target="_blank">{{$druckdatei->file_name}}</a>
                                                                ({{number_format(($druckdatei->dateigroesse)/(1024), 1, ',', '')}}
                                                                KB)
                                                            @else
                                                                <span style="text-decoration: line-through">{{$druckdatei->file_name}}</span>
                                                                <br/>
                                                                [gelöscht: {{\Carbon\Carbon::parse($druckdatei->geloescht_am)->format('d.m.Y')}}
                                                                ]
                                                            @endif
                                                        </li>
                                                        <li class="align-self-start col-md-2 col-12">{{(!empty($druckdatei->uebergabe_dienstleister) ? \Carbon\Carbon::parse($druckdatei->uebergabe_dienstleister)->format('d.m.Y H:i') : '-')}}</li>
                                                        <li class="align-self-start col-md-1 col-12"></li>

                                                        <li class="align-self-start col-md-3 col-12">
                                                            <ul class="status  p-0 m-0">
                                                                <li>
                                                                    <div class="dot"></div>
                                                                    erzeugt
                                                                </li>
                                                                <li>
                                                                    <div class="{{(!empty($druckdatei->uebergabe_dienstleister) ? 'dot' : 'dot-red')}}"></div>
                                                                    übergeben
                                                                </li>
                                                            </ul>
                                                        </li>

                                                    </ul>
                                                    <div class="clearfix"></div>
                                                @endforeach
                                            @endif

                                            <h4 class="p-3  mb-0 pb-0">vom Dienstleister</h4>
                                            @if(!empty($jobfile->id_job))
                                                    <?PHP $responsedateien = App\Models\Responses::Where('id_job', $jobfile->id_job)->Where('step_id', $jobfile->step_id)->orderBy('created_at', 'DESC')->get(['id', 'file_name', 'created_at', 'dateigroesse']); ?>
                                                @foreach($responsedateien AS $responsedatei)
                                                    <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                        <li class="align-self-start col-md-6 col-12"><a
                                                                    href="{{url('/showresponse/'.$responsedatei->id)}}"
                                                                    target="_blank">{{$responsedatei->file_name}}</a>
                                                            ({{number_format($responsedatei->dateigroesse/(1024), 1, ',', '')}}
                                                            KB)
                                                        </li>
                                                        <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($responsedatei->created_at)->format('d.m.Y H:i')}}</li>
                                                        <li class="align-self-start col-md-1 col-12"></li>

                                                        <li class="align-self-start col-md-3 col-12">
                                                            <ul class="status p-0 m-0">
                                                                <li>
                                                                    <div class="dot"></div>
                                                                    übernommen
                                                                </li>
                                                            </ul>
                                                        </li>

                                                    </ul>
                                                    <div class="clearfix"></div>
                                                @endforeach
                                            @endif

                                            <h4 class="p-3  mb-0 pb-0">an Kunde</h4>
                                            <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                <li class="align-self-start col-md-6 col-12">
                                                    @php
                                                        $transmitDiff = \App\Models\Transmitdate::getDiff($jobfile->id_job , $jobfile->step_id);
                                                        $transmitDate = \App\Models\Transmitdate::getTransmitDate($jobfile->id_job , $jobfile->step_id);
                                                    @endphp
                                                    <form>
                                                        <select class="form-control col-12 float-left"
                                                                onchange="setTransmitdate({{$jobfile->id_job}} , {{$jobfile->step_id}}, $(this).val())" {{ \Carbon\Carbon::parse($transmitDate)->format('Ymd') > \Carbon\Carbon::now()->format('Ymd') ? '' : 'disabled="disabled"' }}>
                                                            <option value="-2" {{$transmitDiff  == -2 ? 'selected= selected' : '' }}>
                                                                2 Tage vor PAL
                                                            </option>
                                                            <option value="-1" {{$transmitDiff  == -1 ? 'selected= selected' : '' }}>
                                                                1 Tag vor PAL
                                                            </option>
                                                            <option value="0" {{$transmitDiff  == 0 ? 'selected= selected' : '' }}>
                                                                PAL
                                                            </option>
                                                            <option value="+1" {{$transmitDiff  == 1 ? 'selected= selected' : '' }}>
                                                                1 Tag nach PAL
                                                            </option>
                                                        </select>
                                                    </form>
                                                </li>
                                                <li class="align-self-start col-md-2 col-12">
                                                    <ul></ul>
                                                </li>
                                                <li class="align-self-start col-md-1 col-12"></li>

                                                <li class="align-self-start col-md-3 col-12">
                                                    <ul class="status p-0 m-0">
                                                        {{ !empty($transmitDate) ? \Carbon\Carbon::parse($transmitDate)->format('d.m.Y') : '' }}
                                                    </ul>
                                                </li>

                                            </ul>
                                            @if(!empty($jobfile->id_job))
                                                    <?PHP
                                                    $count_earchiv = \App\Models\Einzelhashdateien::Where('step_id', $jobfile->step_id)->Where('id_job', $jobfile->id_job)->get(['id'])->count();
                                                    $last_earchiv = \App\Models\Einzelhashdateien::Where('step_id', $jobfile->step_id)->Where('id_job', $jobfile->id_job)->orderby('created_at', 'desc')->get(['created_at'])->first();
                                                    ?>
                                                <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                    <li class="align-self-start col-md-6 col-12">Archivdateien
                                                        ({{$count_earchiv}}/{{$jobfile->datensaetze}} Stück)
                                                    </li>
                                                    <li class="align-self-start col-md-2 col-12">

                                                        @if(!empty($last_earchiv) && $count_earchiv >= $jobfile->datensaetze)
                                                            {{\Carbon\Carbon::parse($last_earchiv->created_at)->format('d.m.Y H:i')}}
                                                        @elseif($count_earchiv != 0 &&  $count_earchiv < $jobfile->datensaetze)
                                                            {{\Carbon\Carbon::now()->format('d.m.Y H:i')}} (Stand)
                                                        @else
                                                            -
                                                        @endif
                                                    </li>
                                                    <li class="align-self-start col-md-1 col-12"></li>

                                                    <li class="align-self-start col-md-3 col-12">
                                                        <ul class="status p-0 m-0">
                                                            <li>
                                                                @if($count_earchiv >= $jobfile->datensaetze)
                                                                    <div class="dot"></div> Generierung abgeschlossen
                                                                @elseif($count_earchiv != 0 &&  $count_earchiv < $jobfile->datensaetze)
                                                                    <div class="dot-orange"></div> Generierung gestartet
                                                                @else
                                                                    <div class="dot-red"></div> Generierung ausstehend
                                                            @endif
                                                        </ul>
                                                    </li>

                                                </ul>
                                                <div class="clearfix"></div>
                                                    <?PHP
                                                    $count_archiv = DB::table('transmits')
                                                        ->leftJoin('hashdateien_einzeldatei', 'hashdateien_einzeldatei.id', '=', 'transmits.id_datei')
                                                        ->Where('id_liefertyp', 1)
                                                        ->Where('hashdateien_einzeldatei.step_id', $jobfile->step_id)
                                                        ->Where('hashdateien_einzeldatei.id_job', $jobfile->id_job)
                                                        ->Where('transmits.status_einlieferung', '202')
                                                        ->Where('transmits.id_status', '1')
                                                        ->get(['transmits.id'])
                                                        ->count();
                                                    $last_archiv = DB::table('transmits')
                                                        ->leftJoin('hashdateien_einzeldatei', 'hashdateien_einzeldatei.id', '=', 'transmits.id_datei')
                                                        ->Where('id_liefertyp', 1)
                                                        ->Where('hashdateien_einzeldatei.step_id', $jobfile->step_id)
                                                        ->Where('hashdateien_einzeldatei.id_job', $jobfile->id_job)
                                                        ->Where('transmits.status_einlieferung', '202')
                                                        ->Where('transmits.id_status', '1')
                                                        ->orderby('transmits.antwort_am', 'desc')
                                                        ->get(['transmits.antwort_am AS antwort_am'])
                                                        ->first();
                                                    ?>
                                                <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                    <li class="align-self-start col-md-6 col-12">Archivdateien
                                                        ({{$count_archiv}}/{{$jobfile->datensaetze}} Stück)
                                                    </li>
                                                    <li class="align-self-start col-md-2 col-12">
                                                        @if(!empty($last_archiv) &&  $count_archiv >= $jobfile->datensaetze)
                                                            {{\Carbon\Carbon::parse($last_archiv->antwort_am)->format('d.m.Y H:i')}}
                                                        @elseif($count_archiv != 0 &&  $count_archiv < $jobfile->datensaetze)
                                                            {{\Carbon\Carbon::now()->format('d.m.Y H:i')}} (Stand)
                                                        @else
                                                            -
                                                        @endif

                                                    </li>
                                                    <li class="align-self-start col-md-1 col-12"></li>

                                                    <li class="align-self-start col-md-3 col-12">
                                                        <ul class="status p-0 m-0">
                                                            <li>
                                                                @if($count_archiv >= $jobfile->datensaetze)
                                                                    <div class="dot"></div> Übergabe abgeschlossen
                                                                @elseif($count_archiv != 0 &&  $count_archiv < $jobfile->datensaetze)
                                                                    <div class="dot-orange"></div> Übergabe gestartet
                                                                @else
                                                                    <div class="dot-red"></div> Übergabe ausstehend
                                                                @endif
                                                            </li>
                                                        </ul>
                                                    </li>

                                                </ul>
                                                <div class="clearfix"></div>
                                            @endif
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        @endforeach
                    @endif

                </div>
                <div class="col-md-6 col-12 float-right p-1 mt-1">
                    @if(!empty($jobfile))
                        @if(session('userdata')['rechte']['typ'] == 'ersteller')
                            <div class="col-12  h-100 p-0 m-1">
                                <div class="col-12 bg-white responsebox m-2 ">
                                    <h4>Upload (PWL für
                                        Drucker) </h4>
                                        <?PHP $pwldateien = App\Models\PWLs::Where('id_job', $job->id)->Where(function ($q) {
                                        $q->whereNull('step_id')->orWhere('step_id', '');
                                    })->orderBy('created_at', 'DESC')->get(['file_name', 'created_at', 'dateigroesse', 'geloescht_am']); ?>
                                    @if(count($pwldateien) != 0 )
                                        @foreach($pwldateien AS $pwldatei)
                                            <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                <li class="align-self-start col-md-6 col-12">
                                                    @if(empty($pwldatei->geloescht_am))
                                                        {{$pwldatei->file_name}}
                                                        ({{number_format(($pwldatei->dateigroesse)/(1024), 1, ',', '')}}
                                                        KB)
                                                    @else
                                                        <span style="text-decoration: line-through">{{$pwldatei->file_name}}</span>
                                                        <br/>
                                                        [gelöscht: {{\Carbon\Carbon::parse($pwldatei->geloescht_am)->format('d.m.Y')}}
                                                        ]
                                                    @endif
                                                </li>
                                                <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($pwldatei->created_at)->format('d.m.Y H:i')}}</li>
                                                <li class="align-self-start col-md-1 col-12"></li>

                                                <li class="align-self-start col-md-3 col-12">
                                                    <ul class="status p-0 m-0">
                                                        <li>
                                                            <div class="dot"></div>
                                                            übergeben
                                                        </li>
                                                    </ul>
                                                </li>

                                            </ul>
                                            <div class="clearfix"></div>
                                        @endforeach
                                    @else
                                        <div>Es wurden noch keine PWL-Liste hochgeladen.</div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
                <div class="col-md-6 col-12 float-right  p-1">
                    @if(!empty($jobfile))
                        <div class="col-12 bg-white responsebox h-100 m-3 ">
                            <h4>Response</h4>
                                <?PHP $responsedateien = App\Models\Responses::Where('id_job', $jobfile->id_job)->Where(function ($q) {
                                $q->whereNull('step_id')->orWhere('step_id', '');
                            })->orderBy('created_at', 'DESC')->get(['id', 'file_name', 'created_at', 'dateigroesse', 'geloescht_am']); ?>
                            @foreach($responsedateien AS $responsedatei)
                                <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                    <li class="align-self-start col-md-6 col-12">
                                        @if(empty($responsedatei->geloescht_am))
                                            {{$responsedatei->file_name}}
                                            ({{number_format(($responsedatei->dateigroesse)/(1024), 1, ',', '')}} KB)
                                        @else
                                            <span style="text-decoration: line-through">{{$responsedatei->file_name}}</span>
                                            <br/>
                                            [gelöscht: {{\Carbon\Carbon::parse($responsedatei->geloescht_am)->format('d.m.Y')}}
                                            ]
                                        @endif
                                        <ul class="small">
                                                <?PHP
                                                $responseHistory = App\Models\ResponseDownload::Where('id_Response', $responsedatei->id)->orderBy('datetime', 'DESC')->get();
                                                ?>

                                            @foreach($responseHistory AS $responseHistory)
                                                <li>Download
                                                    am {{  \Carbon\Carbon::parse($responseHistory->datetime)->format('d.m.Y H:i') }}
                                                    von {{  \App\Http\Controllers\UserController::getUserName($responseHistory->id_user) }}</li>
                                            @endforeach
                                        </ul>
                                    </li>

                                    <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($responsedatei->created_at)->format('d.m.Y H:i')}}</li>
                                    <li class="align-self-start col-md-1 col-12"></li>

                                    <li class="align-self-start col-md-3 col-12">
                                        <ul class="status p-0 m-0">
                                            <li>
                                                <div class="dot"></div>
                                                übernommen
                                            </li>
                                        </ul>
                                    </li>

                                </ul>
                                <div class="clearfix"></div>
                            @endforeach
                                <?PHP
                                $count_einzeln = App\Models\Einzelresponses::Where('id_job', $jobfile->id_job)->get('id')->count();
                                $count_einzeln_last = App\Models\Einzelresponses::Where('id_job', $jobfile->id_job)->orderby('created_at', 'desc')->get(['id', 'created_at'])->first();

                                ?>
                            <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                <li class="align-self-start col-md-6 col-12">Responses - OK ({{$count_einzeln}}Stück)
                                </li>
                                <li class="align-self-start col-md-2 col-12">
                                    @if(!empty($count_einzeln_last) && $count_einzeln > 0)
                                        {{\Carbon\Carbon::parse($count_einzeln_last->created_at)->format('d.m.Y H:i')}}
                                    @else
                                        -
                                    @endif
                                </li>
                                <li class="align-self-start col-md-1 col-12"></li>

                                <li class="align-self-start col-md-3 col-12">
                                    <ul class="status p-0 m-0">
                                        <li>
                                            <div class="{{ $count_einzeln > 0  ? 'dot' : 'dot-red'}}"></div>
                                            übernommen
                                        </li>
                                    </ul>
                                </li>

                            </ul>
                            <div class="clearfix"></div>
                            <div class="clearfix"></div>
                                <?PHP
                                $count_resok = DB::table('transmits')->leftJoin('responsedateien_einzeldateien', 'responsedateien_einzeldateien.id', '=', 'transmits.id_datei')->Where('id_liefertyp', 2)->Where('responsedateien_einzeldateien.id_job', $jobfile->id_job)->Where('transmits.status_einlieferung', '202')->Where('transmits.id_status', '1')->get(['transmits.id'])->count();
                                $last_resok = DB::table('transmits')->leftJoin('responsedateien_einzeldateien', 'responsedateien_einzeldateien.id', '=', 'transmits.id_datei')->Where('id_liefertyp', 2)->Where('responsedateien_einzeldateien.id_job', $jobfile->id_job)->Where('transmits.status_einlieferung', '202')->Where('transmits.id_status', '1')->orderby('transmits.antwort_am', 'desc')->get(['transmits.antwort_am AS antwort_am'])->first();
                                ?>
                            <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                <li class="align-self-start col-md-6 col-12">Response - OK ({{$count_resok}}
                                    / {{$count_einzeln}} Stück)
                                </li>
                                <li class="align-self-start col-md-2 col-12">
                                    @if(!empty($last_resok) && $count_resok > 0)
                                        {{\Carbon\Carbon::parse($last_resok->antwort_am)->format('d.m.Y H:i')}}
                                    @else
                                        -
                                    @endif
                                </li>
                                <li class="align-self-start col-md-1 col-12"></li>

                                <li class="align-self-start col-md-3 col-12">
                                    <ul class="status p-0 m-0">
                                        <li>
                                            @if( $count_resok != 0 && ($count_resok >= $count_einzeln))
                                                <div class="dot"></div> Übergabe abgeschlossen
                                            @elseif($count_resok != 0 &&  $count_resok < $count_einzeln)
                                                <div class="dot-orange"></div> Übergabe gestartet
                                            @else
                                                <div class="dot-red"></div> Übergabe ausstehend
                                            @endif
                                        </li>

                                    </ul>
                                </li>

                            </ul>
                        </div>
                    @endif
                </div>
            </div>


        </div>
    </div>
    {{--
        @if(session('userdata')['rechte']['typ'] == 'ersteller')
            <div class="col p-3 fixed-bottom projektfooter" style="margin-left: {{(!empty(session('sidebarwidth'))) ?  session('sidebarwidth') : '162'}}px; display:none;" >
                <ul class="m-1 p-0 float-left">
                    <li class="no-border">Projektinformation:</li>
                    <li ><i class="las la-calendar icon-blue"></i> Projektstart: <span class="werte">{{\Carbon\Carbon::parse($job->jobstart)->format('d.m.Y')}}</span></li>
                    <li><i class="las la-calendar icon-blue"></i> Projektende: <span class="werte">{{\Carbon\Carbon::parse($job->jobende)->format('d.m.Y')}}</span></li>
                    <li><i class="las la-list-ol icon-blue"></i> Geplante Adressmenge: <span class="werte">{{number_format($job->gesamtmenge, 0, ',', '.')}}</span></li>
                    <li><i class="las la-list-ol icon-blue"></i> Mindestmenge pro Druck-Step: <span class="werte">{{number_format($autojob->mindestmenge, 0, ',', '.')}}</span></li>
                    <li><i class="las la-calendar icon-blue"></i> Start Adresslieferung: <span class="werte">{{\Carbon\Carbon::parse($autojob->von)->format('d.m.Y')}}</span></li>
                    <li><i class="las la-list-ol icon-blue"></i> Adresslieferung: <span class="werte">{{\Carbon\Carbon::parse($autojob->bis)->format('d.m.Y')}}</span></li>
                </ul>
                <div class="clearfix"></div>
                <ul class="m-1 p-0 float-left">
                    <li class="no-border">Benutzer:</li>
                    @if(!empty($users))
                        @foreach($users AS $user)
                            <li ><div class="text-blue">{{substr($user->vorname, 0, 1)}}{{substr($user->name,0,1)}}</div>&nbsp;{{$user->vorname}} {{$user->name}}</li>
                        @endforeach
                    @endif
                </ul>
            </div>
        @endif
    --}}

    <script>

        $(document).ready(function () {
            $('#dashboardtable').DataTable({
                "order": [[4, "desc"], [3, "desc"]],
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Keine aktuellen Projekte vorhanden."
                }
            });
        });


        function klappandtoogle(elementid) {


            if (!$('#pfeil' + elementid).hasClass('rotate')) {


                $('#pfeil' + elementid).toggleClass('rotate');
                $('#content' + elementid).slideToggle();
            } else {
                $('#pfeil' + elementid).toggleClass('rotate-reset');
                $('#content' + elementid).slideToggle();
            }

        }

        function slidetoggle(elementid) {
            if ($('#angle' + elementid).hasClass('la-angle-up')) {
                $('#toggle' + elementid).slideToggle();
                $('#angle' + elementid).removeClass('la-angle-up');
                $('#angle' + elementid).addClass('la-angle-down');
            } else {
                $('#toggle' + elementid).slideToggle();
                $('#angle' + elementid).removeClass('la-angle-down');
                $('#angle' + elementid).addClass('la-angle-up');
            }

        }

        // popovers initialization - on hover
        $('[data-toggle="popover"]').popover({
            html: true,
            trigger: 'click',
            placement: 'left',
            container: 'body',
            content: function () {
                return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />';
            }
        });

        function setTransmitdate(jobId, stepId, diff) {
            $.post('./settransmitdate/' + jobId,
                {
                    "_token": "{{ csrf_token() }}",
                    jobId: jobId,
                    stepId: stepId,
                    diff: diff
                },
                function (data) {
                    location.reload();
                }
            );
        }

        $(document).on("click", ".popover .closer", function () {
            $(this).parents(".popover").popover('hide');
        });
        @if(!empty($id_doc)) $('#sidebar_right').animate({width: 'toggle'}, 350);@endif
    </script>

@endsection
