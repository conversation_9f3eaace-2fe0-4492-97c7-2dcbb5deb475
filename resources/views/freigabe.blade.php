@extends('layouts.app')
@section('content')
    @if(!empty(session('error')))
        <div class="error">
                <?PHP echo session('error');
                session()->forget('error');
                ?>
        </div>
    @endif
    <div class="col-12 p-0 m-0 ">
        <div class="d-flex flex-row p-0 m-0">
            <div class="col p-0 m-0 ">
                <div class="title align-middle">
                    @if($reg=='salesforce' && !empty($jobfiles[0]) && empty($jobfiles[0]->stopzeit))
                        <div id="clockdiv" class="float-righ m-1">
                            <div>
                                <span class="hours float-left p-2 m-2 bg-light "></span>
                            </div>
                            <div>
                                <span class="minutes float-left p-2 m-2 bg-light "></span>
                            </div>
                            <div>
                                <span class="seconds float-left p-2 m-2 bg-light "></span>
                            </div>
                            <button class="seconds float-left p-2 m-2 bg-light btn btn-default " data-toggle=modal
                                    data-target=#StoppenModal style="height: 35px;">stoppen
                            </button>

                        </div>
                        <script>
                            function getTimeRemaining(endtime) {
                                const total = Date.parse(endtime) - Date.parse(new Date());
                                const seconds = Math.floor((total / 1000) % 60);
                                const minutes = Math.floor((total / 1000 / 60) % 60);
                                const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
                                const days = Math.floor(total / (1000 * 60 * 60 * 24));

                                return {
                                    total,
                                    days,
                                    hours,
                                    minutes,
                                    seconds
                                };
                            }

                            function initializeClock(id, endtime) {
                                const clock = document.getElementById(id);
                                const hoursSpan = clock.querySelector('.hours');
                                const minutesSpan = clock.querySelector('.minutes');
                                const secondsSpan = clock.querySelector('.seconds');

                                function updateClock() {
                                    const t = getTimeRemaining(endtime);

                                    hoursSpan.innerHTML = ('0' + t.hours).slice(-2);
                                    minutesSpan.innerHTML = ('0' + t.minutes).slice(-2);
                                    secondsSpan.innerHTML = ('0' + t.seconds).slice(-2);

                                    if (t.total <= 0) {
                                        $('#clockdiv').hide();
                                        clearInterval(timeinterval);

                                    }
                                }

                                updateClock();
                                const timeinterval = setInterval(updateClock, 1000);
                            }

                            const deadline = new Date('{{\Carbon\Carbon::parse($jobfiles[0]->created_at)->addMinutes(config('timings.send_delay'))->format('Y/m/d H:i:s')}}');
                            console.log(deadline);
                            initializeClock('clockdiv', deadline);
                        </script>
                    @endif
                    <div class="">
                        <div class="p-2">
                            <a href="{{url('projekte')}}" style="font-size: 20px; color: #000;">
                                <i
                                        class="las la-angle-left border-grey"></i> #{{$job->id}}
                                - {{$job->jobbezeichnung}}

                            </a><br>
                            @if($job->copiedFrom)
                                <small class="ml-5">{{$job->copiedFrom}}</small>
                            @endif
                        </div>
                    </div>


                </div>
                @if((session('userdata')['rechte']['typ'] === 'ersteller') )
                    <div class="tagtabs">
                        <ul class="float-left">
                            <a href="{{url('freigabe/' . $job->id.'/tag/0')}}">
                                <li class="float-left {{(empty($reg)) ?'aktiv' : ''}}">Alle</li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/salesforce' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='salesforce') ?'aktiv' : ''}}">
                                    {{config('app.CUSTOMER_CRM')}}
                                </li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/marketingcloud' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='marketingcloud') ?'aktiv' : ''}}">
                                    Beladung MC
                                </li>
                            </a>
                            <a href="{{url('autozahlen/' . $job->id )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='autozahlen') ?'aktiv' : ''}}">
                                    Automatisiert
                                </li>
                            </a>
                        </ul>
                    </div>
                    <div class="clearfix"></div>
                @endif
                @if((session('userdata')['rechte']['typ'] === 'kunde') )
                    <div class="tagtabs">
                        <ul class="float-left">
                            <a href="{{url('freigabe/' . $job->id.'/tag/0')}}">
                                <li class="float-left {{(empty($reg)) ?'aktiv' : ''}}">Jobübersicht</li>
                            </a>
                            <a href="{{url('freigabe/' . $job->id.'/reg/marketingcloud' )}}">
                                <li class="float-left {{(!empty($reg) && $reg=='marketingcloud') ?'aktiv' : ''}}">
                                    Beladung MC
                                </li>
                            </a>
                        </ul>
                    </div>
                    <div class="clearfix"></div>
                @endif
                <div class="filter">
                    @if($job->fuer_kunde_frei == 1 || (session('userdata')['rechte']['typ'] == 'ersteller') )
                        @if(!$job->freigegeben)
                            <div class="hinweis d-md-block d-none float-right pr-3">
                                {{--
                                @if((session('userdata')['rechte']['typ'] != 'ersteller') )
                                {{$count}} Dokumente | {{($count - count($freigaben['kunde']))}}
                                @else
                                    {{$count}} Dokumente | {{($count - count($freigaben['agentur']))}}
                                @endif

                                Dokumente nicht freigegeben

                                --}}

                                @if($count != 0 && (session('userdata')['rechte']['typ'] != 'ersteller') && session('userdata')['rechte']['jobs'][$job->id]['sf_freigabe'] == 1 && $job->in_bearbeitung != 1 && ($allowCRMRelease))
                                    &nbsp;
                                    <div
                                            class="{{( ($count == count($freigaben['kunde'])) ? 'freigabe_moeglich pointer' : 'keine_freigabe_moeglich')}}  float-right "
                                            {{( ($count == count($freigaben['kunde'])) ? ' data-toggle=modal data-target=#FreigabeModal ' : '')}}>
                                        Projekt
                                        für {{config('app.CUSTOMER_CRM_ARTIKEL')}} {{config('app.CUSTOMER_CRM')}}
                                        freigeben
                                    </div>
                                @endif

                                @if($count != 0 && (session('userdata')['rechte']['typ'] == 'ersteller') &&  ($job->fuer_kunde_frei != 1 || $job->in_bearbeitung == 1)  && session('userdata')['rechte']['jobs'][$job->id]['freigabe']  )
                                    <div
                                            class="{{( ($count <= (count($freigaben['agentur']) - count($ablehnungen['kunde'])) && !empty($job->sf_preview) && ($allowCustomerRelease))  ? 'freigabe_moeglich pointer' : 'keine_freigabe_moeglich')}}  float-right "
                                            {{(( $count <= (count($freigaben['agentur']) - count($ablehnungen['kunde']))) && !empty($job->sf_preview) && ($allowCustomerRelease) ? ' data-toggle=modal data-target=#FreigabeKundenModal ' : '')}}>
                                        Projekt für Kunden freigeben
                                    </div>
                                @endif
                            </div>
                        @endif
                </div>
                <div class="clearfix"></div>
                @if($job->freigegeben )
                    <div class="d-flex flex-row m-4">
                        <div class="flex col-8 in_salesforce float-left  overflow-hidden"><i
                                    class="las la-bell"></i> Dieses Projekt ist aktuell
                            in {{config('app.CUSTOMER_CRM_AUS')}} {{config('app.CUSTOMER_CRM')}} aktiviert und kann
                            nicht
                            bearbeitet werden.
                        </div>
                        <div class="col-4 float-left in_salesforce overflow-hidden"
                             style=" background-color: #FFFFFF; color: #888888; ">
                            <i class="las la-check checkblue"></i>&nbsp;Freigabe {{$freigabeuser}}
                            | {{\Carbon\Carbon::parse($job->freigabedatum)->format('d.m.Y H:i:s')}}</div>
                    </div>
                @endif
                @if($count == count($freigaben['agentur']) && empty($job->sf_preview) && session('userdata')['rechte']['typ'] == 'ersteller')
                    <div class=" col m-4 no_preview">
                        <i class="las la-bell"></i> Dieses Projekt hat noch keine erzeugte Preview und kann somit
                        nicht
                        für den Kunden freigegeben werden.
                    </div>
                @endif
                <div class="d-flex flex-row p-0 m-0">
                    <div class="col">
                        <div id="StopModal" class="modal fade">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        Hinweis
                                    </div>
                                    <div class="modal-body">
                                        <h4>Möchten Sie wirklich dieses Projekt stoppen oder pausieren?</h4>
                                        <h4 id="stopjobname" style="color: #1b4b72;"></h4>
                                        <div class="float-right ">
                                            <input type="hidden" id="idstop" name="idstop" value="">


                                        </div>
                                        <div class="modal-footer mt-4">
                                            <button
                                                    onclick="$('.modal-backdrop').hide();$('#StopModal').hide();return false;"
                                                    class="btn btn-primary float-right ">Nein, zurück
                                            </button>
                                            <button onclick="$('.modal-backdrop').hide();$('#StopModal').hide();"
                                                    class="btn btn-primary float-right ">Ja, Projekt stoppen
                                            </button>
                                        </div>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->
                        <div id="FreigabeModal" class="modal fade col">
                            <form action="{{asset('jobfreigabe/')}}" method="post" name="FreigabeModalForm"
                                  id="FreigabeModalForm">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten dieses Projekt
                                                für {{config('app.CUSTOMER_CRM_ARTIKEL')}} {{config('app.CUSTOMER_CRM')}}
                                                freigeben?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_freigabe" name="id_freigabe"

                                                       value="{{$job->id}}">

                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeModal').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>
                                                &nbsp;
                                                &nbsp;
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeModal').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="StoppenModal" class="modal fade col">
                            <form action="{{asset('uebergabestoppen/')}}" method="post" name="StoppenModalForm"
                                  id="StoppenModalForm">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten die Übergabe stoppen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_stoppen" name="id_stoppen"

                                                       value="{{(!empty($jobfiles[0]) ?  $jobfiles[0]->id : '')}}">

                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#StoppenModal').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>
                                                &nbsp;
                                                &nbsp;
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#StoppenModal').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FreigabeKundenModal" class="modal fade col">
                            <form action="{{asset('jobKfreigabe/')}}" method="post" name="FreigabeKundenModalForm"
                                  id="FreigabeKundenModalForm">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten dieses Projekt für den Kunden freigeben?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_freigabe" name="id_freigabe"
                                                       value="{{$job->id}}">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeKundenModal').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>
                                                &nbsp;
                                                &nbsp;
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeKundenModal').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="BearbeitenModal" class="modal fade col">
                            <form action="{{url('reset_document')}}" method="post" name="BearbeitenModalForm"
                                  id="BearbeitenModalForm">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered" role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Wenn Sie dieses Dokument bearbeiten, werden alle Freigaben
                                                storniert</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_bearbeiten" name="id_bearbeiten"
                                                       value="{{ !empty($doc->id) ? $doc->id :  ''}}">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#BearbeitenModal').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>
                                                &nbsp;
                                                &nbsp;
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#BearbeitenModal').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FreigabeWedding" class="modal fade col">
                            <div class="modal-dialog  modal-dialog-centered " role="document">
                                <div class="modal-content ">
                                    <div class="modal-body text-center freigabe-padding">
                                        <h1>Hochzeitsdokument ist noch nicht generiert.</h1>
                                        <div class="clearfix"></div>
                                        <div>Bitte geben Sie zunächst alle zur Freigabe verfügbaren Dokumente frei.
                                        </div>
                                        <div class=" mt-4 justify-content-center ">
                                            <button
                                                    onclick="$('.modal-backdrop').hide();$('#FreigabeWedding').hide();return false;"
                                                    class="btn  btn-normal">zurück
                                            </button>
                                        </div>
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->
                        <div id="FreigabeDoc" class="modal fade col">
                            <form action="{{asset('docfreigabe/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten eine Dokumentenfreigabe erteilen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_docfreigabe" name="id_docfreigabe"
                                                       value="">
                                                <input type="hidden" id="id_job" name="id_job" value="{{$job->id}}">
                                                <input type="hidden" id="id_doctyp" name="id_doctyp" value="">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeDoc').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button onclick="$('.modal-backdrop').hide();$('#FreigabeDoc').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FastLaneFreigabeDoc" class="modal fade col">
                            <form action="{{asset('docfreigabe/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten eine Dokumentenfreigabe erteilen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_docfastlanefreigabe"
                                                       name="id_docfastlanefreigabe" value="">
                                                <input type="hidden" id="id_docfastlanetyp" name="id_docfastlanetyp"
                                                       value="">
                                                <input type="hidden" id="id_job" name="id_job" value="{{$job->id}}">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FastLaneFreigabeDoc').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FastLaneFreigabeDoc').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="AblehnungDoc" class="modal fade col">
                            <form action="{{asset('docablehnung/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten das Dokumenten ablehnen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_docablehnung"
                                                       name="id_docablehnung" value="">
                                                <input type="hidden" id="id_docablehnungtyp"
                                                       name="id_docablehnungtyp"
                                                       value="">
                                                <input type="hidden" id="id_job" name="id_job" value="{{$job->id}}">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#AblehnungDoc').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#AblehnungDoc').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <div id="FreigabeDatei" class="modal fade col">
                            <form action="{{asset('dateifreigabe/')}}" method="post">
                                {{csrf_field()}}
                                <div class="modal-dialog  modal-dialog-centered " role="document">
                                    <div class="modal-content ">
                                        <div class="modal-body text-center freigabe-padding">
                                            <h1>Sie möchten eine Dateifreigabe erteilen?</h1>
                                            <div class=" ">
                                                <input type="hidden" id="id_dateifreigabe" name="id_dateifreigabe"
                                                       value="">
                                                <input type="hidden" id="id_job" name="id_job" value="{{$job->id}}">
                                            </div>
                                            <div class="clearfix"></div>
                                            <div class=" mt-4 justify-content-center ">
                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeDatei').hide();return false;"
                                                        class="btn  btn-zurueck">zurück
                                                </button>

                                                <button
                                                        onclick="$('.modal-backdrop').hide();$('#FreigabeDatei').hide();"
                                                        class="btn bnt-best">bestätigen
                                                </button>
                                            </div>
                                        </div>
                                    </div><!-- /.modal-content -->
                                </div><!-- /.modal-dialog -->
                            </form>
                        </div><!-- /.modal -->
                        <input type="hidden" id="akt_pdfpage" name="akt_pdfpage" value="1"/>
                        <div class="col-12 col-md-12 float-left">
                            <div class="">
                                <div class="d-flex flex-row flex-wrap p-0 m-0">
                                    @if(!empty($editor))
                                        @if($editorRight == 1  && $editor->frontify != 1 )
                                            <a href="/editor?id={{ $editor->jobId }}&version=latest">
                                                @else
                                                    <a href="{{url('/freigabe/' . $editor->jobId . '/editor')}}">
                                                        @endif
                                                        <div class="documentcard {{$flagEditorRelease ? 'document_freigabe' : 'document_nicht_freigegeben' }}">
                                                            <div class="widgettitel {{ $flagEditorRelease ? 'gruenbg' : 'whitebg' }}">
                                                                <i
                                                                        class="las la-bell"></i>&nbsp;Editor -
                                                                Dokument
                                                            </div>
                                                            <div
                                                                    class="documenticon_pdf align-items-center ">

                                                                <div
                                                                        class=" align-items-center mt-5">
                                                                    Editor
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </a>
                                                @endif
                                                @foreach($documents AS $document)
                                                    @if($document->typ != 4 )
                                                        <a
                                                                href="{{url('freigabe/'.$job->id .'/doc/'.$document->id )}}">
                                                            @elseif(($count - count($freigaben['kunde']) <= 1 && $document->typ == 4) || (session('userdata')['rechte']['typ'] == 'ersteller'  && $document->typ == 4) )
                                                                @if( $document->wedding_ok == 0 && (session('userdata')['rechte']['typ'] == 'ersteller' ))
                                                                    <a href="{{url('weddingmaker/'.$document->id )}}">
                                                                        @else
                                                                            <a href="{{url('freigabe/'.$job->id .'/doc/'.$document->id )}}">
                                                                                @endif
                                                                                @else <a href="#"
                                                                                         data-toggle="modal"
                                                                                         data-target="#FreigabeWedding">
                                                                                    @endif
                                                                                    <div
                                                                                            class="documentcard {{( $document->id == $id_doc ? 'activecard': '')}} {{(((session('userdata')['rechte']['typ'] == 'ersteller' && in_array($document->id , $freigaben['agentur'])) || (session('userdata')['rechte']['typ'] == 'kunde' && in_array($document->id , $freigaben['kunde']))) ? 'document_freigabe' : 'document_nicht_freigegeben')}} {{($document->typ == 4 ?  ((($count - count($freigaben['agentur']) < 1 )) ? 'hochzeit_frei' : 'hochzeit' ) : '')}} {{( $document->anzeige_kunde != 1 ? 'standard-border':'' )}} {{( \App\Models\Dokument2Jobs::GetAblehnungen($document->id) > 0 ? 'disabledcard': '')}}">
                                                                                        @if($document->typ == 4)
                                                                                            @if($count - count($freigaben['agentur']) <= 1 && ((session('userdata')['rechte']['typ'] == 'ersteller' && in_array($document->id , $freigaben['agentur']) || ($count - count($freigaben['kunde']) <= 1 && session('userdata')['rechte']['typ'] == 'kunde' && in_array($document->id , $freigaben['kunde']) ))) )
                                                                                                <div class="widgettitel gruenbg">
                                                                                                    <i
                                                                                                            class="las la-bell"></i>&nbsp;Hochzeitsdokument
                                                                                                </div>
                                                                                            @elseif($count - count($freigaben['agentur']) <= 1 &&  !((session('userdata')['rechte']['typ'] == 'ersteller' && in_array($document->id , $freigaben['agentur']) || ($count - count($freigaben['kunde']) <= 1 && session('userdata')['rechte']['typ'] == 'kunde' && in_array($document->id , $freigaben['kunde'])) )))
                                                                                                <div class="widgettitel orangebg">
                                                                                                    <i
                                                                                                            class="las la-bell"></i>&nbsp;Hochzeitsdokument
                                                                                                </div>
                                                                                            @else
                                                                                                <div class="widgettitel whitebg">
                                                                                                    <i
                                                                                                            class="las la-bell"></i>&nbsp;Hochzeitsdokument
                                                                                                </div>
                                                                                            @endif
                                                                                        @else
                                                                                            <div class="widgettitel small">
                                                                                                &nbsp;
                                                                                            </div>
                                                                                        @endif
                                                                                        <div class="widgetpoints small">
                                                                                            ...
                                                                                        </div>

                                                                                        <div class="clearfix"></div>
                                                                                        <div class="spacer"></div>

                                                                                        @if(!empty($document))

                                                                                            @if(($document->typ == 4))
                                                                                                <div
                                                                                                        class="documenticon_hoch align-items-center ">

                                                                                                    @if(($count - count($freigaben['kunde'])) <= 1 || (session('userdata')['rechte']['typ'] == 'ersteller') )
                                                                                                        @if(!empty($document->preview_img) )
                                                                                                            <img
                                                                                                                    src="data:image/jpg;base64,{{base64_encode($document->preview_img)}}"
                                                                                                                    class="w-100"
                                                                                                                    class="preview"
                                                                                                                    data-toggle="modal"
                                                                                                                    data-target="#preview_{{$document->id}}Modal"
                                                                                                                    data-container="body"
                                                                                                                    title="{{$document->id}}"/>
                                                                                                        @else
                                                                                                            <canvas data=""
                                                                                                                    type="application/pdf"
                                                                                                                    id="preview_a{{$document->id}}"
                                                                                                                    class="pdf_inhalt"
                                                                                                                    style="overflow: hidden; width: 100%; "
                                                                                                                    data-toggle="modal"
                                                                                                                    data-target="#preview_a{{$document->id}}Modal"></canvas>
                                                                                                            <script>
                                                                                                                $(document).ready(function () {
                                                                                                                    previewdocpdf({{$document->id}}, 'preview_a{{$document->id}}', 1, 1, '{{($path)}}');
                                                                                                                });
                                                                                                            </script>
                                                                                                        @endif
                                                                                                    @else
                                                                                                        <img
                                                                                                                src="{{asset('images/PDF-Symbol.png')}}"
                                                                                                                width="100%">
                                                                                                    @endif
                                                                                                </div>

                                                                                            @elseif(strpos($document->name ,'.doc') !== false)
                                                                                                <div
                                                                                                        class="documenticon_text align-items-center ">
                                                                                                    W
                                                                                                </div>
                                                                                            @elseif(strpos($document->name ,'.xls')  !== false || strpos(strtolower($document->name) ,'.csv')  !== false  )
                                                                                                <div
                                                                                                        class="documenticon_tabelle align-items-center ">
                                                                                                    X
                                                                                                </div>
                                                                                            @elseif(strpos($document->name ,'.pdf')  !== false  )
                                                                                                <div
                                                                                                        class="documenticon_pdf align-items-center ">
                                                                                                    @if(!empty($document->preview_img))
                                                                                                        <img
                                                                                                                src="data:image/jpg;base64,{{base64_encode($document->preview_img)}}"
                                                                                                                class="w-100"
                                                                                                                class="preview"
                                                                                                                data-toggle="modal"
                                                                                                                data-target="#preview_{{$document->id}}Modal"
                                                                                                                data-container="body"
                                                                                                                title="{{$document->id}}"/>
                                                                                                    @elseif(!empty($document->inhalt))
                                                                                                        <canvas data=""
                                                                                                                type="application/pdf"
                                                                                                                id="preview_{{$document->id}}"
                                                                                                                class="pdf_inhalt"
                                                                                                                style="overflow: hidden; width: 100%; "
                                                                                                                data-toggle="modal"
                                                                                                                data-target="#preview_{{$document->id}}Modal"></canvas>
                                                                                                        <script>
                                                                                                            $(document).ready(function () {
                                                                                                                previewdocpdf({{$document->id}}, 'preview_{{$document->id}}', 1, 1, '{{$path}}');
                                                                                                            });
                                                                                                        </script>
                                                                                                    @else
                                                                                                        <div
                                                                                                                class="documenticon_pdf align-items-center ">
                                                                                                            <img
                                                                                                                    src="{{asset('images/PDF-Symbol.png')}}">
                                                                                                        </div>
                                                                                                    @endif
                                                                                                </div>
                                                                                            @endif
                                                                                            <div class="name"
                                                                                                 title="{{str_replace("_","_ ",$document->name)}}"
                                                                                                 style="white-space: nowrap;"
                                                                                            >
                                                                                                @if($document->typ == 4 )
                                                                                                    @if(($count - count($freigaben['kunde']) > 1) && (session('userdata')['rechte']['typ'] != 'ersteller')  )
                                                                                                        noch nicht
                                                                                                        generiert
                                                                                                    @else
                                                                                                        {{((strlen($document->name) > 13) ? substr($document->name, 0,13)."..." : $document->name)}}
                                                                                                    @endif
                                                                                                @else
                                                                                                    {{((strlen($document->name) > 13) ? substr($document->name, 0,13)."..." : $document->name)}}
                                                                                                @endif
                                                                                            </div>
                                                                                            <div class="document-title">
                                                                                                {{($document->typ != 4 ? $doctypen[$document->typ] : '')}}
                                                                                                &nbsp;
                                                                                            </div>
                                                                                            @if((session('userdata')['rechte']['typ'] == 'ersteller' || (session('userdata')['rechte']['typ'] == 'kunde'  && $document->anzeige_kunde == 1 )) && in_array($document->id , $ablehnungen['kunde']))
                                                                                                <div class="document-info ">
                                                                                                    <div class="">
                                                                                                        &nbsp;<i
                                                                                                                class="las la-arrow-circle-right document-icon {{(in_array($document->id , $freigaben['agentur']) ? 'icon-freigabe':'')}}"></i>
                                                                                                        <span
                                                                                                                class="{{(in_array($document->id , $freigaben['agentur']) ? 'bestandteil-freigabe':'')}} ">Agenturfreigabe</span>
                                                                                                        <br>
                                                                                                        &nbsp;<i
                                                                                                                class="las la-arrow-circle-right document-icon {{(in_array($document->id , $ablehnungen['kunde']) ? 'icon-ablehnung':'')}}"></i>
                                                                                                        <span
                                                                                                                class="{{(in_array($document->id , $ablehnungen['kunde']) ? 'bestandteil-ablehnung':'bestandteil-kunde')}}">Kundenablehnung</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            @elseif((session('userdata')['rechte']['typ'] == 'ersteller' || (session('userdata')['rechte']['typ'] == 'kunde' && in_array($document->id , $freigaben['kunde'])) ) && $document->anzeige_kunde == 1 )
                                                                                                <div class="document-info ">
                                                                                                    <div class="">
                                                                                                        &nbsp;<i
                                                                                                                class="las la-arrow-circle-right document-icon {{(in_array($document->id , $freigaben['agentur']) ? 'icon-freigabe':'')}}"></i>
                                                                                                        <span
                                                                                                                class="{{(in_array($document->id , $freigaben['agentur']) ? 'bestandteil-freigabe':'')}}">Agenturfreigabe</span>
                                                                                                        <br>
                                                                                                        &nbsp;<i
                                                                                                                class="las la-arrow-circle-right document-icon {{(in_array($document->id , $freigaben['kunde']) ? 'icon-freigabe':'')}}"></i>
                                                                                                        <span
                                                                                                                class="{{(in_array($document->id , $freigaben['kunde']) ? 'bestandteil-freigabe':'')}}">Kundenfreigabe</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            @elseif($document->anzeige_kunde == 1)
                                                                                                <div class="document-info ">
                                                                                                    <div class="">
                                                                                                        <div class="">
                                                                                                            &nbsp;<i
                                                                                                                    class="las la-arrow-circle-right document-icon {{(in_array($document->id , $freigaben['agentur']) ? 'icon-freigabe':'')}}"></i>
                                                                                                            <span
                                                                                                                    class="{{(in_array($document->id , $freigaben['agentur']) ? 'bestandteil-freigabe':'')}}">Agenturfreigabe</span>
                                                                                                            <br>
                                                                                                            &nbsp;<i
                                                                                                                    class="las la-arrow-circle-right document-icon {{(in_array($document->id , $freigaben['kunde']) ? 'icon-freigabe': '')}}"></i>
                                                                                                            <span
                                                                                                                    class="{{(in_array($document->id , $freigaben['kunde']) ? 'bestandteil-freigabe':'')}}">Kundenfreigabe</span>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            @endif

                                                                                        @endif


                                                                                    </div>
                                                                                </a>

                                                                                @endforeach
                                                                                @if($reg == 'salesforce')

                                                                                    @foreach($jobfiles AS $jobfile)
                                                                                        @foreach($hochdocs AS $document)
                                                                                            @if($document->typ != 4 )
                                                                                                <a
                                                                                                        href="{{url('file/'.$document->id )}}"
                                                                                                        target="_blank">
                                                                                                    @elseif(1)
                                                                                                        <a href="{{url('weddingautopreview/'.$document->id.'/file/'. $jobfile->id)}}"
                                                                                                           target="_blank">
                                                                                                            @else
                                                                                                                <a href="#"
                                                                                                                   data-toggle="modal"
                                                                                                                   data-target="#FreigabeWedding">
                                                                                                                    @endif


                                                                                                                    <div
                                                                                                                            class="documentcard {{( in_array($document->id , $freigaben) ? 'document_freigabe' : 'document_nicht_freigegeben')}} {{($document->typ == 4 ?  ($jobfile->freigegeben) ? 'hochzeit_frei' : 'hochzeit' : '')}}  {{(!empty($jobfile->stopzeit) ? 'document_gestoppt' : '')}}">
                                                                                                                        @if($document->typ == 4)
                                                                                                                            @if($jobfile->freigegeben)
                                                                                                                                <div
                                                                                                                                        class="widgettitel {{( $jobfile->freigegeben && in_array($document->id , $druckfreigaben) ? 'gruenbg':'orangebg')}}">
                                                                                                                                    <i
                                                                                                                                            class="las la-bell"></i>&nbsp;#{{$job->id}}
                                                                                                                                    _{{$jobfile->step_id}}
                                                                                                                                </div>
                                                                                                                            @else
                                                                                                                                <div
                                                                                                                                        class="widgettitel whitebg">
                                                                                                                                    <i
                                                                                                                                            class="las la-bell"></i>&nbsp;#{{$job->id}}
                                                                                                                                    _{{$jobfile->step_id}}
                                                                                                                                </div>
                                                                                                                            @endif
                                                                                                                        @else
                                                                                                                            <div
                                                                                                                                    class="widgettitel small">
                                                                                                                                &nbsp;
                                                                                                                            </div>
                                                                                                                        @endif
                                                                                                                        <div
                                                                                                                                class="widgetpoints small">
                                                                                                                            ...
                                                                                                                        </div>

                                                                                                                        <div
                                                                                                                                class="clearfix"></div>
                                                                                                                        <div
                                                                                                                                class="spacer"></div>

                                                                                                                        @if(!empty($document))


                                                                                                                            @if(($document->typ == 4))
                                                                                                                                <div
                                                                                                                                        class="documenticon_hoch align-items-center ">
                                                                                                                                    @if($jobfile->freigegeben )
                                                                                                                                        @if(!empty($document->preview_img) )
                                                                                                                                            <img
                                                                                                                                                    src="data:image/jpg;base64,{{base64_encode($document->preview_img)}}"
                                                                                                                                                    class="w-100"
                                                                                                                                                    class="preview"
                                                                                                                                                    data-toggle="modal"
                                                                                                                                                    data-target="#preview_{{$document->id}}Modal"
                                                                                                                                                    data-container="body"
                                                                                                                                                    title="{{$document->id}}"/>
                                                                                                                                        @else
                                                                                                                                            <canvas
                                                                                                                                                    data=""
                                                                                                                                                    type="application/pdf"
                                                                                                                                                    id="preview_a{{$document->id}}_hoch"
                                                                                                                                                    class="pdf_inhalt"
                                                                                                                                                    style="overflow: hidden; width: 100%; "
                                                                                                                                                    data-toggle="modal"
                                                                                                                                                    data-target="#preview_a{{$document->id}}Modal"></canvas>
                                                                                                                                            <script>
                                                                                                                                                $(document).ready(function () {
                                                                                                                                                    previewdocpdf({{$document->id}}, 'preview_a{{$document->id}}_hoch', 1, 1, '{{($path)}}');
                                                                                                                                                });
                                                                                                                                            </script>
                                                                                                                                        @endif
                                                                                                                                    @else
                                                                                                                                        <img
                                                                                                                                                src="{{asset('images/PDF-Symbol.png')}}"
                                                                                                                                                width="100%">
                                                                                                                                    @endif
                                                                                                                                </div>
                                                                                                                            @elseif(strpos($document->name ,'.doc') !== false)
                                                                                                                                <div
                                                                                                                                        class="documenticon_text align-items-center ">
                                                                                                                                    W
                                                                                                                                </div>
                                                                                                                            @elseif(strpos($document->name ,'.xls')  !== false || strpos(strtolower($document->name) ,'.csv')  !== false  )
                                                                                                                                <div
                                                                                                                                        class="documenticon_tabelle align-items-center ">
                                                                                                                                    X
                                                                                                                                </div>
                                                                                                                            @elseif(strpos($document->name ,'.pdf')  !== false  )
                                                                                                                                <div
                                                                                                                                        class="documenticon_pdf align-items-center ">
                                                                                                                                    @if(!empty($document->preview_img))
                                                                                                                                        <img
                                                                                                                                                src="data:image/jpg;base64,{{base64_encode($document->preview_img)}}"
                                                                                                                                                class="w-100"
                                                                                                                                                class="preview"
                                                                                                                                                data-toggle="modal"
                                                                                                                                                data-target="#preview_{{$document->id}}Modal"
                                                                                                                                                data-container="body"
                                                                                                                                                title="{{$document->id}}"/>
                                                                                                                                    @elseif(!empty($document->inhalt))
                                                                                                                                        <canvas
                                                                                                                                                data=""
                                                                                                                                                type="application/pdf"
                                                                                                                                                id="preview_{{$document->id}}"
                                                                                                                                                class="pdf_inhalt"
                                                                                                                                                style="overflow: hidden; width: 100%; "
                                                                                                                                                data-toggle="modal"
                                                                                                                                                data-target="#preview_{{$document->id}}Modal"></canvas>
                                                                                                                                        <script>
                                                                                                                                            $(document).ready(function () {
                                                                                                                                                previewdocpdf({{$document->id}}, 'preview_{{$document->id}}', 1, 1, '{{$path}}');
                                                                                                                                            });
                                                                                                                                        </script>
                                                                                                                                    @else
                                                                                                                                        <div
                                                                                                                                                class="documenticon_pdf align-items-center ">
                                                                                                                                            <img
                                                                                                                                                    src="{{asset('images/PDF-Symbol.png')}}">
                                                                                                                                        </div>
                                                                                                                                    @endif
                                                                                                                                </div>
                                                                                                                            @endif
                                                                                                                            <div
                                                                                                                                    class="name"
                                                                                                                                    style="white-space: nowrap;">
                                                                                                                                @if($document->typ == 4 )
                                                                                                                                    @if(($count - count($freigaben['kunde']) > 1) && (session('userdata')['rechte']['typ'] != 'ersteller')  )
                                                                                                                                        noch
                                                                                                                                        nicht
                                                                                                                                        generiert
                                                                                                                                    @else
                                                                                                                                        {{((strlen($document->name) > 16) ? substr($document->name, 0,16)."..." : $document->name)}}
                                                                                                                                    @endif
                                                                                                                                @else
                                                                                                                                    {{((strlen($document->name) > 16) ? substr($document->name, 0,16)."..." : $document->name)}}
                                                                                                                                @endif
                                                                                                                            </div>
                                                                                                                            <div
                                                                                                                                    class="document-title">
                                                                                                                                {{($document->typ != 4 ? $doctypen[$document->typ] : '')}}
                                                                                                                                &nbsp;
                                                                                                                            </div>
                                                                                                                            @if(in_array($document->id , $freigaben))
                                                                                                                                <div
                                                                                                                                        class="document-info">
                                                                                                                                    <div
                                                                                                                                            class="">
                                                                                                                                        <small>Projektstatus<br></small>
                                                                                                                                        &nbsp;<i
                                                                                                                                                class="las la-arrow-circle-right document-icon "></i>
                                                                                                                                        Kundenfreigabe
                                                                                                                                        erfolgt
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            @else
                                                                                                                                <div
                                                                                                                                        class="document-info ">
                                                                                                                                    <div
                                                                                                                                            class="">
                                                                                                                                        <small>Projektstatus<br></small>
                                                                                                                                        &nbsp;<i
                                                                                                                                                class="las la-arrow-circle-right document-icon "></i>
                                                                                                                                        Zum
                                                                                                                                        Projekt
                                                                                                                                        hinzugefügt
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            @endif

                                                                                                                    </div>

                                                                                                                </a>
                                                                                                            @endif
                                                                                                            @endforeach
                                                                                                            <div>
                                                                                                                <a
                                                                                                                        href="{{url('file/'.$jobfile->id )}}"
                                                                                                                        target="_blank">
                                                                                                                    <div
                                                                                                                            class="documentcard {{($jobfile->freigegeben ? 'document_freigabe' : 'document_nicht_freigegeben')}} {{(!empty($jobfile->stopzeit) ? 'document_gestoppt' : '')}}">
                                                                                                                        <div
                                                                                                                                class="widgettitel {{( $jobfile->freigegeben ? 'gruenbg':'orangebg')}}">
                                                                                                                            <i
                                                                                                                                    class="las la-bell"></i>&nbsp;#{{$job->id}}
                                                                                                                            _{{$jobfile->step_id}}
                                                                                                                        </div>
                                                                                                                        <div
                                                                                                                                class="widgetpoints small">
                                                                                                                            ...
                                                                                                                        </div>

                                                                                                                        <div
                                                                                                                                class="clearfix"></div>
                                                                                                                        <div
                                                                                                                                class="spacer"></div>
                                                                                                                        @if(!empty($jobfile))
                                                                                                                            @if(strpos($jobfile->org_name ,'.doc') !== false)
                                                                                                                                <div
                                                                                                                                        class="documenticon_text align-items-center ">
                                                                                                                                    W
                                                                                                                                </div>
                                                                                                                            @elseif(strpos($jobfile->org_name ,'.xls')  !== false || strpos(strtolower($jobfile->org_name) ,'.csv')  !== false  )
                                                                                                                                <div
                                                                                                                                        class="documenticon_tabelle align-items-center ">
                                                                                                                                    X
                                                                                                                                </div>
                                                                                                                            @elseif(strpos($jobfile->org_name ,'.pdf')  !== false  )
                                                                                                                                <div
                                                                                                                                        class="documenticon_pdf align-items-center ">
                                                                                                                                    @if(!empty($jobfile->inhalt) && substr(base64_encode($jobfile->inhalt), 0,1) != 'J')
                                                                                                                                        <img
                                                                                                                                                src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}"
                                                                                                                                                class="w-100"
                                                                                                                                                class="preview"
                                                                                                                                                data-toggle="modal"
                                                                                                                                                data-target="#preview_{{$jobfile->id}}Modal"
                                                                                                                                                data-container="body"
                                                                                                                                                title="{{$jobfile->id}}"/>
                                                                                                                                    @elseif(!empty($jobfile->inhalt))
                                                                                                                                        <canvas
                                                                                                                                                data=""
                                                                                                                                                type="application/pdf"
                                                                                                                                                id="preview_{{$jobfile->id}}"
                                                                                                                                                class="pdf_inhalt"
                                                                                                                                                style="overflow: hidden; width: 100%; "
                                                                                                                                                data-toggle="modal"
                                                                                                                                                data-target="#preview_{{$jobfile->id}}Modal"></canvas>
                                                                                                                                        <script>
                                                                                                                                            $(document).ready(function () {
                                                                                                                                                previewdocpdf({{$jobfile->id}}, 'preview_{{$jobfile->id}}', 1, 1, '{{$path}}');
                                                                                                                                            });
                                                                                                                                        </script>
                                                                                                                                    @else
                                                                                                                                        <div
                                                                                                                                                class="documenticon_pdf align-items-center ">
                                                                                                                                            <img
                                                                                                                                                    src="{{asset('images/PDF-Symbol.png')}}">
                                                                                                                                        </div>
                                                                                                                                    @endif
                                                                                                                                </div>
                                                                                                                            @endif
                                                                                                                            <div
                                                                                                                                    class="name"
                                                                                                                                    style="white-space: nowrap;">

                                                                                                                                {{((strlen($jobfile->org_name) > 16) ? substr($jobfile->org_name, 0,16)."..." : $jobfile->org_name)}}
                                                                                                                            </div>
                                                                                                                            <div
                                                                                                                                    class="document-title">
                                                                                                                                Adressdatei
                                                                                                                            </div>
                                                                                                                            @if(($jobfile->freigegeben))
                                                                                                                                <div
                                                                                                                                        class="document-info bestandteil-freigabe">
                                                                                                                                    <div
                                                                                                                                            class="">
                                                                                                                                        <small>Projektstatus<br></small>
                                                                                                                                        &nbsp;<i
                                                                                                                                                class="las la-arrow-circle-right document-icon icon-freigabe"></i>
                                                                                                                                        Freigabe
                                                                                                                                        erfolgt
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            @else
                                                                                                                                <div
                                                                                                                                        class="document-info ">
                                                                                                                                    <div
                                                                                                                                            class="">
                                                                                                                                        <small>Projektstatus<br></small>
                                                                                                                                        &nbsp;<i
                                                                                                                                                class="las la-arrow-circle-right document-icon "></i>
                                                                                                                                        Zum
                                                                                                                                        Projekt
                                                                                                                                        hinzugefügt
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            @endif
                                                                                                                        @endif
                                                                                                                    </div>
                                                                                                                </a>
                                                                                                            </div>




                                            @endforeach

                                        @endif

                                </div>
                                <div class="clearfix"></div>
                                @if(session('userdata')['rechte']['typ'] == 'kunde' )
                                    <div class="col-12  h-100 p-0 m-1">
                                        <div class="col-12 bg-white responsebox m-2 ">
                                            <h4>Upload (PWL für
                                                Drucker) @if(session('userdata')['rechte']['typ'] == 'kunde' && $job->freigegeben )
                                                    <i class="las la-plus border"
                                                       onclick="slideupload('#uploadline');"></i>
                                                @endif</h4>
                                            <div id="uploadline" style="display: none;">
                                                @if(session('userdata')['rechte']['typ'] == 'kunde' && $job->freigegeben  )
                                                    <form enctype="multipart/form-data" method="post"
                                                          action="{{url('pwlupload')}}">
                                                        {{csrf_field()}}
                                                        <input name="pwl_id_job" type="hidden" value="{{$job->id}}">
                                                        <input type="file" class="form-control" name="filedata"
                                                               class="col-6"><br>
                                                        <button class="btn btn-primary">Upload</button>
                                                    </form>
                                                @endif
                                            </div>

                                                <?PHP
                                                $pwldateien = [];
                                                if (!empty($job)) {
                                                    $pwldateien = App\Models\PWLs::Where('id_job', $job->id)->Where(function ($q) {
                                                        $q->whereNull('step_id')->orWhere('step_id', '');
                                                    })->orderBy('created_at', 'DESC')->get();
                                                }
                                                ?>
                                            @if(count($pwldateien) != 0 )
                                                @foreach($pwldateien AS $pwldatei)
                                                    <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                        <li class="align-self-start col-md-6 col-12">{{$pwldatei->file_name}}
                                                            ({{number_format(strlen($pwldatei->file)/(1024), 1, ',', '')}}
                                                            KB)
                                                        </li>
                                                        <li class="align-self-start col-md-2 col-12">{{(!empty($pwldatei->uebergabe_dienstleister) ?\Carbon\Carbon::parse($pwldatei->uebergabe_dienstleister)->format('d.m.Y H:i') : '-')}}</li>
                                                        <li class="align-self-start col-md-1 col-12"></li>

                                                        <li class="align-self-start col-md-3 col-12">
                                                            <ul class="status p-0 m-0">
                                                                <li>
                                                                    @if(!empty($pwldatei->uebergabe_dienstleister))
                                                                        <div class="dot"></div>
                                                                        übergeben
                                                                    @else
                                                                        <div class="dot-red"></div>
                                                                        noch nicht übergeben
                                                                    @endif
                                                                </li>
                                                            </ul>
                                                        </li>

                                                    </ul>
                                                    <div class="clearfix"></div>
                                                @endforeach
                                            @else
                                                <div>Es wurden noch keine PWL-Liste hochgeladen.</div>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                                @if(session('userdata')['rechte']['typ'] == 'kunde')
                                    <div class="col-12  h-100 p-0 m-1">
                                        <div class="col-12 bg-white responsebox m-2 ">
                                            <h4>Download Kunde</h4>
                                                <?PHP $responsedateien = App\Models\Responses::Where('id_job', $job->id)->Where(function ($q) {
                                                $q->whereNull('step_id')->orWhere('step_id', '');
                                            })->orderBy('created_at', 'DESC')->get(); ?>
                                            @if(count($responsedateien) != 0 )
                                                @foreach($responsedateien AS $responsedatei)
                                                    <ul class="d-md-flex   p-1 ml-3  m-0 autofiles">
                                                        @if(session('userdata')['rechte']['jobs'][$job->id]['download'] )
                                                            <li class="align-self-start col-md-6 col-12"><a
                                                                        href="{{url('/responsedownload/'.$responsedatei->id)}}"
                                                                        target="_blank">{{$responsedatei->file_name}}</a>
                                                                ({{number_format(strlen($responsedatei->file)/(1024), 1, ',', '')}}
                                                                KB)<br>
                                                                <ul class="small">
                                                                        <?PHP
                                                                        $responseHistory = App\Models\ResponseDownload::Where('id_Response', $responsedatei->id)->orderBy('datetime', 'DESC')->get();
                                                                        ?>

                                                                    @foreach($responseHistory AS $responseHistory)
                                                                        <li>Download
                                                                            am {{  \Carbon\Carbon::parse($responseHistory->datetime)->format('d.m.Y H:i') }}
                                                                            von {{  \App\Http\Controllers\UserController::getUserName($responseHistory->id_user) }}</li>
                                                                    @endforeach
                                                                </ul>
                                                            </li>
                                                        @else
                                                            <li class="align-self-start col-md-6 col-12">{{$responsedatei->file_name}}
                                                                ({{number_format(strlen($responsedatei->file)/(1024), 1, ',', '')}}
                                                                KB)<br>
                                                                <ul class="small">
                                                                        <?PHP
                                                                        $responseHistory = App\Models\ResponseDownload::Where('id_Response', $responsedatei->id)->orderBy('datetime', 'DESC')->get();
                                                                        ?>

                                                                    @foreach($responseHistory AS $responseHistory)
                                                                        <li>Download
                                                                            am {{  \Carbon\Carbon::parse($responseHistory->datetime)->format('d.m.Y H:i') }}
                                                                            von {{  \App\Http\Controllers\UserController::getUserName($responseHistory->id_user) }}</li>
                                                                    @endforeach
                                                                </ul>
                                                            </li>
                                                        @endif

                                                        <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($responsedatei->created_at)->format('d.m.Y H:i')}}</li>
                                                        <li class="align-self-start col-md-1 col-12"></li>

                                                        <li class="align-self-start col-md-3 col-12">
                                                            <ul class="status p-0 m-0">
                                                                <li>
                                                                    <div class="dot"></div>
                                                                    übernommen
                                                                </li>
                                                            </ul>
                                                        </li>

                                                    </ul>
                                                    <div class="clearfix"></div>
                                                @endforeach
                                            @else
                                                <div>Es wurden noch keine Response zurückgeliefert.</div>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                                <div class="bottom-spacer"></div>
                            </div>


                        </div>

                    </div>
                    @endif
                </div>

            </div>
            @if($id_doc === 0)
                <div class="float-right bg-white p-3  " style="display: none; width: 520px; min-height: 100vh;"
                     id="sidebar_right">
                    <form method="post">
                        {{csrf_field()}}
                        <h3 style="color: #4D7CFE;">Editor-Dokument</h3>
                        <h5><i class="las la-info-circle"></i>&nbsp;Dokument-Historie</h5>
                        @if(!empty($docaktivitaeten))
                            @foreach($docaktivitaeten AS $docaktivitaet)
                                <div class="historie line">
                                    <div class="historie_left"><i
                                                class="las la-check checkblue"></i>&nbsp{{$docaktivitaet['release']}}
                                    </div>
                                    <div
                                            class="historie_right">{{$docaktivitaet['vorname']}} {{$docaktivitaet['name']}}
                                        <br/>
                                        @if(!empty($docaktivitaet['created_at']))
                                            {{\Carbon\Carbon::createFromTimeString($docaktivitaet['created_at'])->format('H:i')}}
                                            | {{\Carbon\Carbon::createFromTimeString($docaktivitaet['created_at'])->format('d.m.Y')}}
                                        @endif
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            @endforeach
                        @endif
                        <div class="historie line">
                            <a href="/editor?id={{ $editor->jobId }}&version=latest">
                                <div class="download-btn btn float-left mb-2">Ansehen</div>
                            </a>
                            <div class="clearfix"></div>
                            <a href="{{url('get/editor/testpdf/download/' . $job->id  )}}">
                                <div class="download-btn btn float-left">Download</div>
                            </a>
                            @if($freigabeK < ($freigabecount)  && (session('userdata')['rechte']['typ'] != 'ersteller') && session('userdata')['rechte']['jobs'][$job->id]['freigabe']  )
                                @if(!in_array(Auth::id(), $userEditorRelease))
                                    <div class="">
                                        <button class="freigabe-btn btn float-right" data-toggle="modal"
                                                data-target="#FreigabeDoc"
                                                onclick=" jQuery('#id_docfreigabe').val(0); jQuery('#id_doctyp').val('editor');return false;">
                                            @if($freigabecount > 1)
                                                {{($freigabeK+1)}}. Freigabe
                                            @else
                                                Freigabe
                                            @endif
                                        </button>
                                    </div>
                                @elseif(in_array(Auth::id(), $userEditorRelease) && session('userdata')['rechte']['jobs'][$job->id]['fastlane'] == 1)
                                    <div class="">
                                        <button class="fastlanefreigabe-btn btn float-right" data-toggle="modal"
                                                data-target="#FreigabeDoc"
                                                onclick=" jQuery('#id_docfreigabe').val('0'); return false;">
                                            Notfreigabe
                                        </button>
                                    </div>

                              @endif
                        @endif
                    </form>
                </div>
                <script>
                    $('#sidebar_right').animate({width: 'toggle'}, 350);
                </script>
            @endif
            @if($reg != 'salesforce')
                <div class="float-right bg-white p-3  " style="display: none; width: 520px; min-height: 100vh;"
                     id="sidebar_right">
                    <form method="post">
                        {{csrf_field()}}
                        @if(!empty($doc))
                            <h3 style="color: #4D7CFE;">{{$doc->name}}</h3>
                            @if(in_array($doc->id , $freigaben) && (session('userdata')['rechte']['typ'] != 'ersteller') )
                                <div class="docfrei">
                                    Dieser Bestandteil ist freigegeben
                                </div>
                            @endif
                            <h5><i class="las la-info-circle"></i>&nbsp;Dokument-Historie</h5>
                            @if(!empty($docaktivitaeten))
                                @foreach($docaktivitaeten AS $docaktivitaet)
                                    @if(!empty($docaktivitaet->afreigabedatum))
                                        <div class="historie line">
                                            <div class="historie_left"><i
                                                        class="las la-check checkblue"></i>&nbsp;{{(!empty($docaktivitaet->taetigkeit_kunde) ?  $docaktivitaet->taetigkeit_kunde : 'Agenturfreigabe')}}
                                            </div>
                                            <div
                                                    class="historie_right">{{$docaktivitaet->vorname}} {{$docaktivitaet->name}}
                                                <br/>
                                                @if(!empty($docaktivitaet->afreigabedatum))
                                                    {{\Carbon\Carbon::createFromTimeString($docaktivitaet->afreigabedatum)->format('H:i')}}
                                                    | {{\Carbon\Carbon::createFromTimeString($docaktivitaet->afreigabedatum)->format('d.m.Y')}}
                                                @endif
                                            </div>
                                        </div>
                                    @else
                                        @if(!empty($docaktivitaet->created_at))
                                            <div class="historie line">
                                                <div class="historie_left"><i
                                                            class="las la-check checkblue"></i>&nbsp;{{(!empty($docaktivitaet->taetigkeit_kunde) ?  $docaktivitaet->taetigkeit_kunde : 'Freigabe')}} {{(!empty($docaktivitaet->storno) ?  '(storno)' : '')}}
                                                </div>
                                                <div
                                                        class="historie_right">{{$docaktivitaet->vorname}} {{$docaktivitaet->name}}
                                                    <br/>
                                                    @if(!empty($docaktivitaet->created_at))
                                                        {{\Carbon\Carbon::createFromTimeString($docaktivitaet->created_at)->format('H:i')}}
                                                        | {{\Carbon\Carbon::createFromTimeString($docaktivitaet->created_at)->format('d.m.Y')}}
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                    <div class="clearfix"></div>

                                @endforeach
                            @endif
                            <div class="clearfix"></div>
                            <div class="line"></div>
                            <div class="">
                                @if(!empty($doc->preview_img))
                                    <img class="w-100" class="preview" data-container="body"
                                         title="{{$doc->jobbezeichnung}}"
                                         src="data:image/jpg;base64,{{base64_encode($doc->preview_img)}}"/>
                                @elseif(empty($doc->preview_img) && (strpos($doc->name ,'.xls')  !== false || strpos(strtolower($doc->name) ,'.csv')  !== false))
                                    <div class="excelicon">
                                        X
                                    </div>
                                @elseif(empty($doc->preview_img) && strpos($doc->name ,'.doc')  !== false )
                                    <div class="wordicon">
                                        W
                                    </div>
                                @else
                                    <div id="seitenzahlen">
                                        @if($doc->typ == 4 && $weddingdoc)
                                            <button
                                                    onclick="previewweddingdoc({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1, '{{$path}}', '{{(!empty($jobfile->id) ? $jobfile->id : '')}}'); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                                <
                                            </button>
                                            <button
                                                    onclick="previewweddingdoc({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1,'{{$path}}','{{(!empty($jobfile->id) ? $jobfile->id : '')}}' ); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                                    id="pdf_next">>
                                            </button>
                                        @else
                                            <button
                                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1, '{{$path}}'); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                                <
                                            </button>
                                            <button
                                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1,'{{$path}}'); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                                    id="pdf_next">>
                                            </button>
                                        @endif
                                    </div>
                                    <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}"
                                           type="hidden">

                                    <div class="pdfview">
                                        <div class="pdfviewer" style="overflow: hidden !important;"
                                             @if($doc->typ == 4)onclick="window.open('{{url('weddingpreview/'.$doc->id)}}', '_blank');"@endif>
                                            <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                            @if($weddingdoc != 1)

                                                @if(!empty($doc->preview_img))
                                                    <img class="w-100" class="preview" data-container="body"
                                                         title="{{$doc->jobbezeichnung}}"
                                                         src="data:image/jpg;base64,{{base64_encode($doc->preview_img)}}"/>
                                                @else
                                                    <canvas data="" type="" id="preview_{{$doc->id}}_big"
                                                            class="pdf_inhalt"
                                                            style="overflow: hidden; width: 100%;">
                                                    </canvas>
                                                @endif
                                            @else
                                                <a href="{{url('weddingautopreview/'.$doc->id.'/file/'.(!empty($jobfile->id) ? $jobfile->id : ''))}}"
                                                   target="_blank">
                                                    @if(!empty($doc->preview_img))
                                                        <img class="w-100" class="preview" data-container="body"
                                                             title="{{$doc->jobbezeichnung}}"
                                                             src="data:image/jpg;base64,{{base64_encode($doc->preview_img)}}"/>
                                                    @else
                                                        <canvas data="" type="" id="preview_{{$doc->id}}_big"
                                                                class="pdf_inhalt"
                                                                style="overflow: hidden; width: 100%;">
                                                        </canvas>
                                                    @endif
                                                </a>
                                            @endif
                                            @if($weddingdoc != 1 && empty($doc->preview_img))
                                                <script>
                                                    $(document).ready(function () {
                                                        @if($count - count($freigaben['agentur']) <= 1 && $doc->typ == 4 )
                                                        previewmakerdoclookup({{$doc->id}}, 'preview_{{$doc->id}}_big', 1, 1, '{{$path}}');
                                                        @else
                                                        previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big', 1, 1, '{{$path}}');
                                                        @endif
                                                    });
                                                </script>
                                            @else
                                                <script>
                                                    $(document).ready(function () {
                                                        previewweddingdoc({{$doc->id}}, 'preview_{{$doc->id}}_big', 1, 1, '{{$path}}', '{{(!empty($jobfile->id) ? $jobfile->id : '')}}');
                                                    });
                                                </script>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>


                            <div class="sidespacer"></div>
                            <div>
                                {{-- @if((strpos($doc->name ,'.xls')  !== false || strpos($doc->name ,'.csv')  !== false) && (session('userdata')['rechte']['typ'] != 'ersteller') )
                                --}}

                                @if($doc->typ == 4)
                                    <a href="{{url('weddingdocdownload').'/' . $doc->id}}">
                                        <div class="download-btn btn float-left">Download</div>
                                    </a>
                                    @if(session('userdata')['rechte']['typ'] == 'ersteller')
                                        <div class="btn btn-warning mr-3 ml-2"
                                             onclick="exportXlsx({{(!empty($doc->id_document) ? $doc->id_document : '')}});">
                                            Vermassungsexport
                                        </div>
                                        <script>
                                            function exportXlsx(id) {
                                                $.get('/getmakercsv/' + id, {},
                                                    function (data) {
                                                        //console.log(data);
                                                        let a = document.createElement('a');
                                                        a.download = "export.xlsx";
                                                        a.href = "data:application/vnd.ms-excel;base64," + (data);
                                                        a.click();
                                                    }
                                                );
                                            }
                                        </script>
                                    @endif
                                @else
                                    <a href="{{url('docdownload').'/' . $doc->id}}">
                                        <div class="download-btn btn float-left">Download</div>
                                    </a>
                                @endif
                                {{-- @endif --}}
                                @if($doc->wedding_ok == 1  && (session('userdata')['rechte']['typ'] == 'ersteller' && $job->freigegeben != 1) &&  ($job->fuer_kunde_frei != 1 || $job->in_bearbeitung == 1))

                                    <div class="download-btn btn float-right" data-toggle=modal
                                         data-target=#BearbeitenModal>Bearbeiten
                                    </div>

                                @endif


                            </div>
                            <div class="clearfix"></div>
                            <div class="line"></div>

                                <?PHP if (!empty($doc) && $doc->typ == 4) $freigabecount = 2; ?>
                            @if(\App\Models\Dokument2Jobs::GetAblehnungen($id_doc) < 1)

                                @if($freigabeK < ($freigabecount+1)  && (session('userdata')['rechte']['typ'] != 'ersteller') && !in_array($doc->id , $freigaben['kunde']) && \App\Models\Dokument2Jobs::CheckFreigabeKUser($doc->id) < 1 && session('userdata')['rechte']['jobs'][$job->id]['freigabe']  )
                                    <div class="">
                                        <button class="freigabe-btn btn float-right" data-toggle="modal"
                                                data-target="#FreigabeDoc"
                                                onclick=" jQuery('#id_docfreigabe').val('{{$doc->id}}'); jQuery('#id_doctyp').val('{{$doc->typ}}');return false;">
                                            @if($freigabecount > 1)
                                                {{($freigabeK+1)}}. Freigabe
                                            @else
                                                Freigabe
                                            @endif
                                        </button>
                                    </div>

                                @elseif(($doc->typ == 4) && $freigabeK < ($freigabecount+1)  && (session('userdata')['rechte']['typ'] != 'ersteller') && !in_array($doc->id , $freigaben['kunde']) &&  session('userdata')['rechte']['jobs'][$job->id]['fastlane'] == 1 && session('userdata')['rechte']['jobs'][$job->id]['freigabe'] )
                                    <div class="">
                                        <button class="fastlanefreigabe-btn btn float-right" data-toggle="modal"
                                                data-target="#FreigabeDoc"
                                                onclick=" jQuery('#id_docfreigabe').val('{{$doc->id}}'); return false;">
                                            Notfreigabe
                                        </button>
                                    </div>
                                @endif
                                @if((session('userdata')['rechte']['typ'] != 'ersteller') && !in_array($doc->id , $freigaben['kunde']) && session('userdata')['rechte']['jobs'][$job->id]['freigabe'] )
                                    <div class="">
                                        <button class="ablehnung-btn btn float-left" data-toggle="modal"
                                                data-target="#AblehnungDoc"
                                                onclick=" jQuery('#id_docablehnung').val('{{$doc->id}}');jQuery('#id_docablehnungtyp').val('{{$doc->typ}}'); return false;">
                                            Ablehnen
                                        </button>
                                    </div>
                                @endif
                                @if($freigabeA < ($freigabecount) && (session('userdata')['rechte']['typ'] == 'ersteller') &&  $doc->anzeige_kunde == 1 && \App\Models\Dokument2Jobs::CheckFreigabeAUser($doc->id) < 1 && session('userdata')['rechte']['jobs'][$job->id]['freigabe'])
                                    <div class="">

                                        <button class="freigabe-btn btn float-right" data-toggle="modal"
                                                data-target="#FreigabeDoc"
                                                onclick=" jQuery('#id_docfreigabe').val('{{$doc->id}}'); jQuery('#id_doctyp').val('{{$doc->typ}}'); return false;">
                                            @if($freigabecount > 1)
                                                {{($freigabeA+1)}}. Freigabe
                                            @else
                                                Freigabe
                                            @endif
                                        </button>
                                    </div>
                                @endif
                            @endif
                            <div class="clearfix"></div>
                            <div style="height: 100px;"></div>
                        @endif
                        @if(!empty($file_info))
                            <h3 style="color: #4D7CFE;">{{$file_info->org_name}}</h3>
                            @if($file_info->freigegeben)
                                <div class="docfrei">
                                    Dieser Bestandteil ist freigegeben
                                </div>
                            @endif
                            <h5><i class="las la-info-circle"></i>&nbsp;Dokument-Historie</h5>
                            @if(!empty($file_info))

                                <div class="historie line">
                                    <div class="historie_left"><i
                                                class="las la-check checkblue"></i>&nbsp;API-Upload
                                    </div>
                                    <div class="historie_right"> {{config('app.CUSTOMER_CRM')}}
                                        <br/>
                                        {{\Carbon\Carbon::createFromTimeString($file_info->created_at)->format('H:i')}}
                                        | {{\Carbon\Carbon::createFromTimeString($file_info->created_at)->format('d.m.Y')}}
                                    </div>
                                </div>
                                <div class="clearfix"></div>

                            @endif
                            <div class="clearfix"></div>
                            <div class="line"></div>
                            <div class="">
                                @if(!empty($file_info->preview_img) && substr(base64_encode($file_info->preview_img), 0,1) != 'J')
                                    <img class="w-100" class="preview" data-container="body"
                                         title="{{$file_info->jobbezeichnung}}"
                                         src="data:image/jpg;base64,{{base64_encode($doc->preview_img)}}"/>
                                @elseif(empty($file_info->preview_img) && (strpos($file_info->org_name ,'.xls')  !== false || strpos(strtolower($file_info->org_name) ,'.csv')  !== false))
                                    <a href="{{url('file/'.$file_info->id)}}" target="_blank">
                                        <div class="excelicon">
                                            X
                                        </div>
                                    </a>
                                @elseif(empty($file_info->preview_img) && strpos($file_info->org_name ,'.doc')  !== false )
                                    <div class="wordicon">
                                        W
                                    </div>
                                @else
                                    {{--
                                    <div id="seitenzahlen">
                                    @if($doc->typ == 4)
                                    <button
                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1, '{{$path}}'); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                    <
                                    </button>
                                    <button
                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1,'{{$path}}'); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                    id="pdf_next">>
                                    </button>
                                    @else
                                    <button
                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1, '{{$path}}'); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                    <
                                    </button>
                                    <button
                                    onclick="previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1,'{{$path}}'); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                    id="pdf_next">>
                                    </button>
                                    @endif
                                    </div>
                                    <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">
                                    <div class="pdfview">
                                    <div class="pdfviewer" style="overflow: hidden !important;">
                                    <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                    <canvas data="" type="" id="preview_{{$doc->id}}_big" class="pdf_inhalt"
                                    style="overflow: hidden; width: 100%;">
                                    </canvas>
                                    <script>
                                    $(document).ready(function () {
                                    @if($count - count($freigaben) <= 1 && $doc->typ == 4 )
                                    previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big', 1, 1, '{{$path}}');
                                    @else
                                    previewdocpdf({{$doc->id}}, 'preview_{{$doc->id}}_big', 1, 1, '{{$path}}');
                                    @endif
                                    });
                                    </script>

                                    </div>
                                    </div>
                                    --}}
                                @endif
                            </div>


                            <div class="sidespacer"></div>
                            <div>
                                {{-- @if((strpos($doc->name ,'.xls')  !== false || strpos($doc->name ,'.csv')  !== false) && (session('userdata')['rechte']['typ'] != 'ersteller') )
                                --}}
                                @if($weddingdoc != 1 && $is_file != 1)
                                    <a href="{{url('docdownload').'/' . $doc->id}}">
                                        <div class="download-btn btn float-left">Download</div>
                                    </a>
                                @elseif($is_file != 1)
                                    <a href="{{url('docdownload').'/' . $doc->id}}">
                                        <div class="download-btn btn float-left">Download</div>
                                    </a>
                                @endif
                                {{-- @endif --}}

                            </div>
                            <div class="clearfix"></div>
                            <div class="line"></div>

                            @if($is_file != 1 && !in_array($doc->id , $freigaben)  && (session('userdata')['rechte']['typ'] != 'ersteller') )
                                <div class="">
                                    <button class="freigabe-btn btn float-right" data-toggle="modal"
                                            data-target="#FreigabeDoc"
                                            onclick=" jQuery('#id_docfreigabe').val('{{$doc->id}}'); return false;">
                                        Freigabe
                                    </button>
                                </div>
                            @elseif($is_file == 1 && (session('userdata')['rechte']['typ'] == 'ersteller')  && $file_info->freigegeben != 1)
                                <div class="">
                                    <button class="freigabe-btn btn float-right" data-toggle="modal"
                                            data-target="#FreigabeDatei"
                                            onclick=" jQuery('#id_dateifreigabe').val('{{$file_info->id}}'); return false;">
                                        Freigabe
                                    </button>
                                </div>
                            @endif
                            <div class="clearfix"></div>

                        @endif
                    </form>

                    <div class="clearfix"></div>
                    <div class="bottom-spacer"></div>
                </div>
            @endif
            @if(0)
                <div class="col p-3 fixed-bottom projektfooter"
                     style="margin-left: {{(!empty(session('sidebarwidth'))) ?  session('sidebarwidth') : '162'}}px;">
                    <ul class="m-1 p-0 float-left">
                        <li class="no-border">Projektinformation:</li>
                        <li><i class="las la-calendar icon-blue"></i> Projektstart: <span
                                    class="werte">{{\Carbon\Carbon::parse($job->jobstart)->format('d.m.Y')}}</span>
                        </li>
                        <li><i class="las la-calendar icon-blue"></i> Projektende: <span
                                    class="werte">{{\Carbon\Carbon::parse($job->jobende)->format('d.m.Y')}}</span>
                        </li>
                        <li><i class="las la-list-ol icon-blue"></i> Geplante Adressmenge: <span
                                    class="werte">{{number_format($job->gesamtmenge, 0, ',', '.')}}</span></li>
                        <li><i class="las la-list-ol icon-blue"></i> Mindestmenge pro Druck-Step: <span
                                    class="werte">{{number_format($autojob->mindestmenge, 0, ',', '.')}}</span></li>
                        <li><i class="las la-calendar icon-blue"></i> Start Adresslieferung: <span
                                    class="werte">{{\Carbon\Carbon::parse($autojob->von)->format('d.m.Y')}}</span>
                        </li>
                        <li><i class="las la-list-ol icon-blue"></i> Adresslieferung: <span
                                    class="werte">{{\Carbon\Carbon::parse($autojob->bis)->format('d.m.Y')}}</span>
                        </li>
                    </ul>
                    <div class="clearfix"></div>
                    <ul class="m-1 p-0 float-left">
                        <li class="no-border">Benutzer:</li>
                        @if(!empty($users))
                            @foreach($users AS $user)
                                <li>
                                    <div
                                            class="text-blue">{{substr($user->vorname, 0, 1)}}{{substr($user->name,0,1)}}</div>&nbsp;{{$user->vorname}} {{$user->name}}
                                </li>
                            @endforeach
                        @endif
                    </ul>
                </div>
            @endif

            <script>

                $(document).ready(function () {
                    $('#dashboardtable').DataTable({
                        "order": [[4, "desc"], [3, "desc"]],
                        searching: false,
                        paging: false,
                        info: false,
                        "language": {
                            "emptyTable": "Keine aktuellen Projekte vorhanden."
                        }
                    });
                });


                function slideupload(elementid) {
                    $(elementid).slideToggle();
                }


                function klappandtoogle(elementid) {


                    if (!$('#pfeil' + elementid).hasClass('rotate')) {


                        $('#pfeil' + elementid).toggleClass('rotate');
                        $('#content' + elementid).slideToggle();
                    } else {
                        $('#pfeil' + elementid).toggleClass('rotate-reset');
                        $('#content' + elementid).slideToggle();
                    }

                }

                // popovers initialization - on hover
                $('[data-toggle="popover"]').popover({
                    html: true,
                    trigger: 'click',
                    placement: 'left',
                    container: 'body',
                    content: function () {
                        return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />';
                    }
                });


                $(document).on("click", ".popover .closer", function () {
                    $(this).parents(".popover").popover('hide');
                });
                @if(!empty($id_doc)) $('#sidebar_right').animate({width: 'toggle'}, 350);@endif
            </script>

@endsection
