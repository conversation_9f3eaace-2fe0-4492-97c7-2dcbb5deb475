@extends('layouts.app')
@section('content')

    <?PHP

    function array_set_pointer_to_key(&$array, $key)
    {
        reset($array);
        $c = 0;
        $l = count($array);
        while (key($array) !== $key) { // jeden Key überprüfen
            if (++$c >= $l) return false; // Array-Ende erreicht
            next($array); // Pointer um 1 verschieben
        }
        return true; // Key gefunden
    }


    function get_next_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = next($array);
        if (empty($return_val)) $return_val = array_shift($array);
        return $return_val;

    }
    function get_prev_dataset($array, $akt_value)
    {
        $key = array_search($akt_value, $array);
        array_set_pointer_to_key($array, $key);
        $return_val = prev($array);
        if (empty($return_val)) $return_val = end($array);
        return $return_val;

    }
    ?>
    <div class="filedetails">
        @if(!empty($error))
            <br/>
            <div class="error">
                {{$error}}
            </div>
            <br/>
        @endif
        <div class="hinweismodal bg-white col-10 col-lg-6 col-md-10" style="display:none;">
            <div onclick="jQuery('.absoften').hide();jQuery('.hinweismodal').hide();" class="float-right"
                 class="close"><img src="{{asset('images/cross.svg')}}"></div>
            Hinweis
            <div class="line"></div>
            <div class="d-flex">
                <div class="hinweistext float-left col-8">
                    <h2>Klick auf „Datei verwenden“ nicht vergessen</h2>
                    <br/>
                    Sie möchten eine Datei herunterladen.<br/>
                    <br/>
                    Bitte stellen Sie sicher, dass Sie den Button „Datei verwenden“ klicken, sobald Sie diese Adressen
                    im Druck einsetzen.<br/>
                    <br/>
                    Dankeschön<br/>
                    <div class="float-left">

                        <a href="{{url('download').'/' . $filedetails['id']}}"
                           onclick="jQuery('.absoften').hide();jQuery('.hinweismodal').hide();">
                            <button class="btn btn-primary ">Download</button>
                        </a>

                    </div>
                </div>
                <div class="hinweislogo float-right col-4">

                    <img src="{{asset('images/downloadhinweis.png')}}">
                </div>


            </div>


        </div>
        <div class="pfadnavi ">

            <div class="float-left">
                {{$filedetails['jobnummer']}} < <a
                    href="{{($filedetails['filetyp'] == 'daten' ) ? url('file/'.$filedetails['id']) : url('jobdetails/'.$filedetails['id_job']) }}">zurück
                    zur Projektdetailansicht</a>
            </div>
            <div class="float-left float-md-right row  ">
                <?PHP  $id_job = $filedetails['id_job'];  ?>
                <div class="col-12 m-0 ">
                    @if(session('userdata')['rechte']['jobs'][$id_job]['download']  == 1 && session('userdata')['rechte']['typ'] != 'ersteller' )
                        @if(!empty($filedetails['array_taetigkeiten']) && in_array( 6 , $filedetails['array_taetigkeiten']) || empty($filedetails['data']))
                            <a href="{{url('download').'/' . $filedetails['id']}}">
                                <button class="download ">
                                    Datei downloaden
                                </button>
                            </a>
                        @elseif(empty(session('userdata')['rechte']['jobs'][$id_job]['freigabe']))
                            <a href="{{url('download').'/' . $filedetails['id']}}">
                                <button class="download ">
                                    Datei downloaden
                                </button>
                            </a>
                        @else
                            <button class="download "
                                    onclick="jQuery('.absoften').show();jQuery('.hinweismodal').show();">
                                Datei downloaden
                            </button>
                        @endif
                    @endif
                    @if($filedetails['ablehnung'] == 0)
                        @if(session('userdata')['rechte']['jobs'][$id_job]['freigabe'] == 1)
                            @if($filedetails['freigabe'] != 0)

                                @if(session('userdata')['rechte']['typ'] != 'ersteller' )

                                    @if(session('userdata')['rechte']['typ'] == 'dienstleister' )

                                        @if(!in_array( 8 , $filedetails['array_taetigkeiten']) && !in_array( 6 , $filedetails['array_taetigkeiten']))
                                            <a href="{{url('freigeben').'/' . $filedetails['id']}}">
                                                <button class="freigabe">
                                                    <img src="{{asset('images/ok.svg')}}"> Datei verwenden
                                                </button>
                                            </a>
                                        @else
                                            <button class="freigabe freigegeben">
                                                <img src="{{asset('images/ok.svg')}}"> Datei verwendet
                                            </button>
                                        @endif
                                    @else
                                        @if(!in_array( 8 , $filedetails['array_taetigkeiten']) && !in_array( 6 , $filedetails['array_taetigkeiten']))
                                            <a href="{{url('freigeben').'/' . $filedetails['id']}}">
                                                <button class="freigabe">
                                                    <img src="{{asset('images/ok.svg')}}"> Datei freigeben
                                                </button>
                                            </a>
                                        @else
                                            <button class="freigabe freigegeben">
                                                <img src="{{asset('images/ok.svg')}}"> Datei freigegeben
                                            </button>
                                        @endif
                                    @endif

                                @else
                                    @if( !empty($filedetails['array_taetigkeiten']) && !in_array( 8 , $filedetails['array_taetigkeiten']) && !in_array( 5 , $filedetails['array_taetigkeiten']))
                                        <a href="{{url('freigeben').'/' . $filedetails['id']}}">
                                            <button class="freigabe btn-info"
                                                    style="background-color: rgba(237,200,76, 1); ">
                                                <img src="{{asset('images/ok.svg')}}"> Datei als geprüft
                                                kennzeichnen
                                            </button>
                                        </a>
                                    @else
                                        <button class="freigabe geprueft">
                                            <img src="{{asset('images/ok.svg')}}"> Datei in Ordnung
                                        </button>
                                    @endif
                                    @if( empty($filedetails->data) )

                                        @if(!in_array( 8 , $filedetails['array_taetigkeiten']) && in_array( 6 , $filedetails['array_taetigkeiten'])  )

                                            @if(!in_array( 7 , (array)$filedetails->array_taetigkeiten) )
                                                <a href="{{url('freigeben').'/' . $filedetails->id}}">
                                                    <button class="finalefreigabe">
                                                        <img src="{{asset('images/ok.svg')}}"> Datei final
                                                        freigeben
                                                    </button>
                                                </a>
                                            @else
                                                <button class="finalefreigabe finalfreigegeben ">
                                                        <img src="{{asset('images/ok.svg')}}"> Datei final
                                                    freigegeben
                                                </button>
                                            @endif
                                        @endif
                                    @endif

                                @endif

                                @if(!in_array( 8 , (array)$filedetails['array_taetigkeiten_usergroup'])    )
                                    <a href="{{url('ablehnen').'/' . $filedetails['id']}}">
                                        <button class="ablehnen btn-danger">
                                            <img src="{{asset('images/cross.svg')}}"> Datei ablehnen
                                        </button>
                                    </a>
                                @else
                                    <button class="ablehnen abgelent btn-danger">
                                        <img src="{{asset('images/cross.svg')}}"> Datei abgelehnt
                                    </button>
                                @endif

                            @endif

                        @endif

                    @endif


                </div>
            </div>
            <div class="clear"></div>
        </div>
        <div id="content">
            <h3 class="filealias">
                {{$filedetails['name']}}
            </h3>
            <form method="post" id="suchform">
                {{csrf_field()}}
                <div class="suchform ">
                    <div class="m-2 form-group row">
                        <input type="hidden" name="hiddenzeile" id="hiddenzeile" value="{{$zeile_akt}}">
                        <input type="hidden" name="pdfpage" id="pdfpage">
                        <input type="hidden" name="page" id="hiddenpage"
                               value="{{(($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) ? $_POST['page'] : $getpage)}}">

                        <select onchange="submit();" name="id_adressdaten"
                                class="form-control  float-left col-12 col-md-3 m-1">
                            <option value="">Bitte Adressdatei auswählen...</option>
                            @if(!empty($afiles))
                                @foreach($afiles['adressfiles'] AS $afile)
                                    <option value="{{$afile['id']}}"
                                            class="{{ ($afile['abgelehnt'] == 1 ? 'durchstrichen':'') }}"
                                            @if($afile['id'] == $id_adressdaten) selected="selected" @endif>{{(!empty($afile['alias_name'])?$afile['alias_name']: $afile['org_name'])}}{{ ($afile['abgelehnt'] == 1 ? ' [abgelehnt]':'') }}</option>
                                @endforeach
                            @endif
                        </select>
                        <input name="suche" id="suche" value="{{ request('suche') }}" placeholder="Suchbegriff"
                               class="form-control float-left col-12 col-md-3">
                        <select name="feld" class="form-control col-12 col-md-3 m-1">
                            <option value="*">Bitte Spalte auswählen...</option>

                            @if(!empty($afiledata['header']))
                                @foreach($afiledata['header'] AS $col)
                                    @if($col != 'qx_zeilen_nr')
                                        <option value="{{$col}}" {{($col == request('feld')? 'selected': '')}}>
                                            {{$col}}
                                        </option>
                                    @endif
                                @endforeach
                            @endif
                        </select>
                        <button name="page" value="1" class=" m-1 btn btn-primary">Suche</button>
                    </div>
                    <div class="clearfix"></div>
                    <div class="datencontainer overflow-hidden">
                        <div class="d-flex justify-content-between">
                            <div class="pages float-left">
                                <?PHP if ($_POST && !empty($_POST['page']) && ($_POST['page'] > 0)) $page = $_POST['page']; else $page = 1; ?>
                                <button name="page" value="{{(($page-1) > 1 ? ($page-1):  1)}}"><</button>
                                <button name="page"
                                        value="{{(($page+1) <= ceil(($filedetails_adr['searchcount'])/6) ? ($page+1) :  ceil(($filedetails_adr['searchcount'])/6))}}">
                                    >
                                </button>
                                Seite {{((!empty($_POST['page']) && ($_POST['page'] > 0)) ? $_POST['page'] : (($getpage < 1) ? 1 : $getpage))}}
                                von {{($filedetails_adr['searchcount'] > 6 ) ? ceil(($filedetails_adr['searchcount'])/6) : 1}}

                            </div>
                            <h3 class="filealias float-left">
                                {{$filedetails_adr['name']}}
                            </h3>
                            <div class="datensaetze">
                                {{$filedetails_adr['searchcount']}} / {{$filedetails_adr['datacount']}} Datensätze
                            </div>
                        </div>
                        <div class="clearfix "></div>

                    </div>
                    <div class="clearfix"></div>
                    <div class="whitebox p-2">
                        <div class="datentabelle table-responsive">
                            <table class="table table-striped table-bordered" id="adressdaten">
                                @if(!empty($afiledata['header']))
                                    <tr>
                                        <th>
                                            Zeilennr.
                                        </th>

                                        @foreach($afiledata['header'] AS $headkey => $col)
                                            @if($col != 'qx_zeilen_nr')
                                                <th>
                                                    {{$col}}
                                                </th>
                                            @endif
                                        @endforeach
                                    </tr>
                                @endif
                                @if(!empty($afiledata['dataset']) )

                                    <?PHP $zeilennummern = array(); ?>
                                    @foreach($afiledata['dataset'] AS $key => $zeile)
                                        <?PHP $zeilennummern[] = $zeile['qx_zeilen_nr'];?>
                                        <tr class='datenzeile' id="{{$zeile['qx_zeilen_nr']}}_zeile"
                                            onclick="$('.datenzeile').css('background-color', 'transparent'); $(this).css('background-color', '#f8eb81'); $('.filedata').hide(); $('#{{$zeile['qx_zeilen_nr']}}_setcard').show();">

                                            <td>
                                                {{intval($zeile['qx_zeilen_nr'])}}
                                            </td>

                                            @foreach($zeile AS $spaltenkey =>  $spalte)

                                                @if($spaltenkey != 'qx_zeilen_nr')
                                                    <td>
                                                        {{$spalte}}
                                                    </td>
                                                @endif
                                            @endforeach
                                        </tr>
                                    @endforeach
                                @endif
                            </table>
                        </div>
                    </div>
                    <div class="splitt_left" id="splitt_left" style="overflow-y: auto; ">


                        @foreach($afiledata['dataset'] AS $key2 => $zeile2)
                            <div class="filedata" id="{{$zeile2['qx_zeilen_nr']}}_setcard" style="display: none;">
                                <div class="m-2">
                                    <button
                                        onclick="$('.filedata').hide();$('#{{ get_prev_dataset($zeilennummern, $zeile2['qx_zeilen_nr'])}}_setcard').show();$('.datenzeile').css('background-color', 'transparent'); $('#{{get_prev_dataset($zeilennummern, $zeile2['qx_zeilen_nr'])}}_zeile').css('background-color', '#f8eb81');return false;">
                                        <
                                    </button>
                                    <button
                                        onclick="$('.filedata').hide();$('#{{ get_next_dataset($zeilennummern, $zeile2['qx_zeilen_nr'])}}_setcard').show();$('.datenzeile').css('background-color', 'transparent');$('#{{get_next_dataset($zeilennummern, $zeile2['qx_zeilen_nr'])}}_zeile').css('background-color', '#f8eb81');return false;">
                                        >
                                    </button>
                                </div>
                                <table>
                                    <tr>
                                        <td colspan="2" class="text-center">
                                            <b>Zeilennr.: {{$zeile2['qx_zeilen_nr']}}</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>
                                            Spaltenname
                                        </th>
                                        <th>
                                            Wert
                                        </th>
                                    </tr>

                                    @if(!empty($zeile2))
                                        @foreach($zeile2 AS $key => $spalte)
                                            @if($key != 'qx_zeilen_nr')
                                                <tr class="datenzeile">
                                                    <td class="spaltenname"
                                                        onclick="$('.datenzeile').css('background-color', 'transparent'); $(this.parentElement).css('background-color', '#f8eb81');">
                                                        {{$key}}
                                                    </td>
                                                    <td id="{{$key}}"
                                                        onclick="$('.datenzeile').css('background-color', 'transparent'); $(this.parentElement).css('background-color', '#f8eb81');"
                                                        ondblclick="copytext('{{$key}}');">
                                                        {{$spalte}}
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif
                                </table>

                            </div>
                        @endforeach
                    </div>

                    @if($filedetails['filetyp'] != 'daten')
                        <div class="splitt_right">
                            {{--
                            <button
                                onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage - 1 : 1}}; submit();">
                                <
                            </button>
                            @if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif / <span id="max_pages"> </span>
                            <button
                                onclick="document.getElementById('pdfpage').value = {{(!empty($pdfpage))? $pdfpage + 1 : 2}} ; submit();"
                                id="pdf_next">>
                            </button>
                            --}}

                            <div id="seitenzahlen">
                                <button
                                    onclick="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val())  - 1 , 1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                    <
                                </button>

                                <input type="text" id="akt_pdfpage" name="akt_pdfpage"
                                       value="@if(!empty($pdfpage)){{$pdfpage}}@else 1 @endif"
                                       style="width: 50px; text-align: right; margin-right: 10px;"
                                       onblur="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), 1)">/
                                <span id="max_pages"> </span>

                                <button
                                    onclick="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val())  + 1, 1 ); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                    id="pdf_next">>
                                </button>
                                <button
                                    onclick="print_id({{$filedetails['id']}}, $('#akt_pdfpage').val());  return false;"
                                    style="width: 60px !important;">drucken
                                </button>
                                Zoom:
                                <button
                                    onclick="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) -0.1); $('#zoom').val((parseFloat($('#zoom').val()) -0.1).toFixed(1));return false;"
                                    id="pdf_next">-
                                </button>
                                <input type="text" id="zoom" name="zoom" value="1"
                                       style="width: 50px; text-align: center; margin-right: 10px;"
                                       onblur="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val())"
                                       onkeyup="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()), $('#zoom').val())">
                                <button
                                    onclick="filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt',  parseInt($('#akt_pdfpage').val()) , parseFloat($('#zoom').val()) +0.1); $('#zoom').val((parseFloat($('#zoom').val()) +0.1).toFixed(1));return false;"
                                    id="pdf_next">+
                                </button>

                            </div>

                            <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}" type="hidden">
                            <div class="pdfview">
                                <div class="pdfviewer" style="overflow: hidden !important;">
                                    <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                    <canvas data="" type="" id="pdf_inhalt" class="pdf_inhalt"
                                            style="overflow: hidden;">
                                    </canvas>

                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="clear"></div>
            </form>
        </div>
    </div>
    <script>
        $('#' + {{($zeile_akt)}} + '_setcard').show();
        $('#' + {{($zeile_akt)}} + '_zeile').css('background-color', '#f8eb81');

        $(document).ready(function () {
            var resize = $(".splitt_left");
            var containerWidth = $("#content").width();

            $(resize).resizable({
                handles: 'e',
                minWidth: 200,
                resize: function (event, ui) {
                    var currentWidth = ui.size.width;
                    // this accounts for padding in the panels +
                    // borders, you could calculate this using jQuery
                    var padding = 220;
                    // this accounts for some lag in the ui.size value, if you take this away
                    // you'll get some instable behaviour
                    $(this).width(currentWidth);
                    // set the content panel width

                    //console.log(containerWidth - currentWidth - padding);
                    $('.splitt_right').width(containerWidth - currentWidth - padding - 20);
                }
            });

        });

        $(document).ready(function () {
            $('#adressdaten').DataTable({
                sorting: false,
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Adressdatei nicht ausgewählt."
                }
            });
        });
        /*
                var resizeOptsleft = {
                    handles: "ew" ,autoHide:true
                };
                var resizeOptsright = {
                    handles: "we" ,autoHide:true
                };
                $( ".splitt_left" ).resizable();
                $( ".splitt_left" ).draggable();
               $( ".splitt_right" ).draggable();
               $( ".splitt_right" ).resizable();

        */

        jQuery(function () {
            var sticky_navigation_offset_top = jQuery('#splitt_left').offset().top;
            var sticky_navigation = function () {
                var scroll_top = jQuery(window).scrollTop();
                if (scroll_top > sticky_navigation_offset_top) {
                    jQuery('#splitt_left').addClass('sticky');

                } else {
                    jQuery('#splitt_left').removeClass('sticky');
                }
            };
            sticky_navigation();
            jQuery(window).scroll(function () {
                sticky_navigation();
            });
            jQuery('a[href="#"]').click(function (event) {
                event.preventDefault();
            });
        });


        var canvas = document.getElementById("pdf_inhalt");
        var context = canvas.getContext('2d');
        var dragging = false;
        var lastX;
        var marginLeft = 0;
        var marginTop = 0;

        for (var i = 0; i < 1000; i++) {
            context.beginPath();
            context.arc(Math.random() * 10000, Math.random() * 250, 20.0, 0, 2 * Math.PI, false);
            context.stroke();
        }

        canvas.addEventListener('mousedown', function (e) {
            var evt = e || event;
            dragging = true;
            lastX = evt.clientX;
            lastY = evt.clientY;
            e.preventDefault();
        }, false);

        window.addEventListener('mousemove', function (e) {
            var evt = e || event;
            if (dragging) {
                var deltaX = evt.clientX - lastX;
                var deltaY = evt.clientY - lastY;
                lastX = evt.clientX;
                lastY = evt.clientY;
                marginLeft += deltaX;
                marginTop += deltaY;
                canvas.style.marginLeft = marginLeft + "px";
                canvas.style.marginTop = marginTop + "px";
            }
            e.preventDefault();
        }, false);

        window.addEventListener('mouseup', function () {
            dragging = false;
        }, false);

    </script>
    @if(!empty($filedetails['id']) && $filedetails['datacount'] == 0)
        <script>
            $(document).ready(function () {
                filedata2pdf({{$filedetails['id']}}, 'pdf_inhalt', {{((!empty($pdfpage))? $pdfpage : 1)}}, 1);
            });
        </script>
    @endif

@endsection

