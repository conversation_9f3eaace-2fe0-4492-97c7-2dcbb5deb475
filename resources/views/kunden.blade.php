@extends('layouts.app')
@section('content')
    <?PHP $tmp_buchstabe = ''; ?>
    <div class="kunden col-11">
        @if(!empty(session()->get('error')))
            <br/>
            <div class="error">
                {{session()->get('error')}}
            </div>
            <br/>
            <?PHP
            session()->forget('error');
            ?>
        @endif
        <div class="titel">
            <h1>Kunden</h1>
            <form method="post">
                {{csrf_field()}}
                @if(!empty($id))
                    <div class="form-group row m-1">
                        <input type="hidden" name="k_id" value="{{$id}}">
                        <input name="firma" type="text" placeholder="Kunde"
                               class="form-control col-12 col-md-3 float-left m-1" value="{{$kunden[$id]['kunde']}}">
                        <input type="text" name="loeschtage" value="{{$kunden[$id]['loeschtage']}}"
                               class="form-control col-12 col-md-3 float-left m-1"
                               placeholder="28 Tage bis zur Löschung">
                        <input type="text"
                               class="jscolor {hash:true, required:false}  form-control col-12 col-md-3 float-left m-1"
                               name="farbe" value="{{$kunden[$id]['farbe']}}" placeholder="Farbe">
                                <button class="btn btn-primary">Speichern</button>
                    </div>
                    <div class="clearfix"></div>
                    <div class="form-check-inline  row m-1">
                        <label for="aktiv" class="form-check-labe float-left m-2">aktiv</label>
                        <input type="checkbox" name="aktiv" class="form-check-input float-left m-1" {{( ($kunden[$id]['aktiv'])? 'checked=checked': '')}}>
                    </div>
                @else
                    <div class="form-group row m-1">
                        <input type="hidden" name="k_id" value="">
                        <input name="firma" type="text" placeholder="Kunde"
                               class="form-control col-12 col-md-3 float-left m-1">
                        <input type="text" name="loeschtage"  value="28"
                               class="form-control col-12 col-md-3 float-left m-1" placeholder="28 Tage bis zur Löschung">
                        <input name="farbe" type="text"
                               class="jscolor {hash:true, required:false} form-control col-12 col-md-3 float-left m-1"
                               placeholder="Farbe">
                        <button class="btn btn-primary float-left">Speichern</button>
                    </div>
                    <div class="clearfix"></div>
                    <div class=" form-check-inline row m-1">
                        <label for="aktiv" class="form-check-labe float-left m-2">aktiv</label>
                        <input type="checkbox" name="aktiv" class="form-check-input float-left m-1" checked=checked>
                    </div>
                @endif


            </form>
        </div>
        <div class=" float-left  col-12 d-flex flex-row">
            {{--
            <a href="{{url('/kunden/new')}}">
                <div>
                    <div class="dokumentencard align-content-center text-center"  style="color: #4D7CFE; font-size: 20px;">
                        <i class="las la-plus-circle" style="font-size: 40px; margin-top: 120px;"></i><br/>
                        Kunde hinzufügen
                    </div>
                </div>
            </a>
          --}}
                @foreach($kunden AS $k_id => $kunde)

                        <a href="{{url('/kunden/' .$k_id )}}">
                            <div>
                                <div class="dokumentencard align-content-center text-center {{( $kunde['aktiv'] == 1 ? '' :'inaktiv')}} "  style="color: #4D7CFE; font-size: 20px;">
                                    <b>{{$kunde['kunde']}}</b><br /><small>[{{$kunde['loeschtage']}} Tage bis zur Löschung]</small>
                                </div>
                            </div>
                        </a>


                @endforeach


        </div>

@endsection
