@extends('layouts.app')
@section('content')
<div class="col" >
    <div class="begruessung col-12 mb-5">
        <div class="brand_logo">
                <img src="images/Actisale_Logo.svg" alt="TeamGoDirect"/>
        </div>
        <div class="anrede">Hallo {{$daten['userdata']['anrede']}} {{$daten['userdata']['name']}},</div>
        <div>
            willkommen bei ActiSafe, dem Tool für den Austausch sensibler Daten und Dokumente gemäß Datenschutz. Sollten Sie Fragen haben, kontaktieren Sie bitte Ihren zuständigen Ansprechpartner bei ACTISALE oder wenden Sie sich an unser Support-Team <a href="mailto:<EMAIL>"><EMAIL></a>.
        </div>
        @if(!empty($daten['last_old_job']) && $daten['last_old_job']['last_job_end'] >= today())
        <div style="margin-top: 10px;">
            <b>Ihr aktuelles Projekt ist nicht aufgelistet?</b><br>Das Projekt wurde in der vorherigen Version von TransferSafe angelegt. Um sich dort anzumelden, klicken Sie bitte <a target="_blank" href="http://v1.transfersafe.de">hier</a>.
        </div>
        @endif
    </div>
<div>
    <div class="col-12 col-md-12 float-left">
        <div class="headline">
            <h2>Status/letzte Aktivitäten</h2>
        </div>
        <div class="offene_aufgaben">
            <table class="table table-striped table-bordered" id="dashboardtable" >
                <thead>
                <tr>
                    <th class="sorting">
                        Projektnummer
                    </th>
                    <th class="w-60">
                        Projektname
                    </th>
                    <th class="w-40">
                        Aktivität | Person
                    </th>
                    <th data-date-format="ddmmyyyy" >
                        Datum
                    </th>
                    <th class="favorit text-center p-2 no-icon" >
                        <img src="{{asset('images/fav_unset.svg')}}">
                    </th>

                </tr>
                </thead>
                <tbody>
                @foreach($daten['aufgaben'] AS $aufgabe)


                    @if(!empty($aufgabe->id))

                    <tr class="topline">

                        <td  >

                            <a href="./jobdetails/{{$aufgabe->id}}"><span
                                        class="">{{$aufgabe->jobnummer}}</span></a>
                        </td>
                        <td >
                            <a href="./jobdetails/{{$aufgabe->id}}"><span
                                        class="">{{$aufgabe->jobbezeichnung}}</span></a>
                        </td>
                        <td>
                            {{--
                            @if(session('userdata')['rechte']['typ'] == 'ersteller')
                                {{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe->taetigkeit)}} {{(empty($aufgabe['last_action']['aktion'])? '': $aufgabe['last_action']['aktion']. ' | ' . $aufgabe['last_action']['name'])}}
                            @elseif(session('userdata')['rechte']['typ'] == 'kunde')
                                {{(empty($aufgabe->taetigkeit_kunde)? 'Angelegt': $aufgabe->taetigkeit_kunde)}} {{(empty($aufgabe->taetigkeit_kunde)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @elseif(session('userdata')['rechte']['typ'] == 'dienstleister')
                                {{(empty($aufgabe->taetigkeit_dienstleister)? 'Angelegt': $aufgabe->taetigkeit_dienstleister)}} {{(empty($aufgabe->taetigkeit_dienstleister)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @endif
                            --}}

                            {{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe['last_action']['aktion'])}} @if(!empty($aufgabe['last_action']['id'])){{ ' | '. $aufgabe['last_action']['user'] }}@endif

                        </td>
                        <td>
                                <span style="display: none;">{{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('YmdHi') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('YmdHi'))}}</span>
                                {{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('d.m.Y H:i') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('d.m.Y H:i'))}}
                        </td>

                        <td onclick="toggle_fav('{{ $aufgabe['id'] }}');" class=" text-center p-2">
                            <span style="display:none;">{{$aufgabe['j_favorit']}}</span>
                            <img src="{{($aufgabe['j_favorit'] ? asset('images/fav_set.svg') : asset('images/fav_unset.svg'))}}">
                        </td>
                    </tr>
                    <?PHP $job_id = $aufgabe->j_id; ?>
                    @endif
                @endforeach
                </tbody>
            </table>

        </div>
        <div style="height: 40px;"></div>

    </div>
</div>
</div>
    <script>

        $(document).ready(function () {
            $('#dashboardtable').DataTable({
                "order": [[ 4, "desc" ], [ 3, "desc" ]],
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Keine aktuellen Projekte vorhanden."
                }
            });
        });


        function klappandtoogle(elementid) {


            if (!$('#pfeil' + elementid).hasClass('rotate')) {


                $('#pfeil' + elementid).toggleClass('rotate');
                $('#content' + elementid).slideToggle();
            } else {
                $('#pfeil' + elementid).toggleClass('rotate-reset');
                $('#content' + elementid).slideToggle();
            }

        }
    </script>

@endsection
