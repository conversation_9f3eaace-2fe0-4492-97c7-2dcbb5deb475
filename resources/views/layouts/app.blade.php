<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" style="height: 100%;">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}"></script>
    <script src="{{ asset('js/pdf.js') }}"></script>
    <script src="{{ asset('js/print.min.js') }}"></script>
    <script src="{{ asset('js/jscolor.js') }}"></script>
    <script src="{{ asset('js/functions.js?'.time()) }}"></script>
    <script src="{{ asset('js/jquery.dataTables.min.js') }}"></script>

    <script src="{{ asset('js/jquery-ui.js') }}"></script>
    <script src="{{ asset('js/dataTables.bootstrap4.min.js') }}"></script>
    <!-- Styles -->
    @if(config('app.mandant') == 'DIRECTHUB')
        <link href="{{ asset('css/app_directhub.css?'.time()) }}" rel="stylesheet">
    @else
        <link href="{{ asset('css/app_directhub.css?'.time()) }}" rel="stylesheet">
    @endif
    <link href="{{ asset('css/jquery-ui.css') }}" rel="stylesheet">
    <link href="{{ asset('css/bootstrap-table.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('css/line-awesome.min.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Rubik&display=swap" rel="stylesheet">
    <link href="" rel="stylesheet">
</head>
<body>
<div class="absoften" style="display:none;"></div>
<div class="absoften_rot" style="display:none;"></div>
<?PHP

if ((session('sidebarwidth')) !== null) {
    $width_sidebar = session('sidebarwidth');
    if($width_sidebar >= 150) $display_sym = 'none';
    else $display_sym = 'block';
}
else {
    $width_sidebar = 150;
    $display_sym = 'none';
}
?>
<div class=" d-flex h-100">
    <div class=" sidemenue float-left position-fixed " style="width: {{session('sidebarwidth')}}px;">
        <div class="menuelogo m-0 text-center">
            @if(config('app.mandant') == 'DIRECTHUB')
                <div class="dhbg">
                    <span id="sym_spacer" style="display:{{$display_sym}};"><img class="appsym" src="{{asset('images/dhsym.svg')}}"></span>
                </div>
            @else
                <div>
                    <span id="sym_spacer" style="display:{{$display_sym}};"><img class="appsym" src="{{asset('images/ts.svg')}}"></span>
                </div>
            @endif
            <a href="{{ url('projekte') }}" title="TransferSafe">
                <span id="logo_spacer" class="" style="display: {{session('logotextshow')}};">
                        @if(config('app.mandant') == 'ACTISALE')
                            <img class="logoplacer" style="{{( $display_sym != 'block' ?  'display: block;' : 'display: none;')}}" src="{{asset('images/ActiSafe_Logo_mono.svg')}}">
                        @elseif(config('app.mandant') == 'DIRECTHUB')
                            <div class="dhbg">
                                <img class="logoplacer" style="{{( $display_sym != 'block' ?  'display: block;' : 'display: none;')}}" src="{{asset('images/directhub_logo_white.svg')}}">
                            </div>
                        @else
                            <div class="ohnebg">
                                <img class="logoplacer" style="{{( $display_sym != 'block' ?  'display: block;' : 'display: none;')}}" src="{{asset('images/TransferSafe_Logo_white.svg')}}">
                            </div>
                        @endif
                </span>
            </a>
        </div>
        <div class="menubox">
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
            <div class="menutitel" >
                <span style="visibility: {{session('textshow')}};">Aktion</span>
            </div>
            <a href="{{ url('neuesprojekt') }}" title="Projekt erstellen">
                <div class="menusub">
                    <i class="las la-plus-circle"></i>
                    <span style="visibility: {{session('textshow')}};">Projekt erstellen</span>
                </div>
            </a>
        @endif
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
        <a href="{{ url('dashboard') }}" title="Dashboard">
            <div class="menusub">
                <i class="las la-tachometer-alt"></i>
                <span style="visibility: {{session('textshow')}};">Dashboard</span>
            </div>
        </a>
        @endif
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
            <a href="{{ url('dokumente') }}" title="Dokumente">
                <div class="menusub">
                    <i class="las la-file-alt"></i>
                    <span style="visibility: {{session('textshow')}};">Dokumente</span>
                </div>
            </a>
        @endif
        <div class="spacer"></div>
        <a href="{{ url('projekte') }}" title="Projekte">
            <div class="menusub">
                <i class="las la-cube"></i>
                <span style="visibility: {{session('textshow')}};">Projekte</span>
            </div>
        </a>
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
        <a href="{{ url('archiv') }}" title="Archiv">
            <div class="menusub">
                <i class="las la-folder-open"></i>
                <span style="visibility: {{session('textshow')}};">Archiv</span>
            </div>
        </a>
        @endif
        {{--
        <a href="{{ url('projekte') }}" title="Projekte">

            <div class="menusub">
                {!! file_get_contents(asset('images/icons/ordner.svg'))  !!}
                <span>Projekte</span>
            </div>
        </a>
        --}}
        {{--

          <a href="{{ url('projekte') }}"  title="Dokumente">
            <div class="menusub dokumenteicon">
                <span>Dokumente</span>
            </div>
        </a>

         --}}
        {{--
            <a href="{{ url('favoriten') }}"  title="Favoriten">
                <div class="menusub">
                    {!! file_get_contents(asset('images/icons/favoriten.svg'))  !!}
                    <span>Favoriten</span>
                </div>
            </a>
            --}}
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
            <div class="menutitel">
                <span style="visibility: {{session('textshow')}};">Verwaltung</span>
            </div>
        @endif
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' )
            <a href="{{ url('stammdaten') }}">
                <div class="menusub" title="Benutzer">
                    <i class="las la-user"></i>
                    <span style="visibility: {{session('textshow')}};">Benutzer</span>
                </div>
            </a>
        @endif
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ']  == 'ersteller' &&  session('userdata')['superuser_kunden'] == 1 )
            <a href="{{ url('kunden') }}">
                <div class="menusub" title="Kunden">
                    <i class="las la-users-cog"></i>
                    <span style="visibility: {{session('textshow')}};">Kunden</span>
                </div>
            </a>
        @endif
        @if(!empty(session('userdata')['rechte']['typ']) && session('userdata')['rechte']['typ'] == 'ersteller' &&  session('userdata')['superuser_dienstleister'] == 1  )
            <a href="{{ url('dienstleister') }}">
                <div class="menusub" title="Dienstleister">
                    <i class="las la-user-tie"></i>
                    <span style="visibility: {{session('textshow')}};">Dienstleister</span>
                </div>
            </a>
        @endif

        <div class="d-flex logout justify-content-center col-12 ">
            <a href="{{url('logout')}}" title="Logout">
                <i class="la la-door-open" style="font-size: 32px;"></i>
            </a>
        </div>
        <div class="d-flex dashboardslide justify-content-center col-12">
            <img class="leftarrow" style="{{(session('sidebarwidth') < 140  && session('sidebarwidth') != 0)? 'transform: rotate(180deg);' : ''}}" src="{{asset('images/spitzlinks.svg')}}" style="">
        </div>

        </div>
    </div>
    <div class="content col p-0 " style="margin-left: {{(!empty(session('sidebarwidth'))) ?  session('sidebarwidth') : '162'}}px;">
        @yield('content')
    </div>
</div>


<script>


    $(document).ready( function(){
        $('.dashboardslide').click( function() {
            var toggleWidth = $(".sidemenue").width() >= 140 ? "40" : "162";
            var textshow = $(".sidemenue").width() >= 140 ? "hidden" : "visible";
            var logotextshow = $(".sidemenue").width() >= 140 ? "hidden" : "visible";
            var logoshow = $(".sidemenue").width() >= 140 ? "block" : "none";

            console.log($(".sidemenue").width() , toggleWidth);
            $('.sidemenue').animate({ width: toggleWidth + "px"});
            $('.content').animate({ marginLeft: toggleWidth + "px"});
            $('.projektfooter').animate({ marginLeft: toggleWidth + "px"});
            $('.menutitel').children('span').css({visibility: textshow});
            $('.menusub').children('span').css({visibility: textshow});
            $.get('{{ url('sidebarwidth') }}' + '/'+ toggleWidth  + '/' + textshow);
            //$.get('{{ url('textshow') }}' + '/'+ textshow);
            if(toggleWidth >= 140){
                $('#logo_spacer').css({display: 'block'});
                $('.logoplacer').css({display: 'block'});
                $('#sym_spacer').css({display: 'none'});
                $('.leftarrow').css("transform", "rotate(0deg)");;
            }
            else{
                $('#logo_spacer').css({display: 'none'});
                $('.logoplacer').css({display: 'none'});
                $('#sym_spacer').css({display: 'block'});
                $('.leftarrow').css("transform", "rotate(180deg)");

            }
        });
/*
        var toggleWidth2 = $(".sidemenue").width();
        var textshow2 = $(".sidemenue").width() <= 140 ? "hidden" : "visible";
        $.get('{{ url('sidebarwidth') }}' + '/'+ toggleWidth2);
        $.get('{{ url('textshow') }}' + '/'+ textshow2);
        */
        //var textshow2 = $(".sidemenue").width() >= 140 ? "hidden" : "visible";
        //$.get('{{ url('sidebarwidth') }}' + '/'+ $(".sidemenue").width() );
       // $.get('{{ url('textshow') }}' + '/'+ textshow2);
    });
</script>

</body>
</html>
