@extends('layouts.app')
@section('content')
<div class="col" >
    <div id="StopModal" class="modal fade">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    Hinweis
                </div>
                <div class="modal-body">
                    <h4>Wollen Sie dieses Projekt wirklich stoppen?</h4>
                    <h2 id="stopjobname" style="color: #1b4b72;"></h2>
                    <div class="float-right ">
                        <input type="hidden" id="idstop" name="idstop" value="">


                    </div>
                    <div class="modal-footer mt-4">
                        <button onclick="jQuery('.absoften').hide();jQuery('.stopmodal').hide();" class="btn btn-primary float-right ">Projekt stoppen</button>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <div class=" col-12">
        <div class="brand_logo mt-4">
                <img src="images/TGD_Logo_RGB_positiv.svg" alt="TeamGoDirect"/>
        </div>
    </div>
<div>
    <div class="col-12 col-md-12 float-left">
        <div class="headline">
            <h2>Archiv</h2>
        </div>
        {{--
        <div class="bg-white col-12 p-3">
            <ul class="p-0">
                <li class="float-left mr-4 underline">Alle</li>
                <li class="float-left mr-4">Standard</li>
                <li class="float-left mr-4">Auto-Mailer</li>
            </ul>
        </div>
        --}}
        <div class="offene_aufgaben">
            <table class="table table-striped table-bordered" id="dashboardtable" >
                <thead>
                <tr>
                    <th class="preview  no-icon">
                    </th>
                    <th class="sorting">
                        Projektnummer
                    </th>
                    <th class="w-60">
                        Projektname
                    </th>
                    <th class="w-40">
                        Aktivität | Person
                    </th>
                    <th class="w-40">
                        Upload-Menge
                    </th>
                    <th data-date-format="ddmmyyyy" >
                        Datum
                    </th>
                    <th class="favorit text-center p-2 no-icon" >
                        <img src="(asset('images/fav_unset.svg')">
                    </th>
                    {{--
                    <th class="dashaktion text-center p-2 no-icon" >
                    </th>
                    --}}
                </tr>
                </thead>
                <tbody>
                @foreach($daten['aufgaben'] AS $aufgabe)


                    @if(!empty($aufgabe->id))

                    <tr class="topline">
                        <td style="max-width: 30px;"  >
                            @if(!empty($aufgabe->preview_img))
                               <img src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}" class="w-100" class="preview"  data-container="body"  title="{{$aufgabe->jobbezeichnung}}" data-toggle="popover" data-img="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}" />
                            @endif
                        </td>
                        <td  >

                            <a href="./jobdetails/{{$aufgabe->id}}"><span
                                        class="">{{$aufgabe->jobnummer}}</span></a>
                        </td>
                        <td >
                            <a href="./jobdetails/{{$aufgabe->id}}"><span
                                        class="">{{$aufgabe->jobbezeichnung}}</span></a>
                        </td>
                        <td>
                            {{--
                            @if(session('userdata')['rechte']['typ'] == 'ersteller')
                                {{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe->taetigkeit)}} {{(empty($aufgabe['last_action']['aktion'])? '': $aufgabe['last_action']['aktion']. ' | ' . $aufgabe['last_action']['name'])}}
                            @elseif(session('userdata')['rechte']['typ'] == 'kunde')
                                {{(empty($aufgabe->taetigkeit_kunde)? 'Angelegt': $aufgabe->taetigkeit_kunde)}} {{(empty($aufgabe->taetigkeit_kunde)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @elseif(session('userdata')['rechte']['typ'] == 'dienstleister')
                                {{(empty($aufgabe->taetigkeit_dienstleister)? 'Angelegt': $aufgabe->taetigkeit_dienstleister)}} {{(empty($aufgabe->taetigkeit_dienstleister)? '': ' | ' . $aufgabe->t_benutzer)}}
                            @endif
                            --}}

                            {{(empty($aufgabe['last_action']['id'])? 'Angelegt': $aufgabe['last_action']['aktion'])}} @if(!empty($aufgabe['last_action']['id']) && !empty($aufgabe['last_action']['user'])  ){{ ' | '. $aufgabe['last_action']['user'] }}@endif

                        </td>
                        <td class="">
                            {{$aufgabe->count}}
                        </td>
                        <td>
                                <span style="display: none;">{{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('YmdHi') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('YmdHi'))}}</span>
                                {{(!empty($aufgabe['last_action']['datetime']) ? \Carbon\Carbon::create($aufgabe['last_action']['datetime'])->format('d.m.Y H:i') : \Carbon\Carbon::create($aufgabe->jobanlage)->format('d.m.Y H:i'))}}
                        </td>

                        <td onclick="toggle_fav('{{ $aufgabe['id'] }}');" class=" text-center p-2">
                            <span style="display:none;">{{$aufgabe['j_favorit']}}</span>
                            <img src="{{($aufgabe['j_favorit'] ? asset('images/fav_set.svg') : asset('images/fav_unset.svg'))}}">
                    </tr>
                    <?PHP $job_id = $aufgabe->j_id; ?>
                    @endif
                @endforeach
                </tbody>
            </table>

        </div>
        <div style="height: 40px;"></div>

    </div>
</div>
</div>
    <script>

        $(document).ready(function () {
            $('#dashboardtable').DataTable({
                "order": [[ 4, "desc" ], [ 3, "desc" ]],
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Keine aktuellen Projekte vorhanden."
                }
            });
        });


        function klappandtoogle(elementid) {


            if (!$('#pfeil' + elementid).hasClass('rotate')) {


                $('#pfeil' + elementid).toggleClass('rotate');
                $('#content' + elementid).slideToggle();
            } else {
                $('#pfeil' + elementid).toggleClass('rotate-reset');
                $('#content' + elementid).slideToggle();
            }

        }

        // popovers initialization - on hover
        $('[data-toggle="popover"]').popover({
            html: true,
            trigger: 'click',
            placement: 'left',
            container: 'body',
            content: function () { return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />'; }
        });



        $(document).on("click", ".popover .closer" , function(){
            $(this).parents(".popover").popover('hide');
        });

    </script>

@endsection
