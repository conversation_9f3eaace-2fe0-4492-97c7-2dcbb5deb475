@extends('layouts.app')
@section('content')
    @if(!empty(session('error')))
        <div class="error">
                <?PHP echo session('error');
                session()->forget('error');
                ?>
        </div>
    @endif
    <div class="col-12 p-0 m-0 ">
        <div class="title align-middle">
            <div class="">
                <div class="p-2">
                    <a href="{{url('projekte')}}" style="font-size: 20px; color: #000;">
                        <i
                                class="las la-angle-left border-grey"></i> #{{$job->id}}
                        - {{$job->jobbezeichnung}}

                    </a><br>
                    @if($job->copiedFrom)
                        <small class="ml-5">{{$job->copiedFrom}}</small>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex flex-row p-0 m-0">
        <div class="tagtabs">
            <ul class="float-left">
                <a href="{{url('freigabe/' . $job->id.'/tag/0')}}">
                    <li class="float-left {{(empty($reg)) ?'aktiv' : ''}}">Jobübersicht</li>
                </a>
                <a href="{{url('freigabe/' . $job->id.'/reg/marketingcloud' )}}">
                    <li class="float-left {{(!empty($reg) && $reg=='marketingcloud') ?'aktiv' : ''}}">
                        Beladung MC
                    </li>
                </a>
            </ul>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
    <div class="col">
        <input type="hidden" id="akt_pdfpage" name="akt_pdfpage" value="1"/>
        <div class="col-12 col-md-12 float-left">
            <div class="">
                <div class="d-flex flex-row flex-wrap p-0 m-0">
                    @if($reg == 'marketingcloud')
                        @foreach($jobfiles AS $jobfile)
                            @if(empty($jobfile->geloescht_am) && empty($jobfile->geloescht_am))
                                @if(!empty($hochdocs->first()))
                                    <a href="{{url('weddingautopreview/'.$hochdocs->first()->id.'/file/'. $jobfile->id)}}">
                                        @else
                                            <a href="{{url('editorpreview/'.$jobfile->id.'/file/1')}}">
                                                @endif
                                                <div>
                                                    <div class="documentcard  {{(!empty($jobfile->generiert) ? 'greenborder' : 'orangeborder')}} {{(!empty($jobfile->stopzeit) ? 'redborder' : '')}}  ">
                                                        @if(1)
                                                            <div
                                                                    class="widgettitel {{(!empty($jobfile->generiert) ? 'gruenbg' : 'orangebg')}}">
                                                                <i class="las la-bell"></i>&nbsp;#{{$job->id}}
                                                                _{{$jobfile->step_id}}
                                                            </div>

                                                        @else
                                                            <div
                                                                    class="widgettitel small">
                                                                &nbsp;
                                                            </div>
                                                        @endif
                                                        <div
                                                                class="widgetpoints small ">
                                                            ...
                                                        </div>

                                                        <div
                                                                class="clearfix"></div>
                                                        <div
                                                                class="spacer"></div>

                                                        @if(1)
                                                            <div
                                                                    class="documenticon_hoch align-items-center ">
                                                                @if($jobfile->generiert )

                                                                    <img
                                                                            src="{{asset('images/PDF-Symbol.png')}}"
                                                                            width="100%">
                                                                @endif
                                                            </div>
                                                        @elseif(strpos($document->name ,'.pdf')  !== false  )
                                                            <div
                                                                    class="documenticon_pdf align-items-center ">
                                                                @if(!empty($document->inhalt) && substr(base64_encode($document->inhalt), 0,1) != 'J')
                                                                    <img
                                                                            src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}"
                                                                            class="w-100"
                                                                            class="preview"
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$document->id}}Modal"
                                                                            data-container="body"
                                                                            title="{{$document->id}}"/>
                                                                @elseif(!empty($document->inhalt))
                                                                    <canvas
                                                                            data=""
                                                                            type="application/pdf"
                                                                            id="preview_{{$document->id}}"
                                                                            class="pdf_inhalt"
                                                                            style="overflow: hidden; width: 100%; "
                                                                            data-toggle="modal"
                                                                            data-target="#preview_{{$document->id}}Modal"></canvas>
                                                                    <script>
                                                                        $(document).ready(function () {
                                                                            previewdocpdf({{$document->id}}, 'preview_{{$document->id}}', 1, 1, '{{$path}}');
                                                                        });
                                                                    </script>
                                                                @else
                                                                    <div
                                                                            class="documenticon_pdf align-items-center ">
                                                                        <img
                                                                                src="{{asset('images/PDF-Symbol.png')}}">
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        @endif
                                                        <div
                                                                class="name " style="white-space: nowrap;">
                                                        </div>
                                                        <div
                                                                class="document-title ">
                                                            &nbsp;<br>
                                                            @php
                                                                $transmitDate = \App\Models\Transmitdate::getTransmitDate($jobfile->id_job , $jobfile->step_id);
                                                            @endphp
                                                            @if(!empty($jobfile->PAL_date))
                                                                Archivierung
                                                                ab {{ \Carbon\Carbon::parse($transmitDate)->format('d.m.Y') }}
                                                            @else
                                                            @endif
                                                            &nbsp;
                                                        </div>

                                                        <div
                                                                class="document-info ">
                                                            <div>

                                                            </div>
                                                            <div>{{!empty($jobfile->datensaetze) ? $jobfile->datensaetze : '-'}}
                                                                Datensätze
                                                            </div>
                                                            <div
                                                                    class="">

                                                                {{ ( !$jobfile->generiert ? 'noch nicht generiert' : 'generiert' ) }}
                                                            </div>
                                                        </div>

                                                    </div>

                                                </div>
                                            </a>
                                @endif
                                @endforeach
                            @endif
                </div>

            </div>
        </div>

    </div>
    </div>

@endsection
