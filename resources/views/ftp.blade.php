@extends('layouts.app')
@section('content')
    <div class="ftp">
        @if(!empty(session()->get('error')))
            <br/>
            <div class="error">
                {{session()->get('error')}}
            </div>
            <br/>
        @endif
        <?PHP session()->forget('error'); ?>
        <div class="m-4 ">
            <h3>FTP-Download</h3>
            Hier bitte direkt <b>ALLE Jobs und Bestandteile zu einer Adressdatei auswählen</b>, da sie mit dem Upload in den Transfersafe (Datenübernahme) auf dem SFTP-Server des Kunden gelöscht werden!<br />
            ACHTUNG: Wenn die Datei mehreren Bestandteilen in einem Projekt zugeordnet werden soll, muss dies auch hier mehrfach hinterlegt werden. Eine nachträgliche Zuordnung ist nicht möglich.
        </div>
        @if(!empty($ftp))
            @foreach($ftp AS $key => $zeile)
                    @if(!empty($zeile['file']))
                        <ul class='col m-2'>
                            <li>
                                <div class="p-2">

                                    <h3 class="m-3">{{($zeile['file'])}} [{{($zeile['file_datum'])}}]</h3>

                                        <form method="post">
                                            {{csrf_field()}}


                                                @if(!empty($projekte->toArray()))
                                                     <div class="row d-flex m-2 p-0">
                                            <select id="z{{($key)}}" class="custom-select m-1 col-12 col-md-8 m-0">
                                                <option value="">Bitte wählen...</option>
                                                @if(!empty($projekte))
                                                    <div class="jobs">
                                                        @foreach($projekte AS $job)
                                                            <option value="{{$job['id']}}">{{($job['jobbezeichnung'])}}</option>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </select>

                                                    <button onclick="add_ftpproject($('#z{{$key}}').children('option:selected').val(), 'file{{($key)}}', {{$key}});  return false;" class="btn btn-primary  col-12 col-md-2">Projekt übernehmen</button>
                                            </div>
                                            <div class="file{{($key)}} m-2" >

                                            </div>
                                            <input type="hidden" name="filename" value="{{($zeile['file'])}}"/>
                                            <div class="clearfix"></div>
                                            <div class="m-2">
                                                <input type="checkbox"  id="chbx{{($key)}}" onclick="if(this.checked == true && $('.file{{($key)}}').children().length >= 1){$('#idpu{{($key)}}').removeClass('button-disabled'); $('#idpu{{($key)}}').addClass('btn-primary');$('#idpu{{($key)}}').attr('disabled', false);}else{$('#idpu{{($key)}}').removeClass('btn-primary');$('#idpu{{($key)}}').addClass('button-disabled');$('#idpu{{($key)}}').attr('disabled', true);} " > Mir ist bewusst, dass die Zuornung nicht rückgängig gemacht werden kann und die Daten nach der Zuordnung vom Server gelöscht werden.
                                            </div>
                                             <div class="clearfix m-0 p-0"></div>
                                            <button class="btn  m-2 button-disabled" disabled="disabled" id="idpu{{($key)}}" style="cursor: default;"  >in die Projekte übernehmen</button>
                                            @else
                                                <div>Es sind keine FTP-Projkete vorhanden.</div>
                                            @endif
                                        </form>
                                </div>

                            </li>
                        </ul>
                    @endif
            @endforeach
        @endif
    </div>

@endsection
