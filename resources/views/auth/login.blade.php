<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <script src="{{ asset('js/app.js') }}"></script>
    @if((config('app.mandant') != 'DIRECTHUB'))
        <link href="{{ asset('css/app_directhub.css') }}" rel="stylesheet" type="text/css"/>
    @else
        <link href="{{ asset('css/app_directhub.css') }}" rel="stylesheet" type="text/css"/>
    @endif
    @if((config('app.mandant') != 'DIRECTHUB'))
        <title>Transfersafe4SF</title>
        @else
        <title>DirectHUB</title>
    @endif
    <style>
        body {
                background-color: #120A36 !important;

            height: 100%;
        }
    </style>
</head>
<body class=" ">
<div class="d-flex  align-content-around flex-wrap justify-content-center h-100  ">
    <div class="centerloginbox">
        @if((config('app.mandant') != 'DIRECTHUB'))
        <div class="tgd_logo">
            <img src="images/TransferSafe_Logo_white.svg" alt="TransferSafe"/>
        </div>
        <div class="m3">&nbsp;</div>
            <div class="loginsubtitel">
                Automatisierte Printanstöße, API unabhängig.
            </div>
        @else
            <div class="tgd_logo">
                <img src="images/directhub_rgb_w.svg" alt="DirectHUB"/>
            </div>
            <div class="loginsubtitel">
                Automatisierte Printanstöße, API unabhängig.
            </div>
        @endif
        <div class="whitebox">
            <div class="whiteinner">
                @if(!empty(session('error')))
                    <div class="error">
                        <?PHP echo session('error'); ?>
                    </div>
                @endif
                <form method="post" action="./login">
                    {{csrf_field()}}
                    <div class="form-group">
                        <label>Ihre E-Mail Adresse</label>
                        <input name="user" placeholder="Geben Sie Ihre E-Mail Adresse ein" class="form-control"
                               value="{{ old('user') }}"><br/>
                        <label>Passwort</label>
                        <input type="password" placeholder="Geben Sie Ihr Passwort ein" class="form-control" name="pass"
                               id="tspasswort"
                               class="login-field login-field-password" value="{{ old('pass') }}"><br/>
                        <div class="passwort-vergessen"><a href="./forgotpassword">Passwort vergessen?</a></div>
                        <button type="submit" id="button" class="anmelden" value="login">Anmelden</button>
                    </div>

                </form>
                <div class="einloggen_mit">
                    Einloggen mit:
                </div>
                <div class="azure">
                    Microsoft Azure
                </div>
                <div class="abbinder">
                    @if((config('app.mandant') != 'DIRECTHUB'))
                    Aus rechtlichen Gründen ist es nicht möglich, einen automatischen Registrierungsprozess anzubieten.
                    Bitte kontaktieren Sie unseren Support, wenn Sie einen Zugang benötigen.<br/>
                    <br/>
                    @endif
                    Sie haben noch keinen Account?&nbsp;<a href="mailto:{{config('app.SUPPORT_MAIL')}}?subject=Support-Anfrage%20über%20{{config('app.APP_URL')}}">Support
                        kontaktieren</a>
                </div>
            </div>

        </div>
            <div class="boxfinish">

            </div>
        <div class="buttomnav">
            <a href="https://dialogagentur.de/kontakt/" target="_blank">Informationen zum @if((config('app.mandant') != 'DIRECTHUB'))TransferSafe @else DirectHUB @endif</a> | <a
                href="https://dialogagentur.de/agb/" target="_blank">AGBs</a> | <a
                href="https://dialogagentur.de/impressum/" target="_blank">Impressum/Datenschutz</a>
        </div>
    </div>
</div>

</body>
<script src="{{asset('js/hideShowPassword.min.js')}}"></script>
<script>
    $('#tspasswort').hideShowPassword({
        innerToggle: true,

        // Makes the toggle functional in touch browsers without
        // the element losing focus.
    });

    $(window).on('resize', function () {
        $('#tspasswort').hideShowPassword({
            innerToggle: true,
            wrapperWidth: true
            // Makes the toggle functional in touch browsers without
            // the element losing focus.
        });
    });
</script>

</html>
