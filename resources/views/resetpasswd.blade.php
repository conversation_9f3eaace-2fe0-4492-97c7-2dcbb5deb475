<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <script src="{{ asset('js/app.js') }}"></script>
    <script src="{{ asset('js/function_pass.js') }}"></script>
    @if((config('app.mandant') != 'DIRECTHUB'))
        <link href="{{ asset('css/app_directhub.css') }}" rel="stylesheet" type="text/css"/>
    @else
        <link href="{{ asset('css/app_directhub.css') }}" rel="stylesheet" type="text/css"/>
    @endif
    <link href="https://fonts.googleapis.com/css2?family=Rubik&display=swap" rel="stylesheet">
    @if((config('app.mandant') != 'DIRECTHUB'))
        <title>Transfersafe4SF</title>
    @else
        <title>DirectHUB</title>
    @endif
    <style>
        body {

                background-color: #120A36 !important;

            height: 100%;
        }
    </style>
</head>
<body class=" ">
<div class="d-flex  align-content-around flex-wrap justify-content-center h-100  ">
    <div class="centerloginbox">
        @if((config('app.mandant') != 'DIRECTHUB'))
            <div class="tgd_logo">
                <img src="{{asset('images/TransferSafe_Logo_white.svg')}}" alt="TransferSafe"/>
            </div>
            <div class="m3">&nbsp;</div>
            <div class="loginsubtitel">
                Automatisierte Printanstöße, API unabhängig.
            </div>
        @else
            <div class="tgd_logo">
                <img src="images/directhub_rgb_w.svg" alt="DirectHUB"/>
            </div>
            <div class="loginsubtitel">
                Automatisierte Printanstöße, API unabhängig.
            </div>
        @endif
            <div class="whitebox">
                <div class="whiteinner">
                    <div class="welcome">
                        Sie möchten Ihr Passwort zurücksetzen?<br/>
                        Bitte geben Sie hier Ihr Passwort ein:
                    </div>
                    @if(!empty(session('error')))
                        <div class="error">
                            <?PHP
                            echo session('error');
                            session()->flush();

                            ?>
                        </div>
                    @endif
                    @if(!empty(session('succsess')))
                        <div class="succsess">
                            <?PHP
                            echo session('succsess');
                            session()->flush();
                            ?>
                        </div>
                    @endif
                    <?PHP session()->forget(['succsess','error' ]); ?>
                    <form method="post">
                        <div class="form-group">
                            <div id='pass_ausgabe' style='display: none;'>
                                <span style="color: #0062a6;">Bitte wählen Sie ein Passwort mit mind. 8 Zeichen und mit 3 weiteren Kriterien:</span>
                                <ul>
                                    <li id='achtzeichen'>8 Zeichen</li>
                                    <li id='grossbuchstaben'>Großbuchstabe</li>
                                    <li id='kleinbuchstaben'>Kleinbuchstabe</li>
                                    <li id='sonderzeichen'>Sonderzeichen</li>
                                    <li id='zahl'>Zahl</li>
                                </ul>
                            </div>
                            {{csrf_field()}}
                            <input id="passwort_ready" name="passwort_ready" type="hidden" value="0"><br/>
                            <input name="token" type="hidden" value="{{$token}}"><br/>
                            <input name="passwd" placeholder="Passwort" class="form-control" type="password" id="tspasswort1"
                                   class="login-field login-field-password" required onkeyup="pass_check(this.value);"
                                   onblur="pass_check(this.value); hide_pass();" onfocus='show_pass();'><br/>
                            <input name="passwd_wdh" placeholder="Passwort-Wiederholung" class="form-control" id="tspasswort2" required
                                   class="login-field login-field-password" type="password" required
                                   onkeyup="pass_check(this.value);" onblur="pass_check(this.value); hide_pass();"
                                   onfocus='show_pass();'><br/>
                            <a href="{{url('/login')}}">zurück zum Login</a><br/>
                            <button type="submit" id="button" style="background-color: lightgrey;" disabled="disabled"
                                    value="login">Zurücksetzen
                            </button>
                        </div>
                    </form>

                    <div class="abbinder">
                        @if((config('app.mandant') != 'DIRECTHUB'))
                        Aus rechtlichen Gründen ist es nicht möglich, einen automatischen Registrierungsprozess anzubieten. Bitte kontaktieren Sie unseren Support, wenn Sie einen Zugang benötigen.<br/>
                        <br/>
                        @endif
                        <a href="mailto:{{config('app.SUPPORT_MAIL')}}?subject=Support-Anfrage%20über%20{{config('app.APP_URL')}}">Support kontaktieren</a>
                    </div>
                </div>
            </div>
        <div class="boxfinish">

        </div>
        <div class="buttomnav">
            <a href="https://dialogagentur.de/kontakt/" target="_blank">Informationen zum @if((config('app.mandant') != 'DIRECTHUB'))TransferSafe @else DirectHUB @endif</a> | <a
                href="https://dialogagentur.de/agb/" target="_blank">AGBs</a> | <a
                href="https://dialogagentur.de/impressum/" target="_blank">Impressum/Datenschutz</a>
        </div>
    </div>
</div>

</body>
<script src="{{asset('js/hideShowPassword.min.js')}}"></script>
<script>
    $('#tspasswort1').hideShowPassword({
        innerToggle: true,
        // Makes the toggle functional in touch browsers without
        // the element losing focus.


    });
    $('#tspasswort2').hideShowPassword({
        innerToggle: true,
        // Makes the toggle functional in touch browsers without
        // the element losing focus.


    });
</script>
</html>
