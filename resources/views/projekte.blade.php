@extends('layouts.app')
@section('content')
    <div class="col-12 p-0 m-0">
        <div class="title align-middle">
            <h3 class="p-2">Projekte</h3>
        </div>
        <div class="filter">
            <ul>
                <li onclick="sortListDir('jobcontainer');"><i class="las la-filter"></i>&nbsp;Sort: <span class="black">A-Z</span><i
                        class="las la-angle-down space"></i></li>
                <li><i class="las la-border-all"></i>&nbsp;Suche: <span class="black"><input name="volltextsuche"
                                                                                             id="volltextsuche"
                                                                                             onkeyup="filterSelection(this.value);"></span>
                </li>
            </ul>
            <div class="hinweis d-md-block d-none float-right pr-3">
                {{count($daten['jobs'])}} Projekt | {{(count($daten['jobs']) - $daten['countfreigaben'])}} Projekte
                nicht freigegeben
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="d-flex flex-row p-0 m-0">
            <div class="col">
                <div id="StopModal" class="modal fade">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                Hinweis
                            </div>
                            <div class="modal-body">
                                <h4>Möchten Sie wirklich dieses Projekt stoppen oder pausieren?</h4>
                                <h4 id="stopjobname" style="color: #1b4b72;"></h4>
                                <div class="float-right ">
                                    <input type="hidden" id="idstop" name="idstop" value="">
                                </div>
                                <div class="modal-footer mt-4">
                                    <button onclick="$('.absoften').hide();$('#StopModal').hide();"
                                            class="btn btn-primary float-right ">Nein, zurück
                                    </button>
                                    <button onclick="$('.absoften').hide();$('#StopModal').hide();"
                                            class="btn btn-primary float-right ">Ja, Projekt stoppen
                                    </button>
                                </div>
                            </div>
                        </div><!-- /.modal-content -->
                    </div><!-- /.modal-dialog -->
                </div><!-- /.modal -->
                <input type="hidden" id="akt_pdfpage" name="akt_pdfpage" value="1"/>

                @foreach($daten['aufgaben'] AS $aufgabe)
                    @if(!empty($aufgabe->id))
                        <div id="preview_{{$aufgabe->id}}Modal" class="modal fade">
                            <div class="modal-dialog modal-xl" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <strong>{{$aufgabe->jobbezeichnung}}</strong>
                                        <div calss="col-1 float-right"
                                             onclick="$('#preview_{{$aufgabe->id}}Modal').modal('hide');">X
                                        </div>
                                    </div>
                                    <div class="modal-body">
                                        @if(!empty($aufgabe->preview_img) && substr(base64_encode($aufgabe->preview_img), 0,1) != 'J')
                                            <img class="w-100" class="preview" data-container="body"
                                                 title="{{(!empty($aufgabe->preview_name) ? $aufgabe->preview_name : '')}}"
                                                 src="data:image/jpg;base64,{{base64_encode($aufgabe->preview_img)}}"/>
                                        @else
                                            <div id="seitenzahlen">
                                                <button
                                                    onclick="previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big',  parseInt($('#akt_pdfpage').val())  - 1 , 1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) - 1); $('#zoom').val(1); if(parseInt($('#akt_pdfpage').val()) < 1) $('#akt_pdfpage').val(1); return false;">
                                                    <
                                                </button>
                                                <button
                                                    onclick="previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big',  parseInt($('#akt_pdfpage').val())  + 1, 1 ); $('#zoom').val(1); $('#akt_pdfpage').val(parseInt($('#akt_pdfpage').val()) + 1); return false;"
                                                    id="pdf_next">>
                                                </button>

                                            </div>
                                            <input name="hiddenpage" value="{{(!empty($pdfpage) ? $pdfpage :'' )}}"
                                                   type="hidden">
                                            <div class="pdfview">
                                                <div class="pdfviewer" style="overflow: hidden !important;">
                                                    <img src="" id="img_inhalt" class="img_inhalt" width="100%">
                                                    <canvas data="" type="" id="preview_{{$aufgabe->id}}_big"
                                                            class="pdf_inhalt" style="overflow: hidden; width: 100%;">
                                                    </canvas>
                                                    <script>
                                                        $(document).ready(function () {
                                                            previewpdf({{$aufgabe->id}}, 'preview_{{$aufgabe->id}}_big', 1, 1);
                                                        });
                                                    </script>

                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div><!-- /.modal-content -->
                            </div><!-- /.modal-dialog -->
                        </div><!-- /.modal -->

                    @endif
                @endforeach
                <div class="col-12 col-md-12 float-left">
                    <ul class="p-0 m-0" id="jobcontainer">
                        <div class="d-flex flex-row flex-wrap p-0 m-0">
                            @foreach($daten['jobs'] AS $job)
                                @if( ($userrechte[$job->id]['ansehen'] == 1) )
                                    <li class="m-0 p-0">
                                        <a href="{{url('freigabe/'.$job->id)}}"
                                           class="filterDiv {{$job->jobbezeichnung}}" style="white-space: nowrap;">
                                            <div>
                                                <div
                                                    class="projectcard {{( $job->freigegeben == 1 ? 'job_freigabe':'job_nichtfreigegeben')}} ">

                                                    @if(!empty($job->id))
                                                        <div class="previewimg">
                                                            @if(!empty($job->preview_img) && substr(base64_encode($job->preview_img), 0,1) != 'J')
                                                                <img class="w-100" class="preview" data-container="body"
                                                                     title="{{!empty($job->preview_name) ? $job->preview_name : ''}}"
                                                                     src="data:image/jpg;base64,{{base64_encode($job->preview_img)}}"/>
                                                            @endif
                                                        </div>
                                                        <div class="cardinfo ">
                                                            <div class="name">
                                                                #{{((strlen($job->id) > 13) ? substr($job->id, 0,13)."..." : $job->id) }}
                                                            </div>
                                                            <div class="job-title"
                                                                 title="{{str_replace("_","_ ",$job->jobbezeichnung)}}">
                                                                {{((strlen($job->jobbezeichnung) > 20) ? substr($job->jobbezeichnung, 0,20)."..." : $job->jobbezeichnung) }}
                                                            </div>
                                                            <div
                                                                class="bestandteil-info {{(($job->count_freigaben == $job->doccount && $job->doccount != 0) ? 'bestandteil-freigabe': '') }}">
                                                                <div class="binfo-left">
                                                                    <i class="las la-file-alt binfo-icon {{(($job->count_freigaben == $job->doccount && $job->doccount != 0 )? 'icon-freigabe': '') }}"></i>&nbsp;{{$job->count_bestandteile}}
                                                                    &nbsp;Dokumente
                                                                </div>
                                                                <div class="binfo-right ">
                                                                    {{$job->count_freigaben}}/{{$job->doccount}}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </a>
                                    </li>
                                @endif
                            @endforeach
                        </div>
                    </ul>
                    <div style="height: 40px;"></div>

                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    {{-- @if(session('userdata')['rechte']['typ'] == 'kunde')--}}
    @if(1)
        <div class="col-11  p-2 m-4">
            <div class="col-12 bg-white responsebox m-2 ">
                <h4>Sonstige Responses</h4>
                <?PHP $responsedateien = App\Models\SonstigeResponses::Where('dateigroesse', '!=', 0)->orderBy('created_at', 'DESC')->get(['id', 'file_name', 'dateigroesse', 'geloescht_am', 'created_at']); ?>
                @if(count($responsedateien) != 0 )
                    @foreach($responsedateien AS $responsedatei)
                        <ul class="d-md-flex   p-0 ml-3  m-0 autofiles border-top">
                            @if(APP\Models\User::find(Auth::id())->is_kunde())
                                @if(empty($responsedatei->geloescht_am))
                                    <li class="align-self-start col-md-6 col-12"><a
                                            href="{{url('/sonstigeresponsedownload/'.$responsedatei->id)}}"
                                        >{{$responsedatei->file_name}}</a>
                                        ({{number_format($responsedatei->dateigroesse/1024, 1, ',', '')}}
                                        KB)
                                    </li>
                                @else
                                    <li class="align-self-start col-md-6 col-12">{{$responsedatei->file_name}}<br/>
                                        <small>[gelöscht: {{\Carbon\Carbon::parse($responsedatei->geloescht_am)->format('d.m.Y')}}
                                            ]</small>
                                    </li>
                                @endif
                            @else
                                <li class="align-self-start col-md-6 col-12">{{$responsedatei->file_name}}
                                    ({{number_format($responsedatei->dateigroesse/(1024), 1, ',', '')}}
                                    KB)
                                </li>
                            @endif
                            <li class="align-self-start col-md-2 col-12">{{\Carbon\Carbon::parse($responsedatei->created_at)->format('d.m.Y H:i')}}</li>
                            <li class="align-self-start col-md-1 col-12"></li>

                            <li class="align-self-start col-md-3 col-12">
                                <ul class="status p-0 m-0">
                                    <li>
                                        <div class="dot"></div>
                                        übernommen
                                    </li>
                                </ul>
                            </li>
                            <div></div>


                        </ul>
                        <div class="clearfix"></div>

                        <?PHP
                        if (!empty($responsedatei)) {
                            $sr_downloads = App\Models\SonstigerDownload::Where('id_sr', $responsedatei->id)->orderBy('created_at', 'DESC')->get();
                        }
                        ?>

                        @if(!empty($sr_downloads))
                            <small class="">
                                <ul class=" p-0 m-0 ml-5">
                                    @foreach($sr_downloads as $sr_download)
                                        <li class="m-0 p-0">
                                            {{\Carbon\Carbon::parse($sr_download->datetime)->format('d.m.Y H:i')}}
                                            Heruntergeladen
                                            / {{App\Models\User::find($sr_download->id_user)->vorname}} {{App\Models\User::find($sr_download->id_user)->name}}
                                        </li>
                                    @endforeach
                                </ul>
                            </small>
                        @endif

                    @endforeach
                @else
                    <div>Es wurden noch keine sonstigen Response zurückgeliefert.</div>
                @endif
            </div>
        </div>
    @endif


    <script>

        $(document).ready(function () {
            $('#dashboardtable').DataTable({
                "order": [[4, "desc"], [3, "desc"]],
                searching: false,
                paging: false,
                info: false,
                "language": {
                    "emptyTable": "Keine aktuellen Projekte vorhanden."
                }
            });
        });


        function klappandtoogle(elementid) {


            if (!$('#pfeil' + elementid).hasClass('rotate')) {


                $('#pfeil' + elementid).toggleClass('rotate');
                $('#content' + elementid).slideToggle();
            } else {
                $('#pfeil' + elementid).toggleClass('rotate-reset');
                $('#content' + elementid).slideToggle();
            }

        }

        // popovers initialization - on hover
        $('[data-toggle="popover"]').popover({
            html: true,
            trigger: 'click',
            placement: 'left',
            container: 'body',
            content: function () {
                return '<img  src="' + $(this).data('img') + '" class="w-100 closer" />';
            }
        });


        $(document).on("click", ".popover .closer", function () {
            $(this).parents(".popover").popover('hide');
        });


        filterSelection("all");

        function filterSelection(c) {
            var x, i;
            x = document.getElementsByClassName("filterDiv");
            if (c == "all") c = "";
            // Add the "show" class (display:block) to the filtered elements, and remove the "show" class from the elements that are not selected
            for (i = 0; i < x.length; i++) {
                w3RemoveClass(x[i], "show");
                if (x[i].className.toLocaleLowerCase().indexOf(c.toLocaleLowerCase()) > -1) w3AddClass(x[i], "show");
            }
        }

        // Show filtered elements
        function w3AddClass(element, name) {
            var i, arr1, arr2;
            arr1 = element.className.split(" ");
            arr2 = name.split(" ");
            for (i = 0; i < arr2.length; i++) {
                if (arr1.indexOf(arr2[i]) == -1) {
                    element.className += " " + arr2[i];
                }
            }
        }

        // Hide elements that are not selected
        function w3RemoveClass(element, name) {
            var i, arr1, arr2;
            arr1 = element.className.split(" ");
            arr2 = name.split(" ");
            for (i = 0; i < arr2.length; i++) {
                while (arr1.indexOf(arr2[i]) > -1) {
                    arr1.splice(arr1.indexOf(arr2[i]), 1);
                }
            }
            element.className = arr1.join(" ");
        }


        function sortListDir(element) {
            var list, i, switching, b, shouldSwitch, dir, switchcount = 0;
            list = document.getElementById(element);
            switching = true;
            // Set the sorting direction to ascending:
            dir = "asc";
            // Make a loop that will continue until no switching has been done:
            while (switching) {
                // Start by saying: no switching is done:
                switching = false;
                b = list.getElementsByTagName("LI");
                // Loop through all list-items:
                for (i = 0; i < (b.length - 1); i++) {
                    // Start by saying there should be no switching:
                    shouldSwitch = false;
                    /* Check if the next item should switch place with the current item,
                    based on the sorting direction (asc or desc): */
                    if (dir == "asc") {
                        if (b[i].innerHTML.toLowerCase() > b[i + 1].innerHTML.toLowerCase()) {
                            /* If next item is alphabetically lower than current item,
                            mark as a switch and break the loop: */
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == "desc") {
                        if (b[i].innerHTML.toLowerCase() < b[i + 1].innerHTML.toLowerCase()) {
                            /* If next item is alphabetically higher than current item,
                            mark as a switch and break the loop: */
                            shouldSwitch = true;
                            break;
                        }
                    }
                }
                if (shouldSwitch) {
                    /* If a switch has been marked, make the switch
                    and mark that a switch has been done: */
                    b[i].parentNode.insertBefore(b[i + 1], b[i]);
                    switching = true;
                    // Each time a switch is done, increase switchcount by 1:
                    switchcount++;
                } else {
                    /* If no switching has been done AND the direction is "asc",
                    set the direction to "desc" and run the while loop again. */
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
        }
    </script>

@endsection
