@extends('layouts.app')
@section('content')
    <?PHP $tmp_buchstabe = ''; ?>
    <div class="col-12 p-0 m-0">
        <div class="d-flex flex-row p-0 m-0">

            @if(!empty(session()->get('error')))
                <br/>
                <div class="error">
                    {{session()->get('error')}}
                </div>

                <br/>
                <?PHP
                session()->forget('error');
                ?>
            @endif
            <div class="col p-0 m-0 ">
                <div class="title align-middle">
                    <h3 class="p-2">Dienstleister</h3>
                </div>
                <div class="d-flex flex-row flex-wrap p-0 m-0">
                    {{--
                    <a href="{{url('/dienstleister/new')}}">
                        <div>
                            <div class="dokumentencard align-content-center text-center"  style="color: #4D7CFE; font-size: 20px;">
                                <i class="las la-plus-circle" style="font-size: 40px; margin-top: 120px;"></i><br/>
                                Dienstleister hinzufügen
                            </div>
                        </div>
                    </a>
                    --}}
                    @foreach($dienstleister AS $d_id => $dienstleister_single)
                        <a href="{{url('/dienstleister/' .$d_id )}}">
                            <div>
                                <div class="dokumentencard align-content-center text-center {{( $dienstleister_single['aktiv'] == 1 ? '' :'inaktiv')}} "  style="color: #4D7CFE; font-size: 20px;">
                                    <b>{{substr($dienstleister_single['dienstleister'], 0, 20)}}</b><br>
                                </div>
                            </div>
                        </a>
                    @endforeach

                </div>
            </div>
            <div class="float-right bg-white p-3" style="display: block;" id="sidebar_right h-100">

                <form method="post">
                    {{csrf_field()}}
                    @if(!empty($id))
                        <h3 style="color: #4D7CFE;">Dienstleister berarbeiten</h3>
                        <input type="hidden" name="d_id" value="{{$id}}">
                        <div class="form-group row m-1">
                            <input name="firma" type="text" class="form-control col-12 col-md-3 float-left m-1"
                                   placeholder="Dienstleister" value="{{$dienstleister[$id]['dienstleister']}}">
                            <input type="text"
                                   class="jscolor {hash:true, required:false} form-control col-12 col-md-3 float-left m-1"
                                   name="farbe" value="{{$dienstleister[$id]['farbe']}}" placeholder="Farbe"><br/>
                            <button class="btn btn-primary ">Speichern</button>
                        </div>
                        <div class="clearfix"></div>
                        <div class=" form-check-inline row m-1">
                            <label for="aktiv" class="form-check-labe float-left m-2">aktiv</label>
                            <input type="checkbox"
                                   name="aktiv" {{( ($dienstleister[$id]['aktiv'])? 'checked=checked': '')}} >
                        </div>
                        <h5><i class="las la-info-circle"></i>Dienstleister-Informationen</h5>
                        <div class="field-title">Firmenname</div>
                        <div><input class="field-input" placeholder="Musterfirma GmbH"
                                    value="{{$dienstleister[$id]['dienstleister']}}"/></div>
                        <div class="field-title">E-Mail</div>
                        <div><input class="field-input" placeholder="<EMAIL>"/></div>
                        <div class="field-title">Telefon</div>
                        <div><input class="field-input" placeholder="040 / 20940212"/></div>
                        <div class="field-title">Rolle</div>
                        <div><input class="field-input" placeholder="Rolle des Dienstleisters"/></div>
                    @else
                        <h3 style="color: #4D7CFE;">Neuen Dienstleister hinzufügen</h3>
                        <div class="form-group row m-1">
                            <input type="hidden" name="k_id" value="">
                            <input name="firma" type="text" class="form-control col-12 col-md-3 float-left m-1"
                                   placeholder="Firma">
                            <input type="text" name="farbe" value=""
                                   class="jscolor {hash:true, required:false} form-control col-12 col-md-3 float-left m-1"
                                   placeholder="Farbe">

                            <button class="btn btn-primary  float-left">Speichern</button>
                        </div>
                        <div class="clearfix"></div>

                        <div class=" form-check-inline row m-1">
                            <label for="aktiv" class="form-check-labe float-left m-2">aktiv</label>
                            <input type="checkbox" name="aktiv" class="form-check-input float-left m-1" checked=checked>
                        </div>
                    @endif

                </form>
            </div>
        </div>
    </div>
    <script>
        @if(!empty($id)) $('#sidebar_right').animate({width: 'toggle'}, 350);@endif
    </script>
@endsection
