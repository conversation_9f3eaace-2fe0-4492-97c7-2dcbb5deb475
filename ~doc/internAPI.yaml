openapi: 3.0.3
info:
  title: TS4SF - internal API
  description: Internal TS4SF-API for Editor exchange
  contact:
    email: ha<PERSON><PERSON>@quaintix.com
  version: "1.0.14"
servers:
  - url: https://ts4sf.local/api/
tags:
  - name: images
    description: image handling
  - name: fonts
    description: var handling
  - name: pages
    description: page handling
  - name: styles
    description: style handling
  - name: templates
    description: template handling
  - name: vars
    description: var handling
  - name: test data
    description: test data handling
  - name: documents
    description: Operations about user
security:
  - bearerAuth: []
paths:
  /pageformats/get/:
    get:
      tags:
        - pages
      summary: get list of page formats
      description: get list of page formats with address field postition, etc.
      operationId: getPageFormats
      responses:
        "200":
          description: Found page Formats
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPageFormatsResponseDto'
        "404":
          description: No page formats found
  /pages/get/{jobId}:
    get:
      tags:
        - pages
      summary: get last version of page construct by jobId
      description: get last version of page construct by jobId
      operationId: getPagesByJobId
      parameters:
        - name: jobId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Found last version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPagesResponseDto'
        "404":
          description: No page construct found
  /pages/get/{jobId}/{version}:
    get:
      tags:
        - pages
      summary: get version of page construct by jobId
      description: get version of page construct by jobId
      operationId: getPagesByJobIdAndVersion
      parameters:
        - name: jobId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: version
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: integer
      responses:
        "200":
          description: Found requested version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPagesResponseDto'
        "404":
          description: No page construct found
  /image/get/{hash}:
    get:
      tags:
        - images
      summary: get image by hash
      description: get image by hash
      operationId: getImageByHash
      parameters:
        - name: hash
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Found requested version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetImageResponseDto'
        "404":
          description: No page construct found
  /images/get:
    get:
      tags:
        - images
      summary: get json of images
      description: get json of images
      operationId: getImages
      responses:
        "200":
          description: Found requested version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetImagesResponseDto'
        "404":
          description: No page construct found
  /images/folder/get:
    get:
      tags:
        - images
      summary: get json of image folders
      description: get json of image folders
      operationId: getImageFolders
      responses:
        "200":
          description: Found image folders
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetImageFoldersResponseDto'
        "404":
          description: No image folders found
  /images/folder/create:
    post:
      tags:
        - images
      summary: create a new image folder
      description: create a new image folder (tag)
      operationId: createImageFolder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                folderName:
                  type: string
                  example: "My New Folder"
                parentId:
                  type: integer
                  nullable: true
                  example: 1
              required:
                - folderName
      responses:
        "201":
          description: Folder created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  folderName:
                    type: string
                  parentId:
                    type: integer
                    nullable: true
                  message:
                    type: string
        "400":
          description: Bad request - invalid input
        "409":
          description: Folder with same name already exists
        "500":
          description: Server error
  /images/folder/delete/{folderId}:
    delete:
      tags:
        - images
      summary: delete an image folder
      description: delete an image folder (tag) if it has no images or subfolders
      operationId: deleteImageFolder
      parameters:
        - name: folderId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Folder deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        "400":
          description: Cannot delete folder - contains images or subfolders
        "404":
          description: Folder not found
        "500":
          description: Server error
  /images/folders/update:
    put:
      tags:
        - images
      summary: update folder assignments for images
      description: update folder assignments for one or multiple images
      operationId: updateImageFolders
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                images:
                  type: array
                  items:
                    type: object
                    properties:
                      imageId:
                        type: integer
                        example: 123
                      folderIds:
                        type: array
                        items:
                          type: integer
                        example: [1, 3]
              example:
                images:
                  - imageId: 123
                    folderIds: [1, 3]
                  - imageId: 124
                    folderIds: []
      responses:
        "200":
          description: Image folders updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Image folders updated successfully"
                  updatedImages:
                    type: array
                    items:
                      type: object
                      properties:
                        imageId:
                          type: integer
                        folderIds:
                          type: array
                          items:
                            type: integer
        "400":
          description: Bad request - invalid input
        "500":
          description: Server error
  /templates/get:
    get:
      tags:
        - templates
      summary: get json of templates
      description: get json of templates
      operationId: getTemplates
      responses:
        "200":
          description: Found templates
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTemplatesResponseDto'
        "404":
          description: No page construct found
  /template/store:
    post:
      tags:
        - templates
      summary: store template construct by id
      description: store template construct by id
      operationId: storeTemplateById
      requestBody:
        required: true  # Specifies that this request body is mandatory
        content:
          application/json:  # The content type of the request body
            schema:  # The schema for validating the request body
              type: object  # The request body is an object
              properties:
                id:
                  type: integer
                  example: 23 | null
                name:
                  type: string
                  example: Template 23
                html:
                  type: string
                  example: 657b3b17d78c96cb1ff8a8fe60b5e4c072d8bc910896aa66f90491ccc11873ee
      responses:
        "202":
          description: Successful stored
          content:
            application/json:
              schema:
                type: string
                example: Successful stored
        "409":
          description: Could not be saved
        "400":
          description: Bad Request name and hash should be set
  /pages/store:
    post:
      tags:
        - pages
      summary: store page construct by jobId
      description: store page construct by jobId
      operationId: storePagesByJobId
      requestBody:
        required: true  # Specifies that this request body is mandatory
        content:
          application/json:  # The content type of the request body
            schema:  # The schema for validating the request body
              type: object  # The request body is an object
              properties:
                id:
                  type: integer
                  example: 719
                css:
                  type: string
                  example: 657b3b17d78c96cb1ff8a8fe60b5e4c072d8bc910896aa66f90491ccc11873ee
                pages:
                  type: string
                  example: [
                    {
                      "html": "86cfeee6df382a203f626305afbca44d228cac67acd151ccf2db29530c548fea",
                      "json": "72d8bc910896aa66f90491ccc11873ee657b3b17d78c96cb1ff8a8fe60b5e4c0"
                    },
                    {
                      "html": "c7960f2ad3d7e59c4e85cf867ebe50cf7a23390eeea3275ec177c88b68a625a8",
                      "json": "72d8bc91011873ee657b3b17d78c96cb1ff8a8fe60b5e4c0896aa66f90491ccc"
                    }
                  ]
      responses:
        "200":
          description: Pages Saved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPagesResponseDto'
        "409":
          description: Pages could not be saved
  /pages/get/{jobId}/history:
    get:
      tags:
        - pages
      summary: get version history of pages
      description: get version history of pages
      operationId: getVersionHistory
      responses:
        "200":
          description: Json with versions
          content:
            application/text:
              example: [{"version":9,"created_at":"2025-04-24T14:24:25.000000Z"},{"version":8,"created_at":"2025-04-24T14:12:25.000000Z"},{"version":7,"created_at":"2025-04-24T14:04:20.000000Z"},{"version":6,"created_at":"2025-04-24T03:29:19.000000Z"},{"version":5,"created_at":"2025-04-23T15:48:03.000000Z"},{"version":4,"created_at":"2025-04-23T15:48:02.000000Z"},{"version":3,"created_at":"2025-04-17T11:57:14.000000Z"},{"version":2,"created_at":"2025-04-15T04:40:43.000000Z"},{"version":1,"created_at":"2025-04-15T04:39:33.000000Z"},{"version":0,"created_at":"2025-03-19T14:24:27.000000Z"}]
        "400":
          description: error getting versions
        "404":
          description: Document not found
  /pages/set/{jobId}/frontify:
    post:
      tags:
        - pages
      summary: set frontify flag
      description: set frontify flag
      operationId: setFrontifyFlag
      responses:
        "200":
          description: Pages Saved
          content:
            application/text:
              example: Successful set frontify flag
        "400":
          description: frontify flag could not be set
        "404":
          description: Document not found
  /pages/get/{jobId}/frontify:
    get:
      tags:
        - pages
      summary: get frontify flag
      description: get frontify flag
      operationId: getFrontifyFlag
      responses:
        "200":
          description: Pages Saved
          content:
            application/text:
              example: ['frontify': 1]
        "400":
          description: frontify flag could not be set
        "404":
          description: Document not found
  /pages/delete/{jobId}/frontify:
    post:
      tags:
        - pages
      summary: delete frontify flag
      description: delete frontify flag
      operationId: deleteFrontifyFlag
      responses:
        "200":
          description: frontify flag deleted
          content:
            application/text:
              example: Successful deleted frontify flag
        "400":
          description: frontify flag could not be deleted
        "404":
          description: Document not found
  /vars/get/{jobId}:
    get:
      tags:
        - vars
      summary: get used vars in job by jobId
      description: get used vars in job by jobId
      operationId: getVars
      parameters:
        - name: jobId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "201":
          description: Registration created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetVarsResponseDto'
        "404":
          description: Vars not found
  /testdata/get/{jobId}:
    get:
      tags:
        - test data
      summary: get test data from job by jobId
      description: get test data from job by jobId
      operationId: getTestData
      parameters:
        - name: jobId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "201":
          description: Registration created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTestDataResponseDto'
        "404":
          description: Test data not found
  /image/store:
    put:
      tags:
        - images
      summary: store image file
      description: store image file with hash
      operationId: storeImage
      parameters:
        - in: header
          name: filename
          required: true
          description: filename
          schema:
            type: string
        - in: header
          name: folderId
          required: false
          description: single folder ID for backward compatibility
          schema:
            type: integer
        - in: header
          name: folderIds
          required: false
          description: comma-separated list of folder IDs
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: "binary"
      responses:
        "202":
          description: Successful stored
          content:
            application/json:
              schema:
                type: string
                example: d37e55dd637cd2771b58c740c417cdd013ef04472910167fc2846b96488acf47.png
        "304":
          description: null if not possible to store
  /html/store:
    put:
      tags:
        - documents
      summary: store html file
      description: store html file with hash
      operationId: storeHtml
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: "binary"
      responses:
        "202":
          description: Successful stored
          content:
            application/json:
              schema:
                type: string
                example: d37e55dd637cd2771b58c740c417cdd013ef04472910167fc2846b96488acf47
        "304":
          description: null if not possible to store
  /json/store:
    put:
      tags:
        - documents
      summary: store json file
      description: store json file with hash
      operationId: storeJson
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: "binary"
      responses:
        "202":
          description: Successful
          content:
            application/json:
              schema:
                type: string
                example: d37e55dd637cd2771b10167fc2846b96488acf417cdd013ef044729758c740c4
        "304":
          description: null if not possible to store
  /css/store:
    put:
      tags:
        - documents
      summary: store json file
      description: store json file with hash
      operationId: storeCss
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: "binary"
      responses:
        "202":
          description: Successful stored
          content:
            application/json:
              schema:
                type: string
                example: d37e55dd637cd2771b10167fc2846b96488acf4758c740c417cdd013ef044729
        "304":
          description: null if not possible to store
  /fonts/get:
    get:
      tags:
        - fonts
      summary: get json of fonts
      description: get json of fonts
      operationId: getFonts
      responses:
        "200":
          description: Found requested version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFontsResponseDto'
        "404":
          description: No fonts found
  /font/store:
    put:
      tags:
        - fonts
      summary: store json file
      description: store font file with hash
      operationId: storeFont
      requestBody:
        required: true
        content:
          application/octet-stream:
            schema:
              type: string
              format: "binary"
      responses:
        "202":
          description: Successful
          content:
            application/json:
              schema:
                type: string
                example: b96488acf4758c740c417d37e55dd637cd2771b10167fc2846cdd013ef044729.ttf
        "304":
          description: null if not possible to store
  /colors/get:
    get:
      tags:
        - styles
      summary: get json of colors
      description: get json of colors
      operationId: getColors
      responses:
        "200":
          description: Found requested version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetColorsResponseDto'
        "404":
          description: No fonts found
components:
  securitySchemes:
    bearerAuth:            # arbitrary name for the security scheme
      type: http
      scheme: bearer
  schemas:
    GetVarsResponseDto:
      type: array
      items:
        type: string
      example: ["Nr","RE_Anrede","RE_Akad_Titel","RE_Vorname","RE_Nachname","RE_Str","RE_Hausnr","RE_Hausnr_Ergaenzung","RE_PLZ","RE_Ort","RE_Land","Liefer_Str",...]
      xml:
        name: getVarsResponseDto
    GetTestDataResponseDto:
      type: array
      items:
        type: string
      example: ["Nr","RE_Anrede","RE_Akad_Titel","RE_Vorname","RE_Nachname","RE_Str","RE_Hausnr","RE_Hausnr_Ergaenzung","RE_PLZ","RE_Ort","RE_Land","Liefer_Str",...]
      xml:
        name: getTestDataResponseDto
    GetPageFormatsResponseDto:
      type: array
      items:
        type: object
      example: [{"name":"A4 Hochformat","description":"A4 Hochformat","height":297,"width":210,"trim_top":5,"trim_right":5,"trim_bottom":5,"trim_left":5,"address_x":20,"address_y":45,"address_w":"90","address_h":"45","fold_marks":"[{\"direction\":\"horizontal\",\"position\":105},{\"direction\":\"horizontal\",\"position\":210}]","pagenumber_w":174.75,"pagenumber_y":273.34,"sender_y":5,"recipient_y":26.7}]
      xml:
        name: getPageFormatsResponseDto
    GetImagesResponseDto:
      type: array
      items:
        type: object
      example: [{"id":1,"folders":[1,3],"thumb":"<hash>.jpg","image":"<hash>.jpg","originalName":"test.jpg"},{"id":2,"folders":[],"thumb":"<hash>.jpg","image":"<hash>.jpg","originalName":"test.jpg"}]
      xml:
        name: getImagesResponseDto
    GetImageFoldersResponseDto:
      type: array
      items:
        type: object
      example: [{"id":1,"folderName":"Gas","parentId":null},{"id":3,"folderName":"Logos","parentId":1},{"id":4,"folderName":"Logos","parentId":2},{"id":2,"folderName":"Strom","parentId":null}]
      xml:
        name: getImageFoldersResponseDto
    GetFontsResponseDto:
      type: array
      items:
        type: object
      example: [{"name":"NeoSansforeprimo-Bold","path":"\/Users\/<USER>\/Sites\/localhost\/TS3.0\/public\/fonts\/pdf\/NeoSansforeprimo-Bold.ttf","originalName":"NeoSansforeprimo-Bold.ttf"},{"name":"NeoSansforeprimo-Italic","path":"\/Users\/<USER>\/Sites\/localhost\/TS3.0\/public\/fonts\/pdf\/NeoSansforeprimo-Italic.ttf","originalName":"NeoSansforeprimo-Italic.ttf"},{"name":"NeoSansforeprimo-Medium","path":"\/Users\/<USER>\/Sites\/localhost\/TS3.0\/public\/fonts\/pdf\/NeoSansforeprimo-Medium.ttf","originalName":"NeoSansforeprimo-Medium.ttf"},{"name":"NeoSansforeprimo-Regular","path":"\/Users\/<USER>\/Sites\/localhost\/TS3.0\/public\/fonts\/pdf\/NeoSansforeprimo-Regular.ttf","originalName":"NeoSansforeprimo-Regular.ttf"}]
      xml:
        name: getFontsResponseDto
    GetTemplatesResponseDto:
      type: array
      items:
        type: object
      example: [{"name":"Template 1","html":"d37e55dd637cd2771b58c740c417cdd013ef04472910167fc2846b96488acf47","id":"1"},{"name":"Template 2","html":"417cdd013ef04472910167fc2846b96488accf474f47417cdd0c2846b8acf47","id":"1"}]
      xml:
        name: getTemplatesResponseDto
    GetColorsResponseDto:
      type: array
      items:
        type: object
      example: [{"name":"eprimo Hellgr\u00fcn","hex":"#B4DA04","rgb":"180,218,4","cmyk":"39,0,96,0"},{"name":"eprimo Dunkelgr\u00fcn","hex":"#46B43E","rgb":"70,180,62","cmyk":"71,0,93,0"},{"name":"eprimo Waldgr\u00fcn","hex":"#20711A","rgb":"32,113,26","cmyk":"72,0,77,56"},{"name":"eprimo Hellblau","hex":"#4FCEF9","rgb":"79,206,249","cmyk":"67,0,0,0"}]
      xml:
        name: getColorsResponseDto
    GetPagesResponseDto:
      type: array
      items:
        type: object
      example: {"id":120,"css":"f0258b5e8f3b7619d9d38c4612bb5eb20c2e34176d9b3af78e55e21b67888e4c","version":24,"pages":{"hashes":[{"json":"30298f1ad207a5c8b66f1ad3e52c27c71d68d2c67c47ff731493318654212f5c","html":"7e4b5a93c454c14b6b5ce41ae35c4be27c23f20d8c17182c36ae2ab71a292f0c"},{"json":"34948a3860ea630da634e9a6d11b04dfb2a791b422f2cab96624d14146b2b4f9","html":"0237d08bea9a4c09212b92c0114367cbc80385dfc9d1e4f44e484624342e7bb9"},{"json":"72fd2d72b10a2f015b948ba73ade223a6748a42ccbad99e96ff86c38dfae519e","html":"774dbd634444e3c4c5ac1457d7b5314d534a1b93faa39b4b1b3b3cd108a0b457"},{"json":"9333c000179f684915ae952f5a1c104f7852bf4a6e2d626c0fa0bbf5a535b28b","html":"e32cbdf0db04df1197d86601688e081a9c2c9cde2728bd66997af3ce078e6dc3"}],"showPageNumbers":false},"frontify":0,"role":"agency"}
      xml:
        name: getPagesResponseDto
    GetImageResponseDto:
      type: string
      example: binary
      xml:
        name: getImageResponseDto