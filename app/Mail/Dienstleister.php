<?php

namespace App\Mail;


use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Auth\Passwords\TokenRepositoryInterface;
use Illuminate\Support\Facades\DB;
use http\Env;

class Dienstleister extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $user;
    public $name;
    public $link;
    public $projektinfo;

    public function __construct($user, $name, $projektinfo)
    {
        $this->user = $user;
        $this->name = $name;
        $this->projektinfo = $projektinfo;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('mail.daten.angeliefert')
            ->subject('Es wurden Daten geliefert');
    }
}
