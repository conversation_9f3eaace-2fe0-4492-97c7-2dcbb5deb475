<?php

namespace App\Mail;


use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Auth\Passwords\TokenRepositoryInterface;
use Illuminate\Support\Facades\DB;
use http\Env;

class Angelegtundfrei extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public $user;
    public $name;
    public $link;

    public function __construct($user, $token, $name)
    {
        $this->user = $user;
        $this->name = $name;
        $this->link = config('app.APP_URL') . '/initpasswd/' . $token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('mail.angelegt.angelegt')
            ->subject('Ihre Anmeldung im Transfersafe (TS4SF) dem automatisierten Drucktool für Kampagnen');
    }
}
