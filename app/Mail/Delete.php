<?php

namespace App\Mail;


use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Auth\Passwords\TokenRepositoryInterface;
use Illuminate\Support\Facades\DB;
use http\Env;

class Delete extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $user;
    public $name;
    public $anrede;
    public $vorname;
    public $jobs;
    public $fromdate;
    public $todate;

    public function __construct($user, $jobs, $fromdate, $todate)
    {
        $this->user = $user->email;
        $this->name = $user->name;
        $this->vorname = $user->vorname;
        $this->anrede = $user->anrede;
        $this->jobs = $jobs;
        $this->fromdate = $fromdate;
        $this->todate = $todate;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('mail.delete.delete')->subject('Löschbestätigung: (' . $this->fromdate . ' - ' . $this->todate . ')');
    }
}
