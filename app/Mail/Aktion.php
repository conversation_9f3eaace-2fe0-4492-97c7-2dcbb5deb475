<?php

namespace App\Mail;


use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Auth\Passwords\TokenRepositoryInterface;
use Illuminate\Support\Facades\DB;
use http\Env;

class Aktion extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $user;
    public $name;
    public $anrede;
    public $vorname;
    public $aktion;
    public $projekt;
    public $id_job;

    public function __construct($user, $aktion)
    {
        $this->user = $user->email;
        $this->name = $user->name;
        $this->vorname = $user->vorname;
        $this->anrede = $user->anrede;
        $this->aktion = $aktion->taetigkeit;
        $this->projekt = $aktion->jobbezeichnung;
        $this->id_job = '';
        if (!empty($aktion->id_job)) {
            $this->id_job = $aktion->id_job;
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('mail.aktion.aktion')
            ->subject('Neue Aktivität im Projekt #' . $this->id_job . ' ' . $this->projekt . ' (' . $this->aktion . ')');
    }
}
