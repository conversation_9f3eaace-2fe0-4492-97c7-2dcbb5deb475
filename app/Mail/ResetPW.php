<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use http\Env;

class ResetPW extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $user;
    public $name;
    public $link;

    public function __construct($user, $token, $name)
    {
        $this->user = $user;
        $this->name = $name;
        $this->link = \config('app.APP_URL') . '/resetpasswd/' . $token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('mail.passwort.reset')
            ->subject('Zurücksetzen des TS4SF-Passworts');
    }
}
