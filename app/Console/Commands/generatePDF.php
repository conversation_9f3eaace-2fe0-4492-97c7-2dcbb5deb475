<?php

namespace App\Console\Commands;

use App\Http\Controllers\EditorController;
use Illuminate\Console\Command;
use Psy\Command\EditCommand;
use Bschmitt\Amqp\Facades\Amqp;

class generatePDF extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'PDF:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $jobId = 122;

        $jobArray = [
            'REF' => 'Testdata_for_Job_' . $jobId .'_' . date('YmdHis'),
            'Job' => $jobId ?? null,
            'Version' => $version ?? null,
            'TestDataset' => $testDataset ?? null,
            'CustomerFile' => $customerFile ?? null,
        ];

        Amqp::publish('routing-key', json_encode($jobArray) , ['queue' => 'pdf-editor-job']);

    }
}
