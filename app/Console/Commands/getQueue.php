<?php

namespace App\Console\Commands;

use App\Http\Controllers\EditorController;
use Illuminate\Console\Command;
use Psy\Command\EditCommand;
use Bschmitt\Amqp\Facades\Amqp;

class getQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'PDF:getQueue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        Amqp::consume('TestData', function ($message, $resolver) {

            var_dump($message->body);

            $resolver->acknowledge($message);

            $resolver->stopWhenProcessed();

        });

    }
}
