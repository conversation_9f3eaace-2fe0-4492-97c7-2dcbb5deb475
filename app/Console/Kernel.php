<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Jobs\ProcessPrintdata;
use App\Jobs\ProcessTransmit;
use App\Jobs\ProcessTransmitResponses;
use App\Jobs\GetResponseFromDl;
use App\Jobs\ProcessArchive;
use App\Jobs\DeliverPrintData;
use App\Jobs\ProcessDelete;
use App\Jobs\ProcessDeleteMail;
use App\Jobs\CheckProcesses;
use App\Jobs\CheckDlFtp;
use App\Jobs\ProcessPrintstatus;
use App\Jobs\GetPalDates;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')
        //          ->hourly();

        $schedule->job(new ProcessPrintdata, 'processPrintdata')->hourly();

        $schedule->job(new GetPalDates, 'GetPalDates')->hourly();

        $schedule->job(new ProcessPrintstatus, 'processPrintstatus')->hourly();

        $schedule->job(new GetResponseFromDl, 'getResponseFromDl')->everyThirtyMinutes();

        //$schedule->job(new CheckProcesses, 'checkProcesses')->everyThirtyMinutes();

        $schedule->job(new ProcessArchive, 'processArchive')->dailyAt('20:00');

        $schedule->job(new DeliverPrintData, 'deliverPrintData')->hourly();

        $schedule->job(new ProcessTransmitResponses, 'processTransmitResponses')->dailyAt('01:15');

        $schedule->job(new ProcessTransmit, 'processTransmit')->dailyAt('01:15');

        $schedule->job(new CheckDlFtp, 'checkDlFtp')->dailyAt('06:30');

        $schedule->job(new ProcessDelete, 'processDelete')->dailyAt('01:00');

        $schedule->job(new ProcessDeleteMail, 'processDeleteMail')->weeklyOn(1, '08:00');

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
