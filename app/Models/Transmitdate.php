<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transmitdate extends Model
{
    use HasFactory;

    public static function getTransmitDate($jobId, $stepId)
    {
        $transmitDateDiff = Transmitdate::Where('id_job', $jobId)->Where('step_id', $stepId)->OrderBy('id', 'DESC')->first();
        $PAL = Datei::Where('id_job', $jobId)->Where('step_id', $stepId)->OrderBy('id', 'DESC')->get('PAL_date')->first();
        if (!empty($transmitDateDiff)) {
            if (!empty($transmitDateDiff->diff) && !empty($PAL->PAL_date)) {
                $transmitDate = Carbon::parse($PAL->PAL_date)->addDays($transmitDateDiff->diff)->format('Y-m-d');
            }
            else{
                $transmitDate = $PAL->PAL_date;
            }
        } else {
            $transmitDate = Carbon::parse($PAL->PAL_date)->addDays(-2)->format('Y-m-d');
        }
        return $transmitDate;
    }

    public static function getDiff($jobId, $stepId)
    {
        $transmitDateDiff = Transmitdate::Where('id_job', $jobId)->Where('step_id', $stepId)->OrderBy('id', 'DESC')->first();
        if (!empty($transmitDateDiff)) {
            return $transmitDateDiff->diff;
        }
        return -2;
    }
}
