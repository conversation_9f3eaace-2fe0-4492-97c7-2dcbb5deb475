<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Weddingmaker extends Model
{
    protected $table = 'weddingmaker';

    public function store($id = null, $id_dokument = null,  $seite = 0, $x = 0, $y = 0, $spalten = null, $max = null, $size = null, $farbe = null, $font = null, $trenner = null)
    {
        if(!empty($id)) $this::find($id);
        $this->id_dokument = $id_dokument;
        $this->seite = (!empty($seite) ? $seite : 1);
        $this->seite = (!empty($seite) ? $seite : 1);
        $this->x = (!empty($x) ? str_replace(',', '.', $x) : 0);
        $this->y = (!empty($y) ? str_replace(',', '.', $y) : 0);
        $this->spalten = (substr($spalten, 0, 1) == ';') ? substr($spalten, 1) : $spalten;
        $this->max = (!empty($max) ? $max : 0);
        $this->size = (!empty($size) ? str_replace(',', '.', $size) : 8);
        $this->farbe = (!empty($farbe) ? $farbe : '0,0,0,80');
        $this->font = (!empty($font) ? $font : null);
        $this->trenner = (!empty($trenner) ? $trenner : null);
        $this->save();
    }
}
