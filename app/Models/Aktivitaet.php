<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Aktivitaet extends Model
{
    protected $table = 'aktivitaeten';

    public function freigabe_kunde()
    {
        return $this->belongsToMany(FreigabenK::class, 'freigaben_kunde', 'id_aktivitaet',  'id');
    }

    public function freigabe_dienstleister()
    {
        return $this->belongsToMany(FreigabenD::class, 'freigaben_dienstleister', 'id_job',  'id_benutzer');
    }

    public static function get_taetigkeiten_usergroup($id)
    {
        $role = User::current()->role();

        $array_freigaben = [];
        $freigaben = DB::select('
                SELECT
                    a.`id_taetigkeit`
                FROM
                    aktivitaeten AS a
                 LEFT JOIN
                    benutzer2jobs AS b2j ON (b2j.`id_job` = a.`id_job`)
                 LEFT JOIN
                    benutzer2' . $role . ' AS b2r ON (b2r.`id_benutzer` =  b2j.`id_benutzer` )
                 WHERE
                    a.`id_dateien` = :id
                 AND
                    b2j.`id_benutzer` = :id_benutzer
                  AND
                    a.`id_benutzer` IN (SELECT CONCAT(b2r.`id_benutzer`) FROM  benutzer2' . $role . ' AS b2r2 WHERE (b2r.`id_' . $role . '` = b2r2.`id_' . $role . '`))
                 ORDER BY
                    a.`id` DESC
                 ', ['id' =>  $id, 'id_benutzer' => Auth::id()]);


        foreach ($freigaben as $freigabe) {
            $array_freigaben[] = $freigabe->id_taetigkeit;
        }

        return $array_freigaben;
    }

    public static  function get_taetigkeiten($id)
    {
        return null;
    }
}
