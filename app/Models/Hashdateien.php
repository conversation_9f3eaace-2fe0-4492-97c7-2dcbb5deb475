<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\FileController;

class Hashdateien extends Model
{
    protected $table = 'hashdateien';

    public function store($jobId, $stepId, $fileName, $content, $mime)
    {
        $this->file_name = $fileName;
        $this->id_job = $jobId;
        $this->step_id = $stepId;
        $this->dateigroesse = strlen($content);
        Log::info("Starting to crypt " . $fileName . " ID " . $jobId . " with size " . strlen($content));
        $this->file = FileController::file_verschluesseln($content);
        Log::info("Done with crypt for ID " . $jobId . " with crypted size: " . strlen($this->file));
        $this->mime = $mime;
        $this->timestamp = now();
        $result = $this->save();
        return $result;
    }
}
