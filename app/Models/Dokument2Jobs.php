<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Dokument2Jobs extends Model
{
    protected $table = 'document2jobs';

    public static function GetCountAgenturFreigabe($id)
    {
        return Agenturfreigaben::Where('id_document2jobs', $id)
            ->Where('storno', 0)
            ->count();
    }

    public static function GetAblehnungen($id)
    {
        return AktivitaetenDoc::Where('id_document2job', $id)
            ->Where('id_docaktivitaet', 7)
            ->count();
    }

    public static function GetCountKundenFreigabe($id)
    {
        return Kundenfreigaben::Where('id_document2jobs', $id)
            ->Where('storno', 0)
            ->count();
    }

    public static function CheckFreigabeAUser($id)
    {
        return Agenturfreigaben::Where('id_document2jobs', $id)
            ->Where('id_user', Auth::id())
            ->Where('storno', 0)
            ->count();
    }

    public static function CheckFreigabeKUser($id)
    {
        return Kundenfreigaben::Where('id_document2jobs', $id)
            ->Where('id_user', Auth::id())
            ->Where('storno', 0)
            ->count();
    }

    public static function getDocument($id)
    {
        return Dokument2Jobs::leftJoin('documents', 'document2jobs.id_document', '=', 'documents.id')
            ->where('document2jobs.id', '=', $id)
            ->where('documents.aktiv', '=', 1)
            ->GroupBy('documents.id')
            ->select('document2jobs.id', 'document2jobs.anzeige_kunde', 'documents.name', 'documents.typ', 'documents.preview_img', 'documents.freigabe_noetig', 'document2jobs.wedding_ok', 'document2jobs.id_document')
            ->first();
    }

    public static function getWeddingDocument($id_job)
    {
        return Dokument2Jobs::leftJoin('documents', 'document2jobs.id_document', '=', 'documents.id')
            ->where('document2jobs.id_job', '=', $id_job)
            ->where('documents.aktiv', '=', 1)
            ->where('documents.typ', '=', 4)
            ->GroupBy('documents.id')
            ->select('documents.id')
            ->first();
    }
    public static function docdownload($id)
    {
        $filedata = Dokument2Jobs::leftJoin('documents', 'document2jobs.id_document', '=', 'documents.id')
            ->where('document2jobs.id', $id)
            ->where('documents.aktiv', 1)
            ->select('document2jobs.id', 'documents.name', 'documents.mime', 'documents.typ', 'documents.inhalt')->first();
        $file['file'] = base64_encode($filedata->inhalt);
        $file['file_name'] = $filedata->name;
        $file['mime'] = $filedata->mime;
        return $file;
    }
}
