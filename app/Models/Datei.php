<?php

namespace App\Models;

use App\Http\Controllers\FileController;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;
use setasign\Fpdi\Fpdi;
use App\Models\EditorRelease;

class Datei extends Model
{
    protected $table = 'dateien';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public function jobinfos()
    {
        return $this->belongsTo(Job::class, 'id_job', 'id')->first();
    }

    public static function countReleases($id)
    {
        $freigaben = 0;
        $aktivitaetenk = Aktivitaet::Where('id_taetigkeit', 5)
            ->Where('id_dateien', $id)
            ->get();

        if (User::current()->is_kunde()) {
            foreach ($aktivitaetenk as $aktivitaetk) {
                if (FreigabenK::Where('id_aktivitaet', $aktivitaetk['id'])) {
                    $freigaben++;
                }
            }
        } else {
            foreach ($aktivitaetenk as $aktivitaetk) {
                if (FreigabenD::Where('id_aktivitaet', $aktivitaetk['id'])) {
                    $freigaben++;
                }
            }
        }

        return $freigaben;
    }

    public static function countRejections($id)
    {
        $ablehnung = 0;
        $aktivitaetenk = Aktivitaet::Where('id_taetigkeit', 8)
            ->Where('id_dateien', $id)
            ->get();
        if (User::current()->is_kunde()) {
            foreach ($aktivitaetenk as $aktivitaetk) {
                if (FreigabenK::find($aktivitaetk['id'])) {
                    $ablehnung++;
                }
            }
        } else if (User::current()->is_dienstleister()) {
            foreach ($aktivitaetenk as $aktivitaetk) {
                if (FreigabenD::find($aktivitaetk['id'])) {
                    $ablehnung++;
                }
            }
        }

        return $ablehnung;
    }

    public function isEditorProject()
    {
        return EditorRelease::isReleased($this->id_job);
    }

    public static function showadressdaten($id, $linenr)
    {
        $filedetails = [];
        $filedetails = Datei::find($id);
        if (!empty($filedetails)) {
            $check_File = File::find($filedetails['id_job']);
        }
        if (!empty($check_File)) $filedata_tmp = json_decode(File::find($filedetails['id_job'])
            ->pluck('data')
            ->first(), ARRAY_FILTER_USE_BOTH);
        $filedata = [];

        if (!empty($filedata_tmp[0])) {
            $filedata = $filedata_tmp[0];
        }

        $dataset = null;
        if (!empty($filedata) && !empty($filedata[$linenr - 1]) && $linenr > 0) {
            $dataset[$linenr] = $filedata[$linenr - 1];
        }

        if (!empty($dataset)) {
            for ($i = 0; $i < 6; $i++) {
                if (!empty($filedata[$linenr + $i])) {
                    $dataset[$linenr + $i] = $filedata[$linenr + $i];
                }
            }
        }

        $filedetails['data'] = $dataset;

        if (!empty($filedetails->id_job)) {
            $filedetails->filetyp = 'daten';
            $pdffiles = Job::getadressfiles($filedetails->id_job);

            $pdf_wahl = [];
            if (!empty($pdffiles)) {
                foreach ($pdffiles as $pdf) {
                    if ($pdf->id_kategorie == $filedetails->id_kategorie) {
                        $pdf['abgelehnt'] = 0;
                        $datei = Datei::Where('id_file', $pdf['id_file'])->get()->first();
                        if (!empty($datei)) {
                            $abgelehnt = Aktivitaet::Where('id_dateien', $datei['id'])
                                ->Where('id_taetigkeit', '8')
                                ->get()->first();
                            if (!empty($abgelehnt)) {
                                $pdf['abgelehnt'] = 1;
                            }
                        }
                        $pdf_wahl[] = $pdf;
                    }
                }
            }

            $filedetails->adressfiles = $pdf_wahl;

            return $filedetails;
        }

        $filedetails['id'] = $id;
        $filedetails['alias_name'] = '';
        $filedetails['name'] = '';
        $filedetails['filetyp'] = 'bild';
        if (!empty($filedetails->id_file)) {
            $pdffiles = Datei::Where('id_job', $filedetails->id_file)
                ->Where('org_name', 'like', '%.pdf')
                ->orWhere('org_name', 'like', '%.jpg')
                ->orWhere('org_name', 'like', '%.jpeg')
                ->orWhere('org_name', 'like', '%.png')
                ->get();
        }

        $pdf_wahl = [];
        if (!empty($pdffiles)) {
            foreach ($pdffiles as $pdf) {
                $pdf_wahl[] = $pdf;
            }
        }
        $filedetails['adressfiles'] = $pdf_wahl;

        return $filedetails;
    }

    public static function return_file_daten($id)
    {
        $filedetails = Datei::find($id);
        $filedetails['jobnummer'] = '';
        $filedetails['name'] = '';
        if (!empty($filedetails['id_file'])) {
            $content = File::find($filedetails['id_file']);
            $filedetails['jobnummer'] = $filedetails->jobinfos()
                ->toArray()['jobnummer'];
            $filedetails['name'] = $filedetails['org_name'];
        }

        $daten_box = [];
        $daten_array = [];
        $file = '/tmp/' . md5(time() . $id);
        $handle = fopen($file, "w+");
        if (!empty($content['file'])) {
            fwrite($handle, FileController::file_entschluesseln($content['file']));
        }
        $bildformate = [
            'image/png',
            'image/jpeg',
            'application/pdf'
        ];

        if (!empty($content['mime']) && in_array($content['mime'], $bildformate)) {
            return [0 => []];
        }

        $inputFileType = null;
        if (!empty(file_get_contents($file))) {
            $inputFileType = IOFactory::identify($file);
            if ($inputFileType == 'Csv') {
                $file_contents = file_get_contents($file);
                $summe_delimiters = [0, 0, 0, 0];
                $delimiters = [0 => ";", 1 => ",", 2 => "\t", 3 => "|"];

                foreach ($delimiters as $key => $delimiter) {
                    $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
                }

                $trenner = array_keys($summe_delimiters, max($summe_delimiters));
                $delim = $delimiters[$trenner[0]];
                $file_array_tmp = explode("\n", $file_contents);

                //find Header
                $tmp_daten = [];
                foreach ($file_array_tmp as $zeile) {
                    if (!empty($zeile)) {
                        $zeile = FileController::checkEncoding($zeile, 'CSV #' . $id);
                        $tmp_daten = str_getcsv($zeile, $delim);
                        $file_array[] = $tmp_daten;
                    }
                }
            } else {
                $reader = IOFactory::createReader($inputFileType);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($file);
                $worksheet = $spreadsheet->getActiveSheet(0);
                $file_array = $worksheet->toArray();
            }


            $max_count = 0;
            $zellcount = 0;
            $array_spalten = [];
            foreach ($file_array as $zeilennr => $zeile) {
                foreach ($zeile as $key => $zelle) {
                    if (!empty($zelle)) {
                        $zellcount = $key;
                        $array_spalten[$zeilennr] = $zellcount;
                    }
                }
                $max_count = max($max_count, $zellcount);
            }

            $zeile_header = 1;
            $header = [];
            foreach ($file_array as $zeilennr => $inhalt) {
                if (empty($inhalt[0]) || count($inhalt) < 1) {
                    $zeile_header++;
                    continue;
                }
                foreach ($inhalt as $zelle) {
                    if (!empty($zelle)) {
                        $array_felder[] = $zelle;
                    }
                }
                if (count($array_felder) < 2) {
                    $zeile_header++;
                    continue;
                }
                $inhalt['qx_zeilen_nr'] = "qx_zeilen_nr";
                $header = $inhalt;
                break;
            }

            foreach ($file_array as $zeilennr => $zeile) {
                if ($zeile_header > $zeilennr) {
                    continue;
                }
                $zeile['qx_zeilen_nr'] = $zeilennr + 1;

                foreach ($zeile as $key => $daten) {
                    if (!empty($header[$key])) {
                        $daten_array[$header[$key]] = $daten;
                    }
                }
                $daten_box[] = $daten_array;
            }
        }
        $blaetter[0] = $daten_box;
        unlink($file);
        return $blaetter;
    }

    public static function dataset($id, $linenr, $suche = null, $feld = null, $page = null)
    {
        if ($feld == '*') {
            $feld = '';
        }

        $filedata_tmp = [0 => []];
        if (!empty($id)) {
            $filedata_tmp = Datei::return_file_daten($id);
        }

        if (!empty($filedata_tmp[0])) {
            $filedata = $filedata_tmp[0];
        }
        if ($linenr < 1) {
            $linenr = 1;
        }

        $maxcount = 6;
        if ($page > 1) {
            $maxcount = 6 * ($page);
        }

        $filesearch_data = [];
        $dataset = [];

        if (!empty($suche) && !empty($filedata)) {
            foreach ($filedata as $key => $data) {
                if (empty($feld)) {
                    foreach ($data as $key2 => $value) {
                        if (stripos($value, $suche) !== false && empty($feld)) {
                            $filesearch_data[$key] = $data;
                        }
                    }
                } else if (stripos($data[$feld], $suche) !== false) {
                    $filesearch_data[$key] = $data;
                }
            }
            $filedata = $filesearch_data;
        }

        if (!empty($filedata) && empty($dataset)) {
            $count = 0;
            foreach ($filedata as $key => $value) {
                if (!empty($value)) {
                    if ($count >= ($maxcount - 6)) {
                        $dataset[$key] = $value;
                    }
                    $count++;
                }
                if ($count == $maxcount) {
                    break;
                }
            }
        }

        return $dataset;
    }

    public static function showdaten($id, $linenr)
    {
        $filedetails = Datei::find($id);
        $filedata_tmp = [];
        if (!empty($filedetails)) {
            $jobinfos = Job::find($filedetails['id_job']);

            $filedetails['jobnummer'] = $jobinfos['jobnummer'];
            $filedata_tmp = Datei::return_file_daten($id);
        }

        if (!empty($filedata_tmp[0])) {
            $filedata = $filedata_tmp[0];
        }
        if ($linenr < 1) {
            $linenr = 1;
        }

        $dataset = [];
        $startline = 0;
        if (!empty($filedata)) {
            foreach ($filedata as $key => $data) {
                if ($data['qx_zeilen_nr'] == $linenr) {
                    $startline = $key;
                    break;
                }
            }
            if (!empty($filedata[$startline])) {
                $dataset[$startline] = $filedata[$startline];
            }
        }

        $filedetails['freigabe'] = 1;
        $filedetails['ablehnung'] = 0;
        if (!User::current()->is_agentur()) {
            $filedetails['freigabe'] = Datei::has_freigabe($id);
            $filedetails['ablehnung'] = Datei::has_ablehnung($id);
        }

        $filedetails['data'] = $dataset;

        if (!empty($filedetails['data'])) {
            $filedetails->filetyp = 'daten';

            $pdf_wahl = [];
            if (!empty($pdffiles)) {
                foreach ($pdffiles as $pdf) {
                    if ($pdf->id_kategorie == $filedetails->id_kategorie) {
                        $pdf_wahl[] = $pdf;
                    }
                }
            }

            $filedetails->pdffiles = $pdf_wahl;
            $header = [];
            if (!empty($dataset)) {
                foreach ($dataset as $key => $inhalt) {
                    foreach ($inhalt as $key => $value) {
                        if (!in_array($key, $header)) {
                            $header[] = $key;
                        }
                    }
                }
            }
            $filedetails['header'] = $header;
        } else {
            $filedetails['id'] = $id;
            $filedetails['alias_name'] = '';
            $filedetails['name'] = '';
            $filedetails['filetyp'] = 'bild';

            $pdffiles = [];
            $pdf_wahl = [];
            foreach ($pdffiles as $pdf) {
                $pdf_wahl[] = $pdf;
            }
            $filedetails['pdffiles'] = $pdf_wahl;
        }
        $filedetails['array_taetigkeiten'] = Aktivitaet::get_taetigkeiten($id);
        $filedetails['array_taetigkeiten_usergroup'] = Aktivitaet::get_taetigkeiten_usergroup($id);
        return $filedetails;
    }

    public static function searchfiledata($id, $search = null, $feld = null, $page = 1)
    {
        $search = urldecode($search);
        $role = User::current()->role();

        $filedetails = Datei::find($id);
        $mime_tmp['mime'] = [];
        if (!empty($filedetails)) {
            $mime_tmp = File::find($filedetails['id_file']);
        }
        $bildformate = [
            'image/png',
            'image/jpeg',
            'application/pdf'
        ];
        $filedetails['jobnummer'] = "";
        $filedetails['name'] = "";
        $tmp_array = [0 => []];
        if (!empty($mime_tmp['mime']) && !in_array($mime_tmp['mime'], $bildformate)) {
            $tmp_array = Datei::return_file_daten($id);
            $filedetails['jobnummer'] = $filedetails->jobinfos()->toArray()['jobnummer'];
            $filedetails['name'] = $filedetails['org_name'];
        }

        $header = [];
        $array_details = [
            'alias_name' => '',
            'name' => '',
            'id' => '',
            'id_job' => '',
            'datacount' => 0,
            'searchcount' => '',
            'jobnummer' => '',
            'filetyp' => '',
            'array_taetigkeiten' => Aktivitaet::get_taetigkeiten($id),
            'array_taetigkeiten_usergroup' => Aktivitaet::get_taetigkeiten_usergroup($id),
            'freigabe' => 1,
            'ablehnung' => 0,
            'data' => [],
        ];
        if ($role != 'ersteller') {
            $array_details['freigabe'] = Datei::has_freigabe($id);
            $array_details['ablehnung'] = Datei::has_ablehnung($id);
        }
        $array_result_tmp = [];
        $array_result_final = [];

        if (!empty($filedetails['org_name'])) {
            $filename = $filedetails['org_name'];
        }
        if (!empty($filename)) {
            $array_details['alias_name'] = $filename;
            $array_details['name'] = $filename;
        }
        if (!empty($id)) {
            $array_details['id'] = $id;
        }
        if (!empty($filedetails['id_job'])) {
            $array_details['id_job'] = $filedetails['id_job'];
        }
        if (!empty($filedetails['jobnummer'])) {
            $array_details['jobnummer'] = $filedetails['jobnummer'];
        }
        if (!empty($tmp_array[0])) {
            $array_details['datacount'] = count($tmp_array[0]);
        }

        $searchcount = 0;
        $all = 0;
        $found = false;
        while ($all < (count($tmp_array[0]))) {
            foreach ($tmp_array[0][$all] as $key => $inhalt) {
                if (((empty($feld) || $feld == '*') && !empty($inhalt)) || $feld != '*' && $key == $feld) {
                    if (stripos($inhalt, $search) !== false) {
                        $array_result_tmp[$all + 1] = $tmp_array[0][$all];
                        $found = true;
                    }
                }
                if (!in_array($key, $header)) {
                    $header[] = $key;
                }
            }
            if ($found) {
                $searchcount++;
            }
            $found = false;
            $all++;
        }
        $seiten = 1;
        $scount = 1;
        foreach ($array_result_tmp as $key => $datentmp) {
            $array_result_final[$seiten][$key] = $datentmp;
            if ($scount % 6 == 0) {
                $seiten++;
            }
            $scount++;
        }
        $array_details['searchcount'] = min($array_details['datacount'], $searchcount);
        $array_details['header'] = $header;
        if (!empty($array_result_final[$page]) && $searchcount != 0) {
            $array_details['data'] = $array_result_final[$page];
        }
        return $array_details;
    }

    public static function loadfiledatadaten($id, $linenr, $page = 1)
    {
        $array_details = Datei::searchfiledata($id, null, null, $page);
        $allData = Datei::return_file_daten($id)[0];
        $array_details['data'] = [];
        $array_details['data'][$linenr] = $allData[$linenr - 1];
        return $array_details;
    }

    public static function show($id, $search = null, $feld = '*', $page = 1)
    {
        $searchcount = 0;
        if ($search == '.') {
            $search = '';
        }

        session(['aktiv' => $page]);
        $header = [];
        if (empty($search)) {
            $filedetails = Datei::find($id);
            $filedetails['jobnummer'] = '';
            $filedetails['name'] = '';
            $mime_tmp['mime'] = [];
            if (!empty($filedetails['id_file'])) {
                $filedetails['jobnummer'] = $filedetails->jobinfos()->toArray()['jobnummer'];
                $filedetails['name'] = $filedetails['org_name'];
                $mime_tmp = File::find($filedetails['id_file']);
            }

            $bildformate = [
                'image/png',
                'image/jpeg',
                'application/pdf'
            ];
            $filedetails['jobnummer'] = "";
            $filedetails['name'] = "";
            $filedetails['searchcount'] = 0;
            $filedetails['datacount'] = 0;
            $tmp_array = [0 => []];
            if (!empty($mime_tmp['mime']) && !in_array($mime_tmp['mime'], $bildformate)) {
                $tmp_array = Datei::return_file_daten($id);
                $filedetails['jobnummer'] = $filedetails->jobinfos()->toArray()['jobnummer'];
                $filedetails['name'] = $filedetails['org_name'];
            }

            $filedetails['freigabe'] = 1;
            $filedetails['ablehnung'] = 0;
            if (!User::current()->is_agentur()) {
                $filedetails['freigabe'] = Datei::countReleases($id);
                $filedetails['ablehnung'] = Datei::countRejections($id);
            }

            if (!empty($tmp_array[0])) {
                $filedetails['datacount'] = count($tmp_array[0]);
            }

            if ($searchcount == 0 && !empty($tmp_array[0])) {
                $filedetails['searchcount'] = count($tmp_array[0]);
            }
            $result_array = [];
            $start = 0;
            // TODO: kill magic
            if ($page > 1) {
                $start = 6 * ($page - 1);
            }
            for ($i = $start; $i < (6 + $start); $i++) {
                if (!empty($tmp_array[0][$i])) {
                    $result_array[$i + 1] = ($tmp_array[0][$i]);
                }
            }
            $filedetails['data'] = $result_array;
            if (!empty($result_array)) {
                foreach ($result_array as $key => $inhalt) {
                    foreach ($inhalt as $key => $value) {
                        if (!in_array($key, $header)) {
                            $header[] = $key;
                        }
                    }
                }
            }
            $filedetails['header'] = $header;
        } else if (substr($search, 0, 1) !== '#') {
            $filedetails = Datei::searchfiledata($id, $search, $feld, $page);
        } else if (substr($search, 1) !== 0) {
            $filedetails = Datei::loadfiledatadaten($id, substr($search, 1));
        }

        $filedetails['array_taetigkeiten'] = Aktivitaet::get_taetigkeiten($id);
        $filedetails['array_taetigkeiten_usergroup'] = Aktivitaet::get_taetigkeiten_usergroup($id);
        return $filedetails;
    }

    public static function delete_files($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = Datei::whereNull('geloescht_am')->Where('created_at', '<', $del_date->format(self::dateTime))->get();
        foreach ($datei as $dataset) {
            $file = File::find($dataset->id_file);
            $string_length = strlen($file->file);
            Log::debug('delete_files: Going to delete file id ' . $dataset->id_file . ', strlen is ' . $string_length);
            $file->file = null;
            $result = $file->save();
            if ($result) {
                $datei_set = Datei::find($dataset->id);
                $datei_set->geloescht_am = now();
                $datei_set->save();
            }
        }
    }

    public static function getpdffiledata($id, $page = 1)
    {
        $datei = Datei::find($id);
        $filedata = File::find($datei->id_file);
        $tmp_pdfcontent = 'data://' . $filedata->mime . ';base64,' . base64_encode(FileController::file_entschluesseln($filedata->file));
        $pdf = new FPDI();
        $pageCount = $pdf->setSourceFile($tmp_pdfcontent);
        $tplidx = $pdf->importPage($page);
        $specs = $pdf->getTemplateSize($tplidx);
        $seitenanzahl = 6;
        if ($pageCount < $seitenanzahl) {
            $seitenanzahl = $pageCount;
        }
        $max_pages = 6;
        if ($seitenanzahl < $max_pages) {
            $max_pages = $seitenanzahl;
        }
        if ($page <= 6) {
            for ($i = 1; $i <= $max_pages; $i++) {
                $pdf->AddPage($specs['height'] > $specs['width'] ? 'P' : 'L');
                $pdf->useTemplate($pdf->importPage($i), null, null, $specs['width'], $specs['height'], true);
            }
        } else {
            $pdf->AddPage();
            $pdf->useTemplate($pdf->importPage($page), null, null, $specs['width'], $specs['height'], true);
        }
        $pdfcontent = $pdf->Output('S');
        $rueckgabe['file'] = base64_encode($pdfcontent);
        $rueckgabe['mime'] = $filedata->mime;
        return "[" . json_encode($rueckgabe) . "]";
    }

}
