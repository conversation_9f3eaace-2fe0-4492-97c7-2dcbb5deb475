<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Job extends Model
{
    public function letzte_aktivitaet_agentur()
    {
        return $this->hasManyThrough(FreigabenA::class, Aktivitaet::class, 'id_job', 'id_aktivitaet')
            ->max('id_aktivitaet');
    }

    public function letzte_aktivitaet_kunde()
    {
        return $this->hasManyThrough(FreigabenK::class, Aktivitaet::class, 'id_job', 'id_aktivitaet')
            ->max('id_aktivitaet');
    }

    public function letzte_aktivitaet_dienstleister()
    {
        return $this->hasManyThrough(FreigabenD::class, Aktivitaet::class, 'id_job', 'id_aktivitaet')
            ->max('id_aktivitaet');
    }

    public function aktivitaeten_agentur()
    {
        return $this->belongsToMany(Taetigkeit::class, Aktivitaet::class, 'id_job', 'id_taetigkeit')->withPivot('datetime', 'id_benutzer', 'id_dateien');
    }

    public function aktivitaeten_kunde()
    {
        return $this->belongsToMany(FreigabenK::class, Aktivitaet::class, 'id_job', 'id_taetigkeit')->withPivot('datetime', 'id_benutzer', 'id_dateien');
    }

    public function aktivitaeten_dienstleister()
    {
        return $this->belongsToMany(FreigabenD::class, Aktivitaet::class, 'id_job', 'id_taetigkeit')->withPivot('datetime', 'id_benutzer', 'id_dateien');
    }


    public function user()
    {
        return $this->belongsToMany(User::class, Jobbenutzer::class, 'id_job', 'id_benutzer')
            ->where('aktiv', 1)
            ->get();
    }

    public function users()
    {
        return $this->belongsToMany(User::class, Jobbenutzer::class, 'id_job', 'id_benutzer')
            ->where('gesperrt', '!=', 1)
            ->get();
    }

    public function user_inaktiv()
    {
        return $this->belongsToMany(User::class, Jobbenutzer::class, 'id_job', 'id_benutzer')
            ->where('aktiv', 0)
            ->get();
    }

    public static function getDocuments($id, $id_tag = null, $reg = null)
    {
        $query =  Dokument2Jobs::leftJoin('documents', 'document2jobs.id_document', '=', 'documents.id')
            ->leftJoin('tagzuordnung', 'document2jobs.id', '=', 'tagzuordnung.id_b2j')
            ->leftJoin('typen', 'documents.typ', '=', 'typen.id')
            ->where('documents.aktiv', '=', 1)
            ->where('document2jobs.id_job', '=', $id)
            ->orderBy('typen.sort', 'ASC')
            ->GroupBy('documents.id')
            ->select('document2jobs.id', 'document2jobs.anzeige_kunde', 'document2jobs.wedding_ok', 'documents.name', 'documents.typ', 'documents.preview_img', 'documents.freigabe_noetig', 'document2jobs.wedding_ok');


        if (!empty($id_tag)) {
            $documents = $query->where('tagzuordnung.id_tag', '=', $id_tag)->get();
        } else if (!empty($reg) && $reg == 'kunde') {
            $documents = $query->where('tagzuordnung.id_tag', '=', $id_tag)
                ->where('document2jobs.anzeige_kunde', '=', 1)->get();
        } else if (!empty($reg) && $reg == 'agentur') {
            $documents = $query->where('tagzuordnung.id_tag', '=', $id_tag)
                ->where('document2jobs.anzeige_kunde', '!=', 1)->get();
        } else if (!empty($reg) && $reg == 'salesforce') {
            $documents = [];
        } else if (User::current()->is_kunde()) {
            $freigegeben = DokFreigabeA::select('id_document2jobs', DB::raw('count(*) as total'))
                ->groupBy('id_document2jobs')
                ->having('total', '>=', 1)
                ->get('id_document2jobs');

            $array_freigaben = [];
            foreach ($freigegeben as $freigaben) {
                $array_freigaben[] = $freigaben->id_document2jobs;
            }

            $documents = $query->whereIn('document2jobs.id', $array_freigaben)->get();
        } else {
            $documents = $query->get();
        }

        return $documents;
    }

    public static function getHochDocs($id)
    {
        return Dokument2Jobs::leftJoin('documents', 'document2jobs.id_document', '=', 'documents.id')
            ->where('document2jobs.id_job', $id)
            ->where('documents.aktiv', 1)
            ->where('documents.typ', 4)
            ->select('document2jobs.id', 'documents.name', 'documents.typ', 'documents.inhalt')
            ->get();
    }

    public static function return_aktivitaeten($id, $rolle)
    {
        $aktivitateten = Aktivitaet::Where('id_job', $id)->orderBy('id', 'DESC')->get();
        $actions = [];
        foreach ($aktivitateten as $aktivitaet) {
            if ($rolle == 'ersteller') {
                $freigabe = Aktivitaet::Where('id', $aktivitaet['id'])
                    ->orderBy('id', 'DESC')
                    ->get();
            } else if ($rolle == 'kunde') {
                $freigabe = FreigabenK::Where('id_aktivitaet', $aktivitaet['id'])->orderBy('id', 'DESC')->get();
            } else if ($rolle == 'dienstleister') {
                $freigabe = FreigabenD::Where('id_aktivitaet', $aktivitaet['id'])->orderBy('id', 'DESC')->get();
            }

            $taetigkeit = Taetigkeit::find($aktivitaet['id_taetigkeit']);
            if (!empty($freigabe->toArray())) {
                $aktivitaet['taetigkeit'] = $taetigkeit['taetigkeit'];
                $aktivitaet['taetigkeit_kunde'] = $taetigkeit['taetigkeit_kunde'];
                $aktivitaet['taetigkeit_dienstleister'] = $taetigkeit['taetigkeit_dienstleister'];
                $aktivitaet['farbe'] = $taetigkeit['farbe'];
                $aktivitaet['farbe_kunde'] = $taetigkeit['farbe_kunde'];
                $aktivitaet['farbe_dienstleister'] = $taetigkeit['farbe_dienstleister'];
                $actions[$aktivitaet->id_dateien][] = $aktivitaet;
            }
        }

        return $actions;
    }

    public static function getadressfiles($id)
    {
        $role = User::current()->role();

        $data = [];

        $data_tmp = Datei::where('id_job', $id)
            ->where(function ($query) {
                $query->where('org_name', 'like', '%.xls%')
                    ->orWhere('org_name', 'like', '%.csv%');
            })->get();


        $aktivitaeten = Job::return_aktivitaeten($id, $role);

        foreach ($data_tmp as $file) {
            if (array_key_exists($file->id, $aktivitaeten)) {
                $data[] = $file;
            }
        }
        return $data;
    }

    public static function get_del_dateien_from_job($id_job, $from, $to)
    {
        return Datei::Where('id_job', $id_job)
            ->whereBetween('geloescht_am', [$from, $to])
            ->get(['org_name', 'step_id', 'geloescht_am'])
            ->toArray();
    }

    public static function get_del_druckdateien_from_job($id_job, $from, $to)
    {
        return Druckdateien::Where('id_job', $id_job)
            ->whereBetween('geloescht_am', [$from, $to])
            ->get(['file_name as org_name', 'step_id', 'geloescht_am'])
            ->toArray();
    }

    public static function get_del_response_from_job($id_job, $from, $to)
    {
        return Responses::Where('id_job', $id_job)
            ->whereBetween('geloescht_am', [$from, $to])
            ->get(['file_name as org_name', 'step_id', 'geloescht_am'])
            ->toArray();
    }


    public static function get_del_archive_from_job($id_job, $from, $to)
    {
        return Einzelhashdateien::Where('id_job', $id_job)
            ->whereBetween('geloescht_am', [$from, $to])
            ->get(['file_name as org_name', 'step_id', 'geloescht_am'])
            ->groupBY('step_id')
            ->toArray();
    }


    public static function get_del_response_ok_from_job($id_job, $from, $to)
    {
        return Einzelresponses::Where('id_job', $id_job)
            ->whereBetween('geloescht_am', [$from, $to])
            ->get(['file_name as org_name', 'step_id', 'geloescht_am'])
            ->groupBY('step_id')
            ->toArray();
    }

    public static function get_del_sonstigeresponse_from_job($from, $to)
    {
        return Responses::whereBetween('geloescht_am', [$from, $to])
            ->get(['file_name as org_name', 'step_id', 'geloescht_am'])
            ->toArray();
    }

}
