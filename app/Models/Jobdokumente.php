<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Jobdokumente extends Model
{
    protected $table = 'document2jobs';

    public static function reset_document($id_doc)
    {
        $wedding_doc = Jobdokumente::find($id_doc);
        $wedding_doc->wedding_ok = 0;
        $wedding_doc->save();
    }

    public static function getAdressfile($jobId)
    {
        return Jobdokumente::leftJoin('documents', 'documents.id', '=', 'document2jobs.id_document')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 3)
            ->select('document2jobs.id')
            ->first();
    }
}
