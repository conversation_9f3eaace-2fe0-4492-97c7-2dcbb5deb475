<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Jobbenutzer extends Model
{
    protected $table = 'benutzer2jobs';

    public static function checkUser($idJob)
    {
        $job = Jobbenutzer::Where('id_job', $idJob)->Where('id_benutzer', Auth::id())->first();
        if(!empty($job) && $job->ansehen == 1){
            return true;
        }
        else{
            return false;
        }
    }
}
