<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Psy\Command\EditCommand;

class EditorRelease extends Model
{
    protected $table = 'editorreleases';

    static function isReleased($jobId)
    {
        $isReleased = false;
        $count = EditorRelease::getReleaseCount($jobId);
        if($count > 1){
            $isReleased = true;
        }
        return $isReleased;
    }

    static function getReleaseCount($jobId)
    {
        $countReleases = EditorRelease::Where('jobId', $jobId)->Where('release', 1)->count();
        $countStorno = EditorRelease::Where('jobId', $jobId)->Where('release', 0)->count();
        return (int) $countReleases - (int) $countStorno;
    }

    static function getVersionHistory($jobId)
    {
        $versions = null;
        $versions = Editordocument::Where('jobId', $jobId)->select(['version', 'created_at', 'user'])->orderBy('created_at', 'DESC')->get();

        $returnVersions = [];

        foreach ($versions as $version) {
            $userName = '-';
            if (!empty($version->user)) {
                $user = User::find($version->user);
                $userName = $user->name;
            }
            $returnVersions[] = [
                'version' => $version->version,
                'created_at' => $version->created_at,
                'user' => $userName,
            ];
        }

        return $returnVersions;
    }

}
