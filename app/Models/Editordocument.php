<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Psy\Command\EditCommand;

class Editordocument extends Model
{
    protected $table = 'editordocuments';

    static function getLastVersion($jobId)
    {
        $lastVersion = null;
        $lastVersion = Editordocument::Where('jobId', $jobId)->orderBy('version', 'DESC')->first();
        return $lastVersion;
    }

    static function getLastFrontifyVersion($jobId)
    {
        $lastVersion = null;
        $lastVersion = Editordocument::Where('jobId', $jobId)->Where('frontify', 1)->orderBy('version', 'DESC')->first();
        return $lastVersion;
    }

    static function getByVersion($jobId, $version)
    {
        $byVersion = null;
        if ($version === 'latest') {
            $byVersion = self::getLastVersion($jobId);
        } else {
            $byVersion = Editordocument::Where('jobId', $jobId)->Where('version', $version)->first();
        }
        return $byVersion;
    }

    static function getVersionHistory($jobId)
    {
        $versions = null;
        $versions = Editordocument::Where('jobId', $jobId)->select(['version', 'created_at', 'user'])->orderBy('created_at', 'DESC')->get();

        $returnVersions = [];

        foreach($versions AS $version) {
            $userName = '-';
            if(!empty($version->user)){
                $user = User::find($version->user);
                $userName = $user->name;
            }
            $returnVersions[] = [
                'version' => $version->version,
                'created_at' => $version->created_at,
                'user' => $userName,
                ];
        }

        return $returnVersions;
    }

}
