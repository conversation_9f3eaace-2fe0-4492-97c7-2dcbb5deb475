<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\FileController;

class Einzelhashdateien extends Model
{
    protected $table = 'hashdateien_einzeldatei';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public function store($jobId, $stepId, $fileName, $vertragskonto, $content, $mime)
    {
        $this->file_name = $fileName;
        $this->id_job = $jobId;
        $this->step_id = $stepId;
        $this->dateigroesse = strlen($content);
        $this->vertragskonto = $vertragskonto;
        Log::info("Starting to crypt " . $fileName . " ID " . $jobId . " with size " . strlen($content));
        $this->file = FileController::file_verschluesseln($content);
        Log::info("Done with crypt for ID " . $jobId . " with crypted size: " . strlen($this->file));
        $this->mime = $mime;
        $this->timestamp = now();

        return $this->save();
    }

    public static function delete_archivdateien($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = Einzelhashdateien::leftJoin(
            'transmits',
            function ($join) {
                $join->on('transmits.id_datei', '=', 'hashdateien_einzeldatei.id')
                    ->where('transmits.id_liefertyp', '=', 1);
            }
        )
            ->whereNull('hashdateien_einzeldatei.geloescht_am')
            ->Where('hashdateien_einzeldatei.created_at', '<', $del_date->format(self::dateTime))
            ->whereNotNull('transmits.antwort_am')
            ->where('transmits.id_status', 1)
            ->get(['hashdateien_einzeldatei.id']);

        FileController::delete_datasets(Einzelhashdateien::class, $datei);
    }
}
