<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class User extends Authenticatable
{
    use Notifiable;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'passwordreset_token'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public static function current(): User
    {
        return User::find(Auth::id());
    }


    public function getRole()
    {
        if ($this->is_agentur()) {
            $role = "agency";
        } else if ($this->is_kunde()) {
            $role = "customer";
        } else if ($this->is_dienstleister()) {
            $role = "provider";
        }

        return $role;
    }

    public function role()
    {
        if ($this->is_agentur()) {
            $role = "ersteller";
        } else if ($this->is_kunde()) {
            $role = "kunde";
        } else if ($this->is_dienstleister()) {
            $role = "dienstleister";
        }

        return $role;
    }

    public function is_agentur()
    {
        return $this->belongsToMany(Ersteller::class, 'benutzer2ersteller', 'id_benutzer', 'id_ersteller')->count();
    }

    public function is_kunde()
    {
        return $this->belongsToMany(Kunde::class, 'benutzer2kunde', 'id_benutzer', 'id_kunde')->count();
    }
    public function is_dienstleister()
    {
        return $this->belongsToMany(Dienstleister::class, 'benutzer2dienstleister', 'id_benutzer', 'id_dienstleister')->count();
    }

    public function jobs()
    {
        return $this->belongsToMany(Job::class, 'benutzer2jobs', 'id_benutzer', 'id_job')
            ->where('gesperrt', '=', 0)->where('dateien_geloescht', '!=', 1)->where(function ($q) {
                $q->where('jobende', '>=',  Carbon::today())->orwhereNull('jobende');
            });
    }

    public function getRightFromJob(int $idJob, string $right)
    {
        return $this->belongsToMany(Job::class, 'benutzer2jobs', 'id_benutzer', 'id_job')
            ->select($right)
            ->where('id_job',  $idJob)
            ->where('gesperrt', '=', 0)
            ->where('dateien_geloescht', '!=', 1)
            ->where(function ($q) {
                $q->where('jobende', '>=',  Carbon::today())->orwhereNull('jobende');
            })->first();
    }

    public function freigegebeneJobs(array $colums)
    {
        return $this->jobs()->where('freigegeben', '1')->get($colums);
    }

    public function getJob($id)
    {
        return $this->jobs()->Where('id_job', $id);
    }

    public function jobs_done()
    {
        return $this->belongsToMany(Job::class, 'benutzer2jobs', 'id_benutzer', 'id_job')
            ->where('gesperrt', '=', 0)->where('dateien_geloescht', '!=', 1)->where('jobende', '<',  Carbon::today());
    }
}
