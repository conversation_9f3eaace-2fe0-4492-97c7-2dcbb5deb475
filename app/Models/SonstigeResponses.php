<?php

namespace App\Models;

use App\Http\Controllers\FileController;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class SonstigeResponses extends Model
{
    protected $table = 'sonstige_responsedateien';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public static function delete_sonstresponse($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = SonstigeResponses::whereNull('geloescht_am')
            ->Where('created_at', '<', $del_date->format(self::dateTime))
            ->get();

        FileController::delete_datasets(SonstigeResponses::class, $datei);
    }

    public static function sonstigerdownload($id)
    {
        $filedata = SonstigeResponses::find($id);
        $file['file'] = base64_encode(FileController::file_entschluesseln($filedata->file));
        $file['file_name'] = $filedata->file_name;
        $file['mime'] = $filedata->mime;
        $srd = new SonstigerDownload();
        $srd->id_sr = $id;
        $srd->id_user = Auth::id();
        $srd->datetime = now();
        $srd->save();
        return $file;
    }
}
