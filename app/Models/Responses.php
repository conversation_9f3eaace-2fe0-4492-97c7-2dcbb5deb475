<?php

namespace App\Models;

use App\Http\Controllers\FileController;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\ResponseDownload;

class Responses extends Model
{
    protected $table = 'responsedateien';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public static function delete_response($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = Responses::whereNull('geloescht_am')
            ->Where('created_at', '<', $del_date->format(self::dateTime))
            ->get();
        Log::info('Going to delete responses. Count: ' . $datei->count());

        FileController::delete_datasets(Responses::class, $datei);
    }

    public static function responsedownload($id)
    {
        $filedata = Responses::find($id);
        $file['file'] = base64_encode(FileController::file_entschluesseln($filedata->file));
        $file['file_name'] = $filedata->file_name;
        $file['mime'] = $filedata->mime;
        $srd = new ResponseDownload();
        $srd->id_response = $id;
        $srd->id_user = Auth::id();
        $srd->datetime = now();
        $srd->save();
        return $file;
    }
}
