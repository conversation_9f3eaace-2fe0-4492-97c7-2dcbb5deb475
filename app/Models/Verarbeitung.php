<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Verarbeitung extends Model
{
    protected $table = 'verarbeitung';

    public static function getPAL($id_job, $lieferdatum, $anzahl)
    {
        if (!empty($anzahl)) {
            $verarbeitung = Verarbeitung::Where('id_job', $id_job)
                ->Where('auflage', '>=', $anzahl)
                ->OrderBy('auflage', 'ASC')
                ->first();
        }

        $pal = "";
        if (!empty($verarbeitung)) {
            if (intval(Carbon::parse($lieferdatum)->format('Hi')) >= 1200) {
                $pal = Carbon::parse($lieferdatum)->addDays($verarbeitung->zeit + 1)->format('d.m.Y');
            } else {
                $pal = Carbon::parse($lieferdatum)->addDays($verarbeitung->zeit)->format('d.m.Y');
            }
        }
        return $pal;
    }

    public static function getKosten($id_job, $lieferdatum, $anzahl)
    {
        if (!empty($anzahl)) {
            $verarbeitung = Verarbeitung::Where('id_job', $id_job)
                ->Where('auflage', '>=', $anzahl)
                ->OrderBy('auflage', 'ASC')
                ->first();
        }
        $kosten = '';
        if (!empty($verarbeitung)) {
            $kosten = number_format($verarbeitung->kosten * $anzahl / 100, 2, ',', '') . " €";
        }
        return $kosten;
    }
}
