<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\FileController;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Druckdateien extends Model
{
    protected $table = 'druckdateien';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public function store($jobId, $stepId, $fileName, $dienstleisterId, $content, $mime)
    {
        $this->file_name = $fileName;
        $this->id_job = $jobId;
        $this->step_id = $stepId;
        $this->id_dienstleister = (!empty(config('connections.STANDARD_DL')) ? config('connections.STANDARD_DL') : $dienstleisterId);
        $this->dateigroesse = strlen($content);
        Log::info("Starting to crypt " . $fileName . " ID " . $jobId . " with size " . strlen($content));
        $this->file = FileController::file_verschluesseln($content);
        unset($content);
        Log::info("Done with crypt for ID " . $jobId . " with crypted size: " . strlen($this->file));
        $this->mime = $mime;
        $this->timestamp = now();

        return $this->save();
    }

    public static function delete_druckdaten($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = Druckdateien::whereNull('geloescht_am')
            ->Where('created_at', '<', $del_date->format(self::dateTime))
            ->get();
        FileController::delete_datasets(Druckdateien::class, $datei);
    }

    public static function return_file_daten($id)
    {
        $filedetails = Druckdateien::find($id);
        $filedetails['jobnummer'] = '';
        $filedetails['name'] = '';
        if (!empty($filedetails['file'])) {
            $content = $filedetails['file'];
        }
        $daten_box = [];
        $daten_array = [];
        $file = '/tmp/' . md5(time() . $id);
        $handle = fopen($file, "w+");
        if (!empty($content)) {
            fwrite($handle, FileController::file_entschluesseln($content));
        }

        $bildformate = [
            'image/png',
            'image/jpeg',
            'application/pdf'
        ];
        if (!empty($content['mime']) && in_array($content['mime'], $bildformate)) {
            return [0 => []];
        }
        $inputFileType = IOFactory::identify($file);
        if ($inputFileType == 'Csv') {
            $file_contents = file_get_contents($file);
            $summe_delimiters = [0, 0, 0, 0];
            $delimiters = [0 => ";", 1 => ",", 2 => "\t", 3 => "|"];

            foreach ($delimiters as $key => $delimiter) {
                $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
            }

            $trenner = array_keys($summe_delimiters, max($summe_delimiters));
            $delim = $delimiters[$trenner[0]];
            $file_array_tmp = explode("\n", $file_contents);

            //find Header
            $tmp_daten = [];
            foreach ($file_array_tmp as $zeile) {
                if (!empty($zeile)) {
                    $zeile = FileController::checkEncoding($zeile, 'CSV (view) #'. $id);

                    $tmp_daten = str_getcsv($zeile, $delim);
                    $file_array[] = $tmp_daten;
                }
            }
            return $file_array;
        } else {
            $reader = IOFactory::createReader($inputFileType);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($file);
            $worksheet = $spreadsheet->getActiveSheet(0);
            $file_array = $worksheet->toArray();
        }

        $max_count = 0;
        $zellcount = 0;
        $array_spalten = [];
        foreach ($file_array as $zeilennr => $zeile) {
            foreach ($zeile as $key => $zelle) {
                if (!empty($zelle)) {
                    $zellcount = $key;
                    $array_spalten[$zeilennr] = $zellcount;
                }
            }
            $max_count = max($max_count, $zellcount);
        }

        $zeile_header = 1;
        $header = [];
        foreach ($file_array as $zeilennr => $inhalt) {
            if (empty($inhalt[0]) || count($inhalt) < 1) {
                $zeile_header++;
                continue;
            }
            foreach ($inhalt as $zelle) {
                if (!empty($zelle)) {
                    $array_felder[] = $zelle;
                }
            }
            if (count($array_felder) < 2) {
                $zeile_header++;
                continue;
            }
            $inhalt['qx_zeilen_nr'] = "qx_zeilen_nr";
            $header = $inhalt;
            break;
        }
    }
}
