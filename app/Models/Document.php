<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Document extends Model
{
    protected $table = 'documents';

    public function zuordnung()
    {
        return $this->belongsToMany(Tags::class, 'tag2dokument', 'id_dokument',  'id_tag')
            ->where('aktiv', '=', 1)
            ->get(['tag', 'id_tag']);
    }

    public function get_jobs($cols = [])
    {
        return $this->belongsToMany(Job::class, 'document2jobs', 'id_document',  'id_job')->select($cols)
            ->get();
    }

    public static function getSchwarzfilm($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->leftJoin('wedding_merge', 'wedding_merge.id_weddingdoc', '=', 'document2jobs.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 7)
            ->select('documents.id', 'documents.inhalt', 'documents.druckinhalt', 'document2jobs.id_job')
            ->first();
    }

    public static function getDruckdateien($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 4)
            ->get(['documents.id', 'documents.typ', 'documents.inhalt', 'documents.name', 'documents.mime', 'document2jobs.id AS d2j_id']);
    }

    public static function getCheckdata($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 10)
            ->get(['documents.id', 'documents.typ', 'documents.inhalt', 'documents.name', 'documents.mime', 'document2jobs.id AS d2j_id']);
    }

    public static function getAdressdata($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 3)
            ->get(['documents.id', 'documents.typ', 'documents.inhalt', 'documents.name', 'documents.mime', 'document2jobs.id AS d2j_id']);
    }

    public static function getHash($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->leftJoin('wedding_merge', 'wedding_merge.id_weddingdoc', '=', 'document2jobs.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 8)
            ->select('documents.id', 'documents.inhalt', 'documents.druckinhalt',  'document2jobs.id AS d2j_id');
    }

    public static function getWeddingdocById($weddingdocId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->leftJoin('wedding_merge', 'wedding_merge.id_weddingdoc', '=', 'document2jobs.id')
            ->Where('document2jobs.id', '=', $weddingdocId)
            ->Where('documents.typ', '=', 4)
            ->select('documents.id', 'documents.inhalt', 'documents.druckinhalt', 'document2jobs.id_job')
            ->first();
    }

    public static function getWeddingdocByJobId($jobId)
    {
        return Document::leftJoin('document2jobs', 'document2jobs.id_document', '=', 'documents.id')
            ->leftJoin('wedding_merge', 'wedding_merge.id_weddingdoc', '=', 'document2jobs.id')
            ->Where('document2jobs.id_job', '=', $jobId)
            ->Where('documents.typ', '=', 4)
            ->select('documents.id', 'documents.inhalt', 'documents.druckinhalt', 'document2jobs.id_job')
            ->first();
    }
}
