<?php

namespace App\Models;

use App\Http\Controllers\FileController;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Einzelresponses extends Model
{
    protected $table = 'responsedateien_einzeldateien';
    const date = "Y-m-d";
    const dateTime = "Y-m-d H:i:s";

    public static function delete_responsedateien($tage = 14)
    {
        $del_date = Carbon::now()->subDays($tage);
        $datei = Einzelresponses::leftJoin(
            'transmits',
            function ($join) {
                $join->on('transmits.id_datei', '=', 'responsedateien_einzeldateien.id')
                    ->where('transmits.id_liefertyp', '=', 2);
            }
        )
            ->whereNull('responsedateien_einzeldateien.geloescht_am')
            ->Where('responsedateien_einzeldateien.created_at', '<', $del_date->format(self::dateTime))
            ->whereNotNull('transmits.antwort_am')
            ->where('transmits.id_status', 1)
            ->get(['responsedateien_einzeldateien.id']);


        FileController::delete_datasets(Einzelresponses::class, $datei);
    }
}
