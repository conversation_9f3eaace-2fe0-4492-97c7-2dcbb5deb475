<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;


class EnsureTokenIsValid
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            Controller::checkEditorApiToken($request->bearerToken());
            Log::info('EnsureTokenIsValid: ' . (!empty($idJob) ? $idJob : '-') . ' success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (EditorApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('EnsureTokenIsValid: ' . (!empty($idJob) ? $idJob : '-') . ' Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('EnsureTokenIsValid: ' . (!empty($idJob) ? $idJob : '-') . ' Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }
        return $next($request);
    }
}
