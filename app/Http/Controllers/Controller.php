<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;
use App\Exceptions\SalesforceApiException;
use App\Exceprions\EditorApiException;
use App\Models\User;
use App\Models\Job;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;


    public static function checkEditorApiToken($token)
    {
        if (empty($token)){
            throw new EditorApiException('token missing');
        }
        if ($token !== config('app.EDITOR_API_KEY')) {
            Log::error('editor API - wrong API key: ' . substr($token, 0, 3) . '...' . substr($token, -3));
            throw new EditorApiException('editor API - wrong API key');
        }
    }


}


