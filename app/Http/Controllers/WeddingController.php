<?php

namespace App\Http\Controllers;

use App\ModelHelper\Mailer;
use App\Models\AktivitaetenDoc;
use App\Models\cmykPDF;
use App\Models\Datei;
use App\Models\Document;
use App\Models\Druckdateien;
use App\Models\Einzelhashdateien;
use App\Models\File;
use App\Models\Hashdateien;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\Jobdokumente;
use App\Models\Schrift;
use App\Models\Transmits;
use App\Models\User;
use App\Models\Weddingmaker;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use setasign\Fpdi\PdfParser\StreamReader;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

define('FPDF_FONTPATH', __DIR__ . '/../../../resources/font/');

class WeddingController extends Controller
{
    const dateTime = "Y-m-d H:i:s";

    public function get_wedding_preview($id_doc)
    {
        $jobdokumente = Jobdokumente::find($id_doc);
        $adressfile = Jobdokumente::getAdressfile($jobdokumente->id_job);
        if (empty($adressfile)) {
            return view('fehler', ['fehler' => 'In diesem Projekt fehlen Testadressen.']);
        }
        $id_file = $adressfile->id;
        $job = Job::find($jobdokumente->id_job);
        $datei = Jobdokumente::find($id_file);
        $file = Document::find($datei->id_document);
        $file_contents = $file->inhalt;
        $summe_delimiters = array(0, 0, 0, 0);
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }


        return view('weddingmakerpreview', ['id_doc' => $id_doc, 'id_file' => $id_file, 'job' => $job]);
    }

    public function weddingdocdownload($id)
    {
        $jobdok = Jobdokumente::find($id);
        $adressfile = Jobdokumente::getAdressfile($jobdok->id_job);
        $id_file = $adressfile->id;
        $file = json_decode(WeddingController::weddingmakerpreview($id, $id_file, 10), ARRAY_FILTER_USE_BOTH);
        return response(base64_decode($file[0]['file']))
            ->header('Content-Disposition', 'attachment; filename=' . time() . '.pdf')
            ->header('Content-Type', 'application/pdf');
    }

    public function mcView($docId = null, $fileId = null, $dataset = null, $showVars = 0)
    {
        $jobDoc = Jobdokumente::find($docId);
        if (!empty($jobDoc) && !empty($fileId)) {
            $jobId = $jobDoc['id_job'];
            $userId = Auth::id();
            $jobUsers = Jobbenutzer::Where('id_job', $jobId)->Where('id_benutzer', $userId)->Where('ansehen', 1)->Where('gesperrt', 0)->first();
            if (!empty($jobUsers)) {
                $file = WeddingController::weddingpreview($docId, $fileId, 1, 1, $dataset + 1, $showVars);
                $pdf = json_decode($file, ARRAY_FILTER_USE_BOTH);
                return response(base64_decode($pdf[0]['file']))
                    ->header('Content-Disposition', 'inline; filename=' . time() . '.pdf')
                    ->header('Content-Type', 'application/pdf');
            } else {
                return response()->json(['message' => 'Not allowed!'], 401);
            }

        } else {
            return response()->json(['message' => 'Not Found!'], 404);
        }

    }

    public function get_wedding_preview_with_bw($id, $id_file, $showDataSetNr = null, $showVars = null)
    {
        $file = WeddingController::weddingpreview($id, $id_file, 10, config('app.max_sw_preview'), $showDataSetNr, $showVars);
        $content = $file;
        return response(str_replace("\n", '', $content));
    }

    public function get_wedding_showVars($id, $id_file, $showVars = null)
    {
        $file = WeddingController::weddingpreview($id, $id_file, 10, config('app.max_sw_preview'), null, $showVars);
        $content = $file;
        return response(str_replace("\n", '', $content));
    }

    public function set_reset_document($id)
    {
        $file = Jobdokumente::reset_document($id);
        return redirect('weddingmaker/' . $id);
    }

    public function set_reset_storno_document(Request $request)
    {
        $id = $request->id_bearbeiten;
        $result_storno = FreigabeController::dokstorno($id);
        $file = Jobdokumente::reset_document($id);
        return redirect('weddingmaker/' . $id);
    }

    public function get_wedding_pdf_from_testdata($id)
    {
        $jobdokumente = Jobdokumente::find($id);
        $adressfile = Jobdokumente::getAdressfile($jobdokumente->id_job);
        $id_file = $adressfile->id;
        $file = WeddingController::weddingmakerpreview($id, $id_file);
        $content = ($file);
        return response(str_replace("\n", '', $content));
    }


    public function get_wedding_pdf($id, $id_file, $showDataSetNr = null)
    {
        $file = WeddingController::weddingmakerpreview($id, $id_file, 10, $showDataSetNr);
        $content = $file;
        return response(str_replace("\n", '', $content));
    }

    /**
     * Display the specified resource.
     *
     * @param int $id_weddingdoc
     * @return \Illuminate\Http\Response
     */

    public function get_wedding_by_id($id_doc)
    {
        $jobdokumente = Jobdokumente::find($id_doc);
        $weddingmaker = Weddingmaker::Where('id_dokument', $jobdokumente->id_document)->orderBy('seite', 'ASC')->orderBy('y', 'ASC')->get();
        $adressfile = Jobdokumente::getAdressfile($jobdokumente->id_job);
        if (empty($adressfile)) {
            return view('fehler', ['fehler' => 'In diesem Projekt fehlen Testadressen.']);
        }
        $id_file = $adressfile->id;
        $datei = Jobdokumente::find($id_file);
        $file = Document::find($datei->id_document);
        $file_contents = $file->inhalt;
        $summe_delimiters = array(0, 0, 0, 0);
        $delim = ';';
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }
        $trenner = array_keys($summe_delimiters, max($summe_delimiters));
        $delim = $delimiters[$trenner[0]];
        $file_array_tmp = explode("\n", $file_contents);
        $header = explode(';', $file_array_tmp[0]);
        $schriften = Schrift::all();
        return view('weddingmaker', ['id_doc' => $id_doc, 'id_file' => $id_file, 'weddingmaker' => $weddingmaker, 'spalten' => $header, 'schriften' => $schriften]);
    }

    private function weddingpreview($id_weddingdoc, $id_file, $breakfarbe = 10, $breakall = null, $showDataSetNr = null, $showVars = null)
    {
        $doc = Document::getWeddingdocById($id_weddingdoc);
        $doc_schwarzfilm = Document::getSchwarzfilm($doc->id_job);
        $doc_hash = Document::getHash($doc->id_job)->first();
        $datei = Datei::find($id_file);
        $file = File::find($datei->id_file);
        $file_contents = FileController::file_entschluesseln($file->file);
        $genrueckgabe = WeddingController::generateFromMaker($doc->id, $file_contents, $doc->inhalt, $breakfarbe, $doc_schwarzfilm->inhalt, $doc_hash->inhalt, $breakall, $showDataSetNr, $showVars);
        $inhalt = $genrueckgabe['inhalt'];
        $rueckgabe['file'] = base64_encode(($inhalt));
        return ("[" . json_encode($rueckgabe) . "]");
    }

    public function get_weddingmaker(Request $request, $id_doc)
    {
        $data = $request->all();
        $makerdaten = [];
        $jobdok = Jobdokumente::find($id_doc);
        if (empty($request->filedata)) {
            foreach ($data as $key => $value) {
                if (strpos($key, '_') !== false) {
                    $tmp = explode('_', $key);
                    if (is_numeric($tmp[0]) && $value != '') {
                        $makerdaten[$tmp[0]][$tmp[1]] = $value;
                    }
                }
            }
            foreach ($makerdaten as $id => $daten) {
                $weddingmaker = Weddingmaker::find($id);
                if (!empty($daten['spalten']) && !empty($daten['seite'])) {
                    $weddingmaker->store(
                        $id,
                        $jobdok->id_document,
                        $daten['seite'],
                        $daten['x'],
                        $daten['y'],
                        $daten['spalten'],
                        $daten['max'] ?? null,
                        $daten['size'] ?? null,
                        $daten['farbe'],
                        $daten['font'],
                        $daten['trenner'] ?? null
                    );
                } else {
                    $weddingmaker->delete();
                }
            }
            if (!empty($data['seiteneu'])) {
                foreach ($data['seiteneu'] as $key => $value) {
                    if (!empty($data['spaltenneu'][$key])) {
                        $weddingmaker = new Weddingmaker();
                        $weddingmaker->store(null, $jobdok->id_document, $value, $data['xneu'][$key], $data['yneu'][$key], $data['spaltenneu'][$key], $data['maxneu'][$key], $data['sizeneu'][$key], $data['farbeneu'][$key], $data['font'][$key], $data['trennerneu'][$key]);
                    }
                }
            }
        } else {
            $filedata = $request->filedata;
            $daten_array = FileController::getExcelDaten($filedata->getRealPath());
            $header = array_shift($daten_array);
            if ($header[1] == 'Seite') {
                $weddingdaten = Weddingmaker::Where('id_dokument', $jobdok->id_document);
                $weddingdaten->delete();
                foreach ($daten_array as $daten) {
                    if (!empty($daten[4])) {
                        $weddingmaker = new Weddingmaker();
                        $weddingmaker->store(null, $jobdok->id_document, $daten[1], $daten[2], $daten[3], $daten[4], $daten[5], $daten[7], $daten[8], $daten[9], $daten[6]);
                    }
                }
            }
        }
        if (!empty($data['wedding_ok']) && $data['wedding_ok'] == 'on') {
            $jobdok->wedding_ok = 1;
            $jobdok->save();
            return redirect('freigabe/' . $jobdok->id_job);
        }
        return redirect('weddingmaker/' . $id_doc . '?pdfpage=' . $data['pdfpage'] . '&pdfzoom=' . $data['zoom']);
    }

    private function weddingmakerpreview($id_weddingdoc, $id_file, $anzahldatensaetze = 10, $showDataSetNr = null)
    {
        $doc = Document::getWeddingdocById($id_weddingdoc);
        $doc_schwarzfilm = null;
        $doc_hash = null;
        if (!empty($doc->id_job)) {
            $doc_schwarzfilm = Document::getSchwarzfilm($doc->id_job);
            $doc_hash = Document::getHash($doc->id_job)->first();
        }
        $datei = Jobdokumente::find($id_file);
        $file = Document::find($datei->id_document);
        $file_contents = $file->inhalt;
        $genrueckgabe = WeddingController::generateFromMaker($doc->id, $file_contents, $doc->inhalt, $anzahldatensaetze, $doc_schwarzfilm->inhalt, $doc_hash->inhalt, $showDataSetNr);
        $inhalt = $genrueckgabe['inhalt'];
        $rueckgabe['file'] = base64_encode($inhalt);
        return "[" . json_encode($rueckgabe) . "]";
    }

    /**
     * Display the specified resource.
     *
     * @param int $id_weddingdoc
     * @return \Illuminate\Http\Response
     */
    public static function generatePrintdoc()
    {
        $dateien = Datei::Where('created_at', '<', Carbon::now()->subMinute(config('timings.send_delay'))->format(self::dateTime))
            ->Where('generiert', '0')->Where('datensaetze', '>', 0)->WhereNull('stopzeit')->get();
        $result_csv = false;
        $result_org = false;
        $result_druck = false;
        $editorResult = false;
        Log::info("Found " . count($dateien) . " CSV-files");
        foreach ($dateien as $datei) {
            $isEditorProject = $datei->isEditorProject();
            if ($isEditorProject) {
                $editorController = new EditorController();
                $editorResult = $editorController->generatePrintPdf($datei->id);
            } else {
                Log::info("Generating for CSV:  " . $datei->org_name);
                $adressdatei = File::find($datei->id_file);
                $file_contents = FileController::file_entschluesseln($adressdatei->file);
                $druckdateien = Document::getDruckdateien($datei->id_job);
                $dokument_schwarzfilm = Document::getSchwarzfilm($datei->id_job);
                $weddingDoc = Document::getWeddingdocByJobId($datei->id_job);
                foreach ($druckdateien as $druckdatei) {
                    Log::info("Generating for file:  " . $druckdatei->name);
                    $genrueckgabe = WeddingController::generateFromMaker($weddingDoc->id, $file_contents, $dokument_schwarzfilm->inhalt, null);
                    Log::info("Generating done:  " . $druckdatei->name);
                    $inhalt = $genrueckgabe['inhalt'];
                    $steuerdatei_daten = $genrueckgabe['steuerdaten'];
                    unset($genrueckgabe);
                    unset($file_contents);
                    $key = array_keys(!empty($steuerdatei_daten[0]) ? $steuerdatei_daten[0] : []);

                    $csv_content = WeddingController::str_putcsv($key, ';') . "\n";
                    foreach ($steuerdatei_daten as $fields) {
                        $csv_content .= WeddingController::str_putcsv($fields, ';') . "\n";
                    }
                    $hoch_datei = Document::find($druckdatei->id);
                    $doc2job = Jobdokumente::Where('id_job', $datei->id_job)->Where('id_document', $hoch_datei->id)->first();
                    $tmp_name = explode('.', $druckdatei->name);
                    $druckdatei_org = new Druckdateien();
                    $org_filename = 'Druckdatei_#' . $datei->id_job . '_' . $datei->step_id . '.' . $tmp_name[1];
                    $result_org = $druckdatei_org->store($datei->id_job, $datei->step_id, $org_filename, $doc2job->anzeige_dienstleister, $hoch_datei->inhalt, $hoch_datei->mime);
                    if ($result_org) {
                        unset($hoch_datei);
                        unset($druckdatei_org);
                        $csv_filename = 'Steuerdatei_#' . $datei->id_job . '_' . $datei->step_id . '.csv';
                        $csvdatei = new Druckdateien();
                        $result_csv = $csvdatei->store($datei->id_job, $datei->step_id, $csv_filename, $doc2job->anzeige_dienstleister, $csv_content, 'text/csv');
                    }

                    if ($result_csv) {
                        unset($csvdatei);
                        $filename = 'Eindruck_#' . $datei->id_job . '_' . $datei->step_id . '.pdf';
                        $druckdatei = new Druckdateien();
                        try {
                            $result_druck = $druckdatei->store($datei->id_job, $datei->step_id, $filename, $doc2job->anzeige_dienstleister, $inhalt, 'application/pdf');
                        } catch (\Illuminate\Database\QueryException $ex) {
                            $job = Job::find($datei->id_job);
                            $jobuser = Jobbenutzer::where('id_job', $datei->id_job)->get();
                            foreach ($jobuser as $juser) {
                                $userinfo = User::find($juser->id_benutzer);
                                if (!empty($userinfo) && $userinfo->is_agentur() && $juser->benachrichtigung == 1) {
                                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                    $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'fehler' => 'Fehler bei der Speicherung der Eindruckdatei', 'dateibezeichnung' => $filename, 'projekt_id' => $job->id, 'stepid' => $datei->step_id, 'projektname' => $job->jobbezeichnung);

                                    Mailer::sendMailByTemplate('EMAIL_SYSTEMMELDUNG_TGD', $maildata);
                                }
                            }
                            Log::error("Could not save file to DB. ID job: " . $datei->id_job);
                        }
                    }

                    if ($result_druck) {
                        unset($inhalt);
                        unset($druckdatei);
                        $job = Job::find($datei->id_job);
                        $content = $job->sf_preview;
                        $filename = 'Ansicht_#' . $datei->id_job . '_' . $datei->step_id . '.pdf';
                        $druckdatei = new Druckdateien();
                        $druckdatei->store($datei->id_job, $datei->step_id, $filename, $doc2job->anzeige_dienstleister, $content, 'application/pdf');
                    }
                }
                if ($result_csv && $result_org && $result_druck) {
                    unset($druckdatei);
                    $generiert = Datei::find($datei->id);
                    $generiert->generiert = 1;
                    $generiert->save();
                }
            }
        }
        if (($result_csv && $result_org && $result_druck) || $editorResult) {
            return 'done';
        }
        return 'error';
    }

    private static function fontsFromMaker($platzierungen)
    {
        $genutzteSchriften = [];
        foreach ($platzierungen as $tmp_schrift) {
            if (!in_array($tmp_schrift->font, $genutzteSchriften) && !empty($tmp_schrift->font)) {
                $genutzteSchriften[] = $tmp_schrift->font;
            }
            if (!in_array($tmp_schrift->font, $genutzteSchriften)) {
                $genutzteSchriften[] = 'AvenirNextLTPro';
            }
        }
        if (empty($genutzteSchriften)) {
            $genutzteSchriften[] = 'AvenirNextLTPro';
        }

        return $genutzteSchriften;
    }

    private static function datasetFromMaker($file_contents)
    {
        $startTime = microtime(true);
        $summe_delimiters = array(0, 0, 0, 0);
        $delim = ';';
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }
        $trenner = array_keys($summe_delimiters, max($summe_delimiters));
        $delim = $delimiters[$trenner[0]];
        $file_array_tmp = explode("\n", $file_contents);

        Log::info('Reading CSV file.');
        foreach ($file_array_tmp as $i => $row) {
            $row = str_replace("\r", "", $row);
            if ($i == 0) {
                $header = explode($delim, $row);
                continue;
            }
            //$row = FileController::checkEncoding($row, 'Weddingcontroller - datasetFromMaker');

            $data = explode($delim, $row);
            $num = count($data);

            for ($c = 0; $c < $num; $c++) {
                if (!empty($header[$c])) {
                    $array_dataset[$i][$header[$c]] = $data[$c];
                }
            }
        }
        $stop = microtime(true);
        $diff = $stop - $startTime;
        return $array_dataset;
    }

    private static function generateFromMaker($id_dokument, $file_contents, $doc_inhalt, $anzahldatensaetze = 10, $merge_schwarzfilm = null, $merge_hash = null, $breakall = null, $dataSetNum = null, $showVars = null)
    {
        $platzierungen = Weddingmaker::Where('id_dokument', $id_dokument)->get();
        $array_dataset = WeddingController::datasetFromMaker($file_contents);
        $genutzteSchriften = WeddingController::fontsFromMaker($platzierungen);
        $font = $genutzteSchriften[0];
        $bschnitt_x = 1;
        $bschnitt_y = 0.75;
        $pdf = new cmykPDF();
        $pdf->SetMargins(0, 0, -500);
        foreach ($genutzteSchriften as $genutzteschrift) {
            $schrift = Schrift::Where('bezeichnung', $genutzteschrift)->first();
            $pdf->AddFont($schrift->bezeichnung, '', $schrift->file);
        }
        $pdf->SetAutoPageBreak(0);
        $gesamtseiten = 1;
        $steuerdatei_daten = [];
        if (!empty($array_dataset)) {
            Log::info('Generating print file with ' . count($array_dataset) . ' datasets.');
            $steuerdatei_daten = WeddingController::generatePdfFile($pdf, $doc_inhalt, $array_dataset, $font, $bschnitt_x, $bschnitt_y, $gesamtseiten, $anzahldatensaetze, $platzierungen, $dataSetNum, $showVars);

            if (!empty($merge_hash)) {
                Log::info('Generating archive file.');
                WeddingController::generatePdfFile($pdf, $merge_hash, $array_dataset, $font, $bschnitt_x, $bschnitt_y, $gesamtseiten, $anzahldatensaetze, $platzierungen, $dataSetNum, $showVars);
            }
            if (!empty($merge_schwarzfilm)) {
                Log::info('Generating schwarzfilm file.');
                WeddingController::generatePdfFile($pdf, $merge_schwarzfilm, $array_dataset, $font, $bschnitt_x, $bschnitt_y, $gesamtseiten, $breakall, $platzierungen, $dataSetNum, $showVars);
            }
        }
        $genrueckgabe = [];
        $genrueckgabe['steuerdaten'] = [];
        $genrueckgabe['inhalt'] = $pdf->Output('S');
        if (!empty($steuerdatei_daten)) {
            $genrueckgabe['steuerdaten'] = $steuerdatei_daten;
        }
        return $genrueckgabe;
    }

    private static function generatePdfDatensatz(cmykPDF &$pdf, $font, $bschnitt_x, $bschnitt_y, &$gesamtseiten, $pageCount, $platzierungen, $dataset, $showVars = null)
    {
        $vars = [];
        $dataset_tmp = $dataset;
        $dataset = [];
        foreach ($dataset_tmp as $key => $data) {
            $hex = bin2hex($key);
            if (substr($hex, 0, 6) !== 'efbbbf') {
                $dataset[$key] = $data;
            } else {
                $dataset[hex2bin(substr($hex, 6))] = $data;
            }
        }
        if (!empty($platzierungen)) {
            foreach ($platzierungen as $platz) {
                $tmp_ersetzung = explode(';', $platz->spalten);
                $ersetzung = '';
                foreach ($tmp_ersetzung as $value) {
                    if (!empty($dataset[$value])) {
                        $ersetzung .= trim($dataset[$value]) . " ";
                    } else if (!empty($ersetzung) && $value == '_#_') {
                        $ersetzung = trim($ersetzung) . $platz->trenner . " ";
                    }
                }
                $vars[$platz->seite][] = array(
                    'x' => $platz->x,
                    'y' => $platz->y,
                    'value' => trim($ersetzung),
                    'max' => $platz->max,
                    'size' => $platz->size,
                    'farbe' => $platz->farbe,
                    'font' => $platz->font
                );
            }
        }
        for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
            $templateId = $pdf->importPage($pageNo);
            $pdf->AddPage();
            $pdf->useTemplate($templateId, ['adjustPageSize' => true]);
            $pdf->SetFont($font);
            if (!empty($vars[$pageNo])) {
                foreach ($vars[$pageNo] as $var) {
                    $pdf->SetXY($var['x'] - $bschnitt_x, $var['y'] - $bschnitt_y);
                    if (!empty($var['farbe'])) {
                        $array_farbe = explode(',', $var['farbe']);
                        $pdf->SetTextColor(intval($array_farbe[0]), intval($array_farbe[1]), intval($array_farbe[2]), intval($array_farbe[3]));
                    } else {
                        $pdf->SetTextColor(0, 0, 0, 80);
                    }

                    if ($showVars == 1) {
                        $pdf->SetTextColor(0, 100, 0, 0);
                    }

                    if (!empty($var['font'])) {
                        $pdf->SetFont($var['font'], '', $var['size']);
                    } else {
                        $pdf->SetFont($font, '', $var['size']);
                    }
                    try {
                        $pdf->Write(0, iconv('UTF-8', 'windows-1252', ($var['max'] > 0 ? substr($var['value'], 0, $var['max']) : $var['value'])));
                    } catch (Exception $e) {
                        Log::error('Value: ' . $var['value']);
                        Log::error('Exception abgefangen: ' . $e->getMessage());
                    }

                }
            }
            $dataset['Seite ' . $pageNo] = $gesamtseiten;
            $gesamtseiten++;
        }
        return $dataset;
    }

    private static function generatePdfFile(cmykPDF &$pdf, $druck_content, $array_dataset, $font, $bschnitt_x, $bschnitt_y, &$gesamtseiten, $anzahldatensaetze, $platzierungen, $dataSetNum = null, $showVars = null)
    {
        $counter = 0;
        $stream = StreamReader::createByString($druck_content);
        $pageCount = $pdf->setSourceFile($stream);
        foreach ($array_dataset as $dataset) {
            $counter++;
            if (!empty($dataSetNum) && (($counter + 1) != $dataSetNum)) {
                continue;
            }
            if ($counter % 1000 == 0) {
                Log::debug('generated print rows ' . $counter . ' rows.');
            }

            $tmp_dataset = WeddingController::generatePdfDatensatz($pdf, $font, $bschnitt_x, $bschnitt_y, $gesamtseiten, $pageCount, $platzierungen, $dataset, $showVars);
            $merged_dataset = array_merge($dataset, $tmp_dataset);
            $steuerdatei_daten[] = $merged_dataset;
            if ($counter >= $anzahldatensaetze && $anzahldatensaetze != null) {
                break;
            }
        }
        if (!empty($steuerdatei_daten)) {
            return $steuerdatei_daten;
        }

    }

    private static function generateEinzelFromMaker($id_dokument, $file_contents, $doc_inhalt, $id_job = null, $step_id = null)
    {
        $platzierungen = Weddingmaker::Where('id_dokument', $id_dokument)->get();
        $array_dataset = WeddingController::datasetFromMaker($file_contents);
        $genutzteSchriften = WeddingController::fontsFromMaker($platzierungen);

        $font = $genutzteSchriften[0];
        $bschnitt_x = 1;
        $bschnitt_y = 0.75;
        $druck_content = $doc_inhalt;
        $pdf = new cmykPDF();
        $pdf->SetMargins(0, 0, -500);
        foreach ($genutzteSchriften as $genutzteschrift) {
            $schrift = Schrift::Where('bezeichnung', $genutzteschrift)->first();
            $pdf->AddFont($schrift->bezeichnung, '', $schrift->file);
        }
        $pdf->SetAutoPageBreak(0);
        $farbcounter = 0;
        $gesamtseiten = 1;
        if (!empty($array_dataset)) {
            foreach ($array_dataset as $dataset) {
                $pdf = new cmykPDF();
                $pdf->SetMargins(0, 0, -500);
                $stream = StreamReader::createByString($druck_content);
                $pageCount = $pdf->setSourceFile($stream);
                foreach ($genutzteSchriften as $genutzteschrift) {
                    $schrift = Schrift::Where('bezeichnung', $genutzteschrift)->first();
                    $pdf->AddFont($schrift->bezeichnung, '', $schrift->file);
                }
                $pdf->SetAutoPageBreak(0);
                $farbcounter++;

                WeddingController::generatePdfDatensatz($pdf, $font, $bschnitt_x, $bschnitt_y, $gesamtseiten, $pageCount, $platzierungen, $dataset);

                $filename = 'Seite' . ($gesamtseiten - 1) . '_' . $step_id . '.pdf';
                if (!empty($dataset['VERTRAGSKONTO'])) {
                    $filename = $dataset['VERTRAGSKONTO'] . '_' . $step_id . '.pdf';
                }

                $content = $pdf->Output('S');
                $druckdatei = new Einzelhashdateien();
                try {
                    if (!empty($dataset['VERTRAGSKONTO'])) {
                        $druckdatei->store($id_job, $step_id, $filename, $dataset['VERTRAGSKONTO'], $content, 'application/pdf');
                        $transmit = new Transmits();
                        $transmit->id_datei = $druckdatei->id;
                        $transmit->id_liefertyp = 1;
                        $transmit->save();
                    } else {
                        Log::error("archive file could not be created: JobId " . $id_job . ", stepId " . $step_id . ",  VERTRAGSKONTO is empty. Skipping this file.");
                    }
                } catch (\Illuminate\Database\QueryException $ex) {
                    Log::error("archive file could not be created: JobId " . $id_job . ", stepId " . $step_id . ",  VERTRAGSKONTO " . ($dataset['VERTRAGSKONTO'] ? $dataset['VERTRAGSKONTO'] : '') . " Skipping this file.");
                }

            }
        }
        return true;
    }

    public function generateHashdoc()
    {
        $dateien = Datei::Where('created_at', '<', Carbon::now()
            ->subMinute(config('timings.send_delay'))
            ->format(self::dateTime))
            ->Where('hasherzeugt', '0')
            ->Where('datensaetze', '>', 0)
            ->WhereNull('stopzeit')->get();


        foreach ($dateien as $datei) {
            $adressdatei = File::Find($datei->id_file);
            $file_contents = FileController::file_entschluesseln($adressdatei->file);
            $doc = Document::getWeddingdocByJobId($datei->id_job);
            $druckdateien = Document::getHash($datei->id_job)->get();
            foreach ($druckdateien as $druckdatei) {
                $genrueckgabe = WeddingController::generateFromMaker($doc->id, $file_contents, $druckdatei->inhalt, null);
                $content = $genrueckgabe['inhalt'];
                $tmp_name = explode('.', $druckdatei->name);
                $filename = 'Hashdatei_#' . $datei->id_job . '_' . $datei->step_id . '.' . $tmp_name[1];
                $druckdatei = new Hashdateien();
                $result_hash = $druckdatei->store($datei->id_job, $datei->step_id, $filename, $content, 'application/pdf');
                if ($result_hash) {
                    $hasherzeugt = Datei::find($datei->id);
                    $hasherzeugt->hasherzeugt = 1;
                    $hasherzeugt->save();
                }
            }
        }
    }

    public static function generateEinzelHashdoc()
    {
        $dateien = Datei::Where('created_at', '<', Carbon::now()
            ->subMinute(config('timings.send_delay'))
            ->format(self::dateTime))
            ->Where('transmit_erzeugt', '0')
            ->Where('datensaetze', '>', 0)
            ->WhereNull('stopzeit')
            ->get();

        foreach ($dateien as $datei) {
            Log::debug('generateEinzelHashdoc: Processing datei ID ' . $datei->id . ', file ID ' . $datei->id_file . '.');
            $isEditorProject = $datei->isEditorProject();
            if ($isEditorProject) {
                $editorController = new EditorController();
                $editorResult = $editorController->generatePrintPdf($datei->id);
            }
            if($editorResult) {
                $customers = (CsvController::getAdressData($datei->id));
                $dataSet = 1;
                foreach($customers AS $customer) {
                    $filename = 'Seite_Customer_' . $dataSet . '_' . $datei->step_id . '.pdf';
                    if (!empty($customer['VERTRAGSKONTO'])) {
                        $filename = $customer['VERTRAGSKONTO'] . '_' . $datei->step_id  . '.pdf';
                    }
                    Log::debug('generateEinzelHashdoc: Get Content(Dataset '. $dataSet.') datei ID ' . $datei->id . ', file ID ' . $datei->id_file . '.');
                    $content = $editorController->returnSingleArchivePDF($datei->id, $dataSet);
                    $archiveFile = new Einzelhashdateien();
                    try {
                        if (!empty($customer['VERTRAGSKONTO'])) {
                            $archiveFile->store($datei->id_job, $datei->step_id, $filename, $customer['VERTRAGSKONTO'], $content, 'application/pdf');
                            $transmit = new Transmits();
                            $transmit->id_datei = $archiveFile->id;
                            $transmit->id_liefertyp = 1;
                            $transmit->save();
                        } else {
                            Log::error("archive file could not be created: JobId " . $datei->id_job . ", stepId " . $datei->step_id . ",  VERTRAGSKONTO is empty. Skipping this file.");
                        }
                    } catch (\Illuminate\Database\QueryException $ex) {
                        Log::error("archive file could not be created: JobId " . $datei->id_job . ", stepId " . $datei->step_id . ",  VERTRAGSKONTO " . ($customer['VERTRAGSKONTO'] ? $customer['VERTRAGSKONTO'] : '') . " Skipping this file.");
                    }
                    $dataSet++;
                }

            } else {
                $adressdatei = File::Find($datei->id_file);
                if (strlen($adressdatei->file) == 0) {
                    Log::error("generateEinzelHashdoc: datei ID " . $datei->id . ", file ID " . $datei->id_file . " is empty. Skipping this file.");
                    continue;
                }
                $file_contents = FileController::file_entschluesseln($adressdatei->file);
                $doc = Document::getWeddingdocByJobId($datei->id_job);
                $druckdateien = Document::getHash($datei->id_job)->get();
                foreach ($druckdateien as $druckdatei) {
                    $check_rejection = AktivitaetenDoc::Where('id_document2job', $druckdatei->d2j_id)->Where('id_docaktivitaet', '7')->first();
                    if (empty($check_rejection)) {
                        $genrueckgabe = WeddingController::generateEinzelFromMaker($doc->id, $file_contents, $druckdatei->inhalt, $datei->id_job, $datei->step_id);
                        if ($genrueckgabe) {
                            $hasherzeugt = Datei::find($datei->id);
                            $hasherzeugt->hasherzeugt = 1;
                            $hasherzeugt->transmit_erzeugt = 1;
                            $hasherzeugt->save();
                        }
                    }
                }
            }
        }
    }

    private static function str_putcsv($input, $delimiter = ',', $enclosure = '')
    {
        $fp = fopen('php://temp', 'r+');
        fputcsv($fp, $input, $delimiter);
        rewind($fp);
        $data = fread($fp, 1048576);
        fclose($fp);
        return rtrim($data);
    }


    public static function getMakerCsv($id = null)
    {
        if (!empty($id)) {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $makerData = Weddingmaker::Where('id_dokument', $id)->orderBy('seite')->orderBy('y')->get();
            $count = 1;
            $row = 1;
            $sheet->setCellValueByColumnAndRow(1, $row, "Nummer");
            $sheet->setCellValueByColumnAndRow(2, $row, "Seite");
            $sheet->setCellValueByColumnAndRow(3, $row, "X (mm)");
            $sheet->setCellValueByColumnAndRow(4, $row, "Y (mm)");
            $sheet->setCellValueByColumnAndRow(5, $row, "Spalte(n)");
            $sheet->setCellValueByColumnAndRow(6, $row, "Zeichenbegrenzung");
            $sheet->setCellValueByColumnAndRow(7, $row, "Sonderzeichen");
            $sheet->setCellValueByColumnAndRow(8, $row, "Pt.");
            $sheet->setCellValueByColumnAndRow(9, $row, "CMYK");
            $sheet->setCellValueByColumnAndRow(10, $row, "Schriftart");
            foreach ($makerData as $dataset) {
                $row = $count + 1;
                $sheet->setCellValueByColumnAndRow(1, $row, $count);
                $sheet->setCellValueByColumnAndRow(2, $row, $dataset->seite);
                $sheet->setCellValueByColumnAndRow(3, $row, $dataset->x);
                $sheet->setCellValueByColumnAndRow(4, $row, $dataset->y);
                $sheet->setCellValueByColumnAndRow(5, $row, $dataset->spalten);
                $sheet->setCellValueByColumnAndRow(6, $row, !empty($dataset->max) ? $dataset->max : '');
                $sheet->setCellValueByColumnAndRow(7, $row, $dataset->trenner);
                $sheet->setCellValueByColumnAndRow(8, $row, $dataset->size);
                $sheet->setCellValueByColumnAndRow(9, $row, $dataset->farbe);
                $sheet->setCellValueByColumnAndRow(10, $row, $dataset->font);
                $count++;
            }

            $writer = IOFactory::createWriter($spreadsheet, "Xlsx");

            ob_start();
            $writer->save('php://output');
            $response = ob_get_clean();
            return base64_encode($response);
        }
    }
}
