<?php

namespace App\Http\Controllers;

use App\ModelHelper\Mailer;
use App\ModelHelper\MailerPayload;
use App\ModelHelper\SftpClient;
use App\Models\BenutzerA;
use App\Models\Datei;
use App\Models\Dienstleister;
use App\Models\Druckdateien;
use App\Models\Einzelresponses;
use App\Models\Ftpdienstleister;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\PWLs;
use App\Models\Responses;
use App\Models\SonstigeResponses;
use App\Models\Transmits;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use League\Flysystem\Config;
use League\Flysystem\Filesystem;
use League\Flysystem\PhpseclibV2\SftpAdapter;
use League\Flysystem\PhpseclibV2\SftpConnectionProvider;
use League\Flysystem\FilesystemReader;
use Illuminate\Support\Facades\Storage;
use App\Models\Printstatus;
use Illuminate\Support\Carbon;


class FTPController extends Controller
{
    /**
     * Display the specified resource.
     *
     * @param int $id_weddingdoc
     * @return \Illuminate\Http\Response
     */
    public static function ausliefern_dienstleister()
    {
        $druckdateien = Druckdateien::Where('uebergabe_dienstleister', null)->get();
        $result = false;
        $zugangsdaten_last = "";
        $zugang = [];

        foreach ($druckdateien as $druckdatei) {
            $zugangsdaten = Ftpdienstleister::Where('id_job', $druckdatei->id_job)
                ->Where('aktiv', 1)
                ->Where('id_dienstleister', $druckdatei->id_dienstleister)
                ->first();

            if ($zugangsdaten != $zugangsdaten_last) {
                $ftp = new SftpClient($zugangsdaten->host, 22, $zugangsdaten->user, $zugangsdaten->pass, $zugangsdaten->auth_file, $zugangsdaten->root . 'Eingang/');
                $ftp->connect();
            }
            $zugangsdaten_last = $zugangsdaten;

            $job = Job::find($druckdatei->id_job);
            $datei = Datei::Where('id_job', $druckdatei->id_job)
                ->Where('step_id', $druckdatei->step_id)->first();
            if ($job) {
                /*
                $projektinfo[$druckdatei->id_job][$druckdatei->step_id][0] = ['bezeichnung' => 'ProjektID', 'value' => $druckdatei->id_job];
                $projektinfo[$druckdatei->id_job][$druckdatei->step_id][1] = ['bezeichnung' => 'StepID', 'value' => $druckdatei->step_id];
                $projektinfo[$druckdatei->id_job][$druckdatei->step_id][2] = ['bezeichnung' => 'Projektbezeichnung', 'value' => $job->jobbezeichnung];
                $projektinfo[$druckdatei->id_job][$druckdatei->step_id][3] = ['bezeichnung' => 'Anzahl Datensätze', 'value' => $datei->datensaetze];
                */
                $projektinfo[$druckdatei->id_job][$druckdatei->step_id] = [
                    'id_job' => $druckdatei->id_job,
                    'step_id' => $druckdatei->step_id,
                    'jobbezeichnung' => $job->jobbezeichnung,
                    'datensaetze' => $datei->datensaetze,
                ];
                $zugang[$druckdatei->id_job] = $zugangsdaten;
            }

            if (!empty($zugangsdaten)) {
                $return = FTPController::upload($zugangsdaten, $druckdatei);
                $result[$druckdatei->id_job] = $return;
                if ($result[$druckdatei->id_job]) {
                    $update_druckdatei = Druckdateien::find($druckdatei->id);
                    $update_druckdatei->uebergabe_dienstleister = now();
                    $update_druckdatei->save();
                } else {

                    $jobuser = Jobbenutzer::where('id_job', $job->id)->get();

                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);
                        $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);

                        if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                            $userdata = User::find($juser->id_benutzer, [
                                'id',
                                'email',
                                'name',
                                'vorname'
                            ]);
                            $mailerPayload = new MailerPayload($job->id, $job->jobbezeichnung, $dienstleister->dienstleister, $datei->datensaetze, $druckdatei->step_id, config('app.APP_URL') . 'freigabe/' . $job->id, 'Datei konnte nicht abgelegt werden.');
                            $mailer = new Mailer($userdata, 'EMAIL_DRUCKFTP_FEHLER_TGD', $mailerPayload);
                            $mailer->execute();
                        }
                    }
                }
            }
        }

        if (!empty($projektinfo) && $result) {
            foreach ($projektinfo as $key => $stepIdValues) {
                foreach ($stepIdValues AS $pvalue) {
                    $zugang_tmp = $zugang[$key];
                    $job = Job::find($druckdatei->id_job);
                    $dienstleister = Dienstleister::find($druckdatei->id_dienstleister);
                    if (!empty($zugang_tmp->mail) && $result[$druckdatei->id_job]) {
                        $mailadressen = explode(";", $zugang_tmp->mail);
                        if (!empty($mailadressen)) {
                            foreach ($mailadressen as $mailadresse) {
                                $mail_tmp = explode("|", $mailadresse);
                                $name = explode(" ", $mail_tmp[1]);
                                $userfetch = new User();
                                $userfetch->email = (!empty($mail_tmp[0]) ? $mail_tmp[0] : '');
                                $userfetch->vorname = (!empty($name[0]) ? $name[0] : '');
                                $userfetch->name = (!empty($name[1]) ? $name[1] : '');

                                $mailerPayload = new MailerPayload(
                                    $pvalue['id_job'],
                                    $pvalue['jobbezeichnung'],
                                    $dienstleister['dienstleister'],
                                    $pvalue['datensaetze'],
                                    $pvalue['step_id'],
                                    config('app.APP_URL') . 'freigabe/' . $pvalue['id_job']
                                );
                                $mailer = new Mailer($userfetch, 'EMAIL_DATENUEBERGABE_AN_DRUCKER', $mailerPayload);
                                $mailer->execute();
                            }
                        }
                        $jobuser = Jobbenutzer::where('id_job', $job->id)->get();
                        foreach ($jobuser as $juser) {
                            $userfetch = User::find($juser->id_benutzer);

                            if (!empty($userfetch) && $userfetch->is_kunde() && ($juser->freigabe == 1) && ($juser->benachrichtigung == 1)) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    $pvalue['id_job'],
                                    $pvalue['jobbezeichnung'],
                                    $dienstleister['dienstleister'],
                                    $pvalue['datensaetze'],
                                    $pvalue['step_id'],
                                    config('app.APP_URL') . 'freigabe/' . $pvalue['id_job']
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_DATENUEBERGABE_AN_DRUCK_INFOKUNDE', $mailerPayload);
                                $mailer->execute();
                            }

                            if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->freigabe == 1) && ($juser->benachrichtigung == 1)) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    $pvalue['id_job'],
                                    $pvalue['jobbezeichnung'],
                                    $dienstleister['dienstleister'],
                                    $pvalue['datensaetze'],
                                    $pvalue['step_id'],
                                    config('app.APP_URL') . 'freigabe/' . $pvalue['id_job']
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_DATENUEBERGABE_AN_DRUCKER', $mailerPayload);
                                $mailer->execute();
                            }
                        }
                    }
                }
            }
        }
    }

    public static function ausliefern_pwl($relPath = '')
    {
        $pwls = PWLs::Where('uebergabe_dienstleister', null)->get();
        $result = false;
        $zugangsdaten_last = "";
        $zugang = [];
        foreach ($pwls as $pwl) {
            $zugangsdaten = Ftpdienstleister::Where('id_job', $pwl->id_job)
                ->Where('id_dienstleister', $pwl->id_dienstleister)
                ->get()
                ->first();
            $job = Job::find($pwl->id_job);
            if ($job) {
                $projektinfo[$pwl->id_job][0] = [
                    'bezeichnung' => 'ProjektID',
                    'value' => $pwl->id_job
                ];
                $projektinfo[$pwl->id_job][1] = [
                    'bezeichnung' => 'Projektbezeichnung',
                    'value' => $job->jobbezeichnung
                ];
                $projektinfo[$pwl->id_job][2] = ['bezeichnung' => 'Name der PWL-Datei', 'value' => $pwl->file_name];
                $zugang[$pwl->id_job] = $zugangsdaten;
            }
            if (empty($zugangsdaten)) {
                continue;
            }

            $adapter = new SftpClient(
                $zugangsdaten->host,
                $zugangsdaten->port,
                $zugangsdaten->user,
                $zugangsdaten->pass,
                !empty($zugangsdaten->auth_file) ? $relPath . $zugangsdaten->auth_file : null,
                $zugangsdaten->root . 'Eingang/');

            if (!empty($zugangsdaten->auth_file)) {
                if (!is_readable($relPath . $zugangsdaten->auth_file)) {
                    Log::error('Auth File: ' . realpath($relPath . $zugangsdaten->auth_file) . ' is not readable.');
                }
            }

            if ($zugangsdaten_last != $zugangsdaten->host . $zugangsdaten->user) {
                $filesystem = $adapter->connect();
                Log::debug('Connection to new host: ' . $zugangsdaten->host . '');
            }
            $zugangsdaten_last = $zugangsdaten->host . $zugangsdaten->user;


            $exists = $filesystem->fileExists($pwl->id_job);
            if (!$exists) {
                $filesystem->createDirectory($pwl->id_job, new Config());
            }

            if (empty($pwl->file)) {
                Log::debug('ProjektID #' . $pwl->id_job . ': skipping empty PWL-file ' . $pwl->file_name . '.');
                continue;
            }

            $result = $filesystem->write($pwl->id_job . '/' . $pwl->file_name, FileController::file_entschluesseln($pwl->file), new Config());
            if (empty($result)) {
                $update_druckdatei = PWLs::find($pwl->id);
                $update_druckdatei->uebergabe_dienstleister = now();
                $update_druckdatei->save();
                return true;
            }
        }
        return false;
    }

    /**
     * Display the specified resource.
     *
     * @param int $id_weddingdoc
     * @return \Illuminate\Http\Response
     */
    public static function response_dienstleister()
    {
        $result = false;
        $return = "";
        $zugangsdatenall = Ftpdienstleister::Where('aktiv', 1)->get();
        $zugangsdaten_last = "";
        foreach ($zugangsdatenall as $zugangsdaten) {
            $job_exists = Job::find($zugangsdaten->id_job);
            if (empty($job_exists) || empty($zugangsdaten)) {
                continue;
            }

            Log::debug('Starting response transfers for id_job=' . $zugangsdaten->id_job . ' with auth file ' . $zugangsdaten->auth_file . '.');

            if ($zugangsdaten->auth_file != '' && !file_exists($zugangsdaten->auth_file)) {
                Log::error("Error with response transfer job id_job=" . $zugangsdaten->id_job . ' and CWD:"' . getcwd() . '". File ' . $zugangsdaten->auth_file . " not found.");
                continue;
            }

            $sftp = new SftpConnectionProvider(
                host: $zugangsdaten->host,
                username: $zugangsdaten->user,
                password: $zugangsdaten->pass,
                privateKey: $zugangsdaten->auth_file != '' ? $zugangsdaten->auth_file : null,
                timeout: 30
            );

            $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);

            if ($zugangsdaten_last != $zugangsdaten->host . $zugangsdaten->user) {
                $filesystem = new Filesystem(new SftpAdapter($sftp, $zugangsdaten->root . 'Ausgang/'));
                Log::debug('Connection to new host: ' . $zugangsdaten->host . '');

                $zugangsdaten_last = $zugangsdaten->host . $zugangsdaten->user;

                $files = self::load_files($filesystem);
            }


            Log::debug('Starting traversing response files (id_job=' . $zugangsdaten->id_job . ').');
            foreach ($files as $fkey => $file) {
                $tmp_basename = explode('_', $file['basename']);
                $mailinggroup = "";
                $tmp_path = explode('/', $file['path']);
                $step_id = '';
                $check_jobid = null;
                if (!empty($tmp_path[0]) && is_numeric($tmp_path[0])) {
                    $check_jobid = $tmp_path[0];
                }
                if (strpos($tmp_basename[0], 'EINGELESEN') !== false || strpos($tmp_basename[0], 'FEHLER') !== false || empty($file['extension'])) {
                    unset($files[$fkey]);
                    continue;
                }
                if (count($tmp_basename) >= 3 && !empty($check_jobid) && $check_jobid != intval($tmp_basename[2])) {
                    $job = Job::find($check_jobid);
                    Log::info("FEHLER: " . $file['basename'] . ' in Path: ' . $check_jobid);;
                    $mailadressen = explode(";", $zugangsdaten->mail);
                    if (!empty($mailadressen)) {
                        foreach ($mailadressen as $mailadresse) {
                            $mail_tmp = explode("|", $mailadresse);

                            // ÜBERGABE AN DRUCKKDIOENSTLEISTER
                            $name = explode(" ", $mail_tmp[1]);
                            $maildata = [
                                'email' => (!empty($mail_tmp[0]) ? $mail_tmp[0] : ''),
                                'vorname' => (!empty($name[0]) ? $name[0] : ''),
                                'name' => (!empty($name[1]) ? $name[1] : ''),
                                'projekt_id' => '#' . (!empty($job) ? $job->id : '-'),
                                'projektname' => (!empty($job) ? $job->jobbezeichnung : '-'),
                                'stepid' => '-', 'dabeibezeichnung' => $file['basename'],
                                'ordnerbezeichnung' => $check_jobid
                            ];
                            Mailer::sendMailByTemplate('EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_DDL', $maildata);
                        }
                    }
                    if (!empty($job)) {
                        $jobuser = Jobbenutzer::where('id_job', $job->id)->get();
                    } else {
                        $jobuser = [];
                    }

                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);

                        if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                            $mailerPayload = new MailerPayload(
                                id: $job->id,
                                projektname: $job->jobbezeichnung,
                                dienstleister: $dienstleister->dienstleister,
                                link: config('app.APP_URL') . 'freigabe/' . $job->id,
                                ordnerbezeichnung: $file['basename'],
                                aktion: $check_jobid
                            );
                            $mailer = new Mailer($userdata, 'EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_TGD', $mailerPayload);
                            $mailer->execute();
                        }
                    }
                    if ($filesystem->has($file['path'] . '/' . $file['basename'])) {
                        $filesystem->move($file['path'] . '/' . $file['basename'], $file['path'] . '/' . "FEHLER_" . $file['basename']);
                    }
                }

                $id_job = 0;
                $mailinggroup = "agentur|kunde";
                $basename_lower = strtolower($tmp_basename[0]);
                if (($basename_lower == 'echtandruck' || $basename_lower == 'einlieferungslisten') && !empty($file['extension'])) {
                    $id_job = str_replace('#', '', $tmp_basename[1]);
                    $tmp_stepid = explode('.', $tmp_basename[2]);
                    $step_id = $tmp_stepid[0];
                    $mailinggroup = "agentur";
                } else if (($basename_lower == 'response' || strpos(strtolower($file['basename']), '_nok_') !== false) && !empty($file['extension'])) {
                    $id_job = str_replace('#', '', $tmp_basename[2]);
                } else if (!empty($file['path']) && is_numeric($tmp_path[0]) && !empty($file['extension']) && ($file['extension'] == 'pdf' || $file['extension'] == 'csv')) {
                    $id_job = $file['path'];
                }

                if ($file['timestamp'] >= time() - 60) {
                    unset($files[$fkey]);
                    continue;
                }

                if (strpos(strtolower($file['path']), '_ok_') !== false && (substr_count($file['basename'], '_') == 1) && (is_numeric($tmp_basename[0])) && is_numeric(str_replace('.pdf', '', $tmp_basename[1]))) {

                    $job = Job::find($id_job);
                    $content = stream_get_contents($filesystem->readStream($file['path'] . '/' . $file['basename']));
                    $response = new Einzelresponses();
                    $response->id_job = $id_job;
                    $response->vertragskonto = $tmp_basename[0];
                    $response->file_name = $file['basename'];
                    $response->timestamp = $file['timestamp'];
                    $response->dateigroesse = strlen($content);
                    $response->file = FileController::file_verschluesseln($content);
                    $result = $response->save();
                    Log::debug('Read (_ok_) to DB from DDL: ' . $file['basename']);
                    if ($result) {

                        $transmit = new Transmits();
                        $transmit->id_datei = $response->id;
                        $transmit->id_liefertyp = 2;
                        $transmit->save();

                        $filesystem->move($file['path'] . '/' . $file['basename'], $file['path'] . '/' . "EINGELESEN_" . $file['basename']);
                        if (stripos($mailinggroup, 'agentur')) {
                            $jobuser = Jobbenutzer::where('id_job', $id_job)->get();

                            foreach ($jobuser as $juser) {
                                $userfetch = User::find($juser->id_benutzer);

                                if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                    $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);
                                    $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'projekt_id' => '#' . $job->id, 'projektname' => $job->jobbezeichnung, 'dienstleister' => $dienstleister->dienstleister, 'link' => config('app.APP_URL') . 'freigabe/' . $job->id);
                                }
                            }
                        }
                    }
                } else if ($id_job != 0 && ($file['extension'] == 'zip' || $file['extension'] == 'csv')) {
                    $job = Job::find($id_job);
                    $content = stream_get_contents($filesystem->readStream($file['path'] . '/' . $file['basename']));
                    $response = new Responses();
                    $response->id_job = $id_job;
                    $response->step_id = $step_id;
                    $response->file_name = $file['basename'];
                    $response->timestamp = $file['timestamp'];
                    $response->dateigroesse = strlen($content);
                    $response->file = FileController::file_verschluesseln($content);
                    $result = $response->save();
                    Log::debug('Read (_nok_) to DB from DDL: ' . $file['basename']);
                    if ($result) {
                        $filesystem->move($file['path'] . '/' . $file['basename'], $file['path'] . '/' . "EINGELESEN_" . $file['basename']);
                        $jobuser = Jobbenutzer::where('id_job', $id_job)->get();
                        foreach ($jobuser as $juser) {
                            $userfetch = User::find($juser->id_benutzer);
                            $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);
                            if (!empty($userfetch) && $userfetch->is_kunde() && ($juser->benachrichtigung == 1)) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    id: $job->id,
                                    projektname: $job->jobbezeichnung,
                                    dienstleister: $dienstleister->dienstleister,
                                    link: config('app.APP_URL') . 'freigabe/' . $job->id
                                );
                                if ((strtolower($tmp_basename[0]) == 'response')) {
                                    $mailer = new Mailer($userdata, 'EMAIL_RESPONSE1_KUNDE', $mailerPayload);
                                } elseif (strpos(strtolower($file['basename']), '_nok_') !== false) {
                                    $mailer = new Mailer($userdata, 'EMAIL_RESPONSE2_KUNDE', $mailerPayload);
                                }
                                $mailer->execute();
                            }
                            if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    id: $job->id,
                                    projektname: $job->jobbezeichnung,
                                    dienstleister: $dienstleister->dienstleister,
                                    link: config('app.APP_URL') . 'freigabe/' . $job->id
                                );
                                if ((strtolower($tmp_basename[0]) == 'response')) {
                                    $mailer = new Mailer($userdata, 'EMAIL_RESPONSE1_TGD', $mailerPayload);
                                } elseif (strpos(strtolower($file['basename']), '_nok_') !== false) {
                                    $mailer = new Mailer($userdata, 'EMAIL_RESPONSE2_TGD', $mailerPayload);
                                }
                                $mailer->execute();
                            }
                        }
                    }
                } elseif ($file['extension'] == 'zip' && stripos($file['path'], 'sonstiges') !== false) {
                    if ($filesystem->has($file['path'] . '/' . "EINGELESEN_" . $file['basename'])) {
                        Log::error("response_dienstleister: Target file " . $file['path'] . '/' . "EINGELESEN_" . $file['basename'] . "does exist already.");
                        return false;
                    }

                    $content = stream_get_contents($filesystem->readStream($file['path'] . '/' . $file['basename']));
                    $response = new SonstigeResponses();
                    $response->id_job = $id_job;
                    $response->file_name = $file['basename'];
                    $response->timestamp = $file['timestamp'];
                    $response->dateigroesse = strlen($content);
                    $response->file = FileController::file_verschluesseln($content);
                    $result = $response->save();
                    Log::debug('Read (_sonstige_) to DB from DDL: ' . $file['basename']);
                    if ($result) {
                        $filesystem->move($file['path'] . '/' . $file['basename'], $file['path'] . '/' . "EINGELESEN_" . $file['basename']);
                        $user = User::where('aktiv', 1)->get();
                        foreach ($user as $juser) {
                            $userfetch = User::find($juser->id);
                            $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);
                            if (!empty($userfetch) && $userfetch->is_kunde()) {
                                $kuser = Jobbenutzer::Where('id_benutzer', $juser->id)->Where('download', 1)->first();
                                if (!empty($kuser)) {
                                    $userdata = User::find($juser->id, ['id', 'email', 'name', 'vorname']);
                                    $mailerPayload = new MailerPayload(
                                        id: (!empty($job) ? $job->id : '-'),
                                        projektname: (!empty($job) ? $job->jobbezeichnung : '-'),
                                        dienstleister: $dienstleister->dienstleister,
                                        link: config('app.APP_URL') . 'projekte'
                                    );
                                    $mailer = new Mailer($userdata, 'EMAIL_RESPONSE3_KUNDE', $mailerPayload);
                                    $mailer->execute();
                                }
                            }

                            if (!empty($userfetch) && $userfetch->is_agentur()) {
                                $userdata = User::find($juser->id, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    id: (!empty($job) ? $job->id : '-'),
                                    projektname: (!empty($job) ? $job->jobbezeichnung : '-'),
                                    dienstleister: $dienstleister->dienstleister,
                                    link: config('app.APP_URL') . 'projekte'
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_RESPONSE3_TGD', $mailerPayload);
                                $mailer->execute();
                            }
                        }
                    }
                } else {
                    Log::info('Response no matching found: ' . $file['path'] . '/' . $file['basename']);
                    if ($filesystem->has($file['path'] . '/' . $file['basename'])) {
                        $filesystem->move($file['path'] . '/' . $file['basename'], $file['path'] . '/' . "FEHLER_" . $file['basename']);
                        $mailadressen = explode(";", $zugangsdaten->mail);
                    }
                    $job = Job::find($check_jobid);

                    if (!empty($mailadressen)) {
                        foreach ($mailadressen as $mailadresse) {
                            $mail_tmp = explode("|", $mailadresse);
                            // ÜBERGABE AN DRUCKKDIOENSTLEISTER
                            $name = explode(" ", $mail_tmp[1]);

                            $additional_subject = "";
                            if (!empty($job->id) && !empty($job->jobbezeichnung)) {
                                $additional_subject = "im " . $job->id . " - " . $job->jobbezeichnung;
                            }

                            $maildata = array('email' => (!empty($mail_tmp[0]) ? $mail_tmp[0] : ''), 'vorname' => (!empty($name[0]) ? $name[0] : ''), 'name' => (!empty($name[1]) ? $name[1] : ''), 'projekt_id' => '#' . (!empty($job->id) ? $job->id : ''), 'projektname' => (!empty($job->jobbezeichnung) ? $job->jobbezeichnung : ''), 'stepid' => '-', 'dabeibezeichnung' => $file['basename'], 'ordnerbezeichnung' => $file['path'], 'betreffzeile' => $additional_subject);
                            Mailer::sendMailByTemplate('TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_DDL', $maildata);
                        }
                    }

                    if (!empty($job->id)) {
                        $jobuser = Jobbenutzer::where('id_job', $job->id)->get();
                    } else {
                        $jobuser = Jobbenutzer::where('id_job', $check_jobid)->get();
                    }

                    if (!empty($jobuser)) {
                        $jobuser = BenutzerA::where('id_ersteller', '1')->get();
                    }

                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);

                        if (!empty($userfetch) && $userfetch->is_agentur() && $userfetch->aktiv == 1) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);

                            $additional_subject = "";
                            if (!empty($job->id) && !empty($job->jobbezeichnung)) {
                                $additional_subject = "im " . $job->id . " - " . $job->jobbezeichnung;
                            }

                            $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'projekt_id' => '#' . (!empty($job->id) ? $job->id : ''), 'projektname' => (!empty($job->jobbezeichnung) ? $job->jobbezeichnung : ''), 'stepid' => '-', 'dabeibezeichnung' => $file['basename'], 'ordnerbezeichnung' => $file['path'], 'betreffzeile' => $additional_subject);
                            Mailer::sendMailByTemplate('TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_TGD', $maildata);
                        }
                    }
                }


                unset($files[$fkey]);
            }
        }


        return $return;
    }

    private static function load_files($filesystem)
    {
        $sftpfiles_tmp = $filesystem->listContents('./');
        foreach ($sftpfiles_tmp as $sftpfile_tmp) {
            if ($sftpfile_tmp["type"] == 'dir') {
                if (!empty($sftpfile_tmp['path'])) {
                    $sftpfiles[$sftpfile_tmp['path']] = $filesystem->listContents('./' . $sftpfile_tmp['path'] . '/');
                }
            }
        }

        $files = [];

        if (!empty($sftpfiles)) {
            foreach ($sftpfiles as $path => $ftpdirs) {

                foreach ($ftpdirs as $ftpfiles) {
                    if ($ftpfiles['type'] == 'dir') {
                        $ftpdirs2 = $filesystem->listContents('./' . $ftpfiles['path'] . '/');
                        foreach ($ftpdirs2 as $ftpfiles2) {
                            if (!empty($ftpfiles2['fileSize']) && $ftpfiles2['fileSize'] > 0) {
                                $files[] = [
                                    'basename' => substr($ftpfiles2['path'], strrpos($ftpfiles2['path'], '/') + 1),
                                    'path' => substr($ftpfiles2['path'], 0, strrpos($ftpfiles2['path'], '/')),
                                    'timestamp' => $ftpfiles2['lastModified'],
                                    'extension' => (!empty(substr($ftpfiles2['path'], -3)) ? substr($ftpfiles2['path'], -3) : '')

                                ];
                            }

                        }
                    } else {
                        $files[] = [
                            'basename' => substr($ftpfiles['path'], strrpos($ftpfiles['path'], '/') + 1),
                            'path' => $path,
                            'timestamp' => $ftpfiles['lastModified'],
                            'extension' => (!empty(substr($ftpfiles['path'], -3)) ? substr($ftpfiles['path'], -3) : '')
                        ];
                    }
                }
            }
        }

        return $files;
    }


    public static function check_dl_ftp()
    {
        $return = "";
        $zugangsdatenall = Ftpdienstleister::Where('aktiv', 1)->get();
        $zugangsdaten_last = "";
        $files = [];
        $files_tmp = [];
        foreach ($zugangsdatenall as $zugangsdaten) {
            $job_exists = Job::find($zugangsdaten->id_job);
            if (empty($job_exists) || empty($zugangsdaten)) {
                continue;
            }

            $sftp = new SftpConnectionProvider(
                host: $zugangsdaten->host,
                username: $zugangsdaten->user,
                password: $zugangsdaten->pass,
                privateKey: ($zugangsdaten->auth_file != '' ? $zugangsdaten->auth_file : null),
                timeout: 30
            );

            if ($zugangsdaten_last != $zugangsdaten->host . $zugangsdaten->user) {
                $filesystem = new Filesystem(new SftpAdapter($sftp, $zugangsdaten->root . 'Ausgang/'));
                $files = [];
                Log::debug('Connection to new host: ' . $zugangsdaten->host . '');
            }

            $zugangsdaten_last = $zugangsdaten->host . $zugangsdaten->user;

            $sftpfiles_tmp = $filesystem->listContents('./');
            $sftpfiles = [];
            foreach ($sftpfiles_tmp as $sftpfile_tmp) {
                if ($sftpfile_tmp["type"] == 'dir') {
                    if (!empty($sftpfile_tmp['path'])) {
                        $sftpfiles[$sftpfile_tmp['path']] = $filesystem->listContents('./' . $sftpfile_tmp['path'] . '/');
                    }
                }
            }
            $files = [];
            foreach ($sftpfiles as $ftpdirs) {
                foreach ($ftpdirs as $ftpfiles) {
                    if ($ftpfiles['type'] == 'dir') {
                        $ftpdirs2 = $filesystem->listContents('./' . $ftpfiles['dirname'] . '/' . $ftpfiles['basename'] . '/');

                        foreach ($ftpdirs2 as $ftpfiles2) {
                            $files[] = array(
                                'basename' => $ftpfiles2['basename'],
                                'path' => $ftpfiles2['dirname'],
                                'timestamp' => $ftpfiles2['timestamp'],
                                'extension' => (!empty($ftpfiles2['extension']) ? $ftpfiles2['extension'] : '')

                            );
                        }
                    } else {
                        $files[] = array(
                            'basename' => $ftpfiles['basename'],
                            'path' => $ftpfiles['dirname'],
                            'timestamp' => $ftpfiles['timestamp'],
                            'extension' => (!empty($ftpfiles['extension']) ? $ftpfiles['extension'] : '')

                        );
                    }
                }
            }


            if (!empty($files) && $files != $files_tmp) {
                foreach ($files as $file) {
                    $tmp_basename = explode('_', $file['basename']);
                    $tmp_path = explode('/', $file['path']);
                    $id_job = $tmp_path[0];

                    if (strpos($tmp_basename[0], 'FEHLER') !== false) {
                        Log::debug('Marked file:  ' . $file['path'] . '/' . $file['basename'] . '');
                        $jobuser = Jobbenutzer::where('id_job', $id_job)->get();

                        foreach ($jobuser as $juser) {
                            $userfetch = User::find($juser->id_benutzer);
                            $dienstleister = Dienstleister::find($zugangsdaten->id_dienstleister);

                            if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);

                                $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'dienstleister' => $dienstleister->dienstleister, 'dateibezeichnung' => $file['basename']);

                                Mailer::sendMailByTemplate('EMAIL_DRUCKFTP_FEHLER_TGD', $maildata);
                            }
                        }
                    }
                }
            }
            $files_tmp = $files;
        }


        return $return;
    }

    public static function getPrintStatus()
    {
        $return = "";
        $zugangsdaten = json_decode(config('connections.PRINT_STATUS_FTP'));
        $sftp = new SftpConnectionProvider(
            host: $zugangsdaten->host,
            username: $zugangsdaten->user,
            password: $zugangsdaten->pass,
            port: $zugangsdaten->port,
            privateKey: ($zugangsdaten->auth_file != '' ? $zugangsdaten->auth_file : null),
            timeout: 30
        );
        $filesystem = new Filesystem(new SftpAdapter($sftp, $zugangsdaten->root));
        Log::debug('Connection PrintStatus to host: ' . $zugangsdaten->host);
        $sftpfiles_tmp = $filesystem->listContents('./in');
        $sftpfiles = [];
        foreach ($sftpfiles_tmp as $sftpfile_tmp) {
            if ($sftpfile_tmp["type"] == 'file') {
                $sftpfiles[$sftpfile_tmp['path']] = $sftpfile_tmp;
            }
        }
        foreach ($sftpfiles as $sftpfile) {
            Log::debug('Parse file (PrintStatus): ' . $sftpfile['path']);
            $content = $filesystem->read($sftpfile['path']);
            $result = Storage::disk('printStatus')->put($sftpfile['path'], $content);
            $xmlObject = simplexml_load_string($content);
            $json = json_encode($xmlObject);
            $phpArray = json_decode($json, true);
            $dbresult = false;
            $noJobId = false;
            $doublication = false;
            foreach ($phpArray['RESULTSET']['ROW'] as $row) {
                if (!empty($row) && !empty($row['_5300_5004_termine_tego_jobid'])) {
                    $found_dataset = Printstatus::where('id_job', $row['_5300_5004_termine_tego_jobid'])->where('step_id', $row['_5300_5005_termine_tego_stepid'])->where('pp_mdate', Carbon::parse(str_replace('/', ' ', $row['_5300_1004_termine_printplus_mdate']))->format('Y-m-d H:i:s'))->where('pp_status', $row['_5300_1007_termine_printplus_status'])->first();
                    Log::debug($found_dataset);
                    if (empty($found_dataset) && $row['_5300_5004_termine_tego_jobid']) {
                        $printstatus = new Printstatus();
                        if (!empty($row['_5300_5004_termine_tego_jobid'])) $printstatus->id_job = $row['_5300_5004_termine_tego_jobid'];
                        if (!empty($row['_5300_5005_termine_tego_stepid'])) $printstatus->step_id = $row['_5300_5005_termine_tego_stepid'];
                        if (!empty($row['_5300_1001_termine_printplus_bezeichnung'])) $printstatus->pp_bezeichnung = $row['_5300_1001_termine_printplus_bezeichnung'];
                        if (!empty($row['_5300_1002_termine_printplus_datum'])) $printstatus->pp_datum = Carbon::parse($row['_5300_1002_termine_printplus_datum'])->format('Y-m-d H:i:s');
                        if (!empty($row['_5300_1003_termine_printplus_cdate'])) $printstatus->pp_cdate = $row['_5300_1003_termine_printplus_cdate'];
                        if (!empty($row['_5300_1004_termine_printplus_mdate'])) $printstatus->pp_mdate = $row['_5300_1004_termine_printplus_mdate'];
                        if (!empty($row['_5300_1005_termine_printplus_id'])) $printstatus->pp_id = $row['_5300_1005_termine_printplus_id'];
                        if (!empty($row['_5300_1006_termine_printplus_terminstelle_id'])) $printstatus->pp_terminstelle_id = $row['_5300_1006_termine_printplus_terminstelle_id'];
                        if (!empty($row['_5300_1007_termine_printplus_status'])) $printstatus->pp_status = $row['_5300_1007_termine_printplus_status'];
                        if (!empty($row['_5300_1008_termine_printplus_deleted'])) $printstatus->pp_deleted = $row['_5300_1008_termine_printplus_deleted'];
                        if (!empty($row['_5300_1009_termine_printplus_aupa_id'])) $printstatus->pp_aupa_id = $row['_5300_1009_termine_printplus_aupa_id'];
                        if (!empty($row['_5300_1010_termine_printplus_ord_id'])) $printstatus->pp_ord_id = $row['_5300_1010_termine_printplus_ord_id'];
                        if (!empty($row['_5300_1201_termine_job_pnummer_id'])) $printstatus->job_pnummer_id = $row['_5300_1201_termine_job_pnummer_id'];
                        if (!empty($row['_5300_1204_termine_job_bez'])) $printstatus->job_bez = $row['_5300_1204_termine_job_bez'];
                        if (!empty($row['_5300_1205_termine_job_kurz4'])) $printstatus->job_kurz4 = $row['_5300_1205_termine_job_kurz4'];
                        if (!empty($row['_5300_1206_termine_job_sabe'])) $printstatus->job_sabe = $row['_5300_1206_termine_job_sabe'];
                        if (
                            !empty($printstatus->id_job) &&
                            !empty($printstatus->step_id) &&
                            !empty($printstatus->pp_datum) &&
                            !empty($printstatus->pp_cdate) &&
                            !empty($printstatus->pp_mdate) &&
                            !empty($printstatus->pp_terminstelle_id) &&
                            !empty($printstatus->pp_status)
                        ) {
                            $printstatus->success = 1;
                            Log::info('Parse success: data OK!');
                        } else {
                            Log::error('Parse error [print-status]: JobID #' . (!empty($printstatus->id_job) ? $printstatus->id_job : '-') . 'StepID ' . (!empty($printstatus->step_id) ? $printstatus->step_id : '-'));
                        }
                        $dbresult = $printstatus->save();
                    } else {
                        if (!empty($found_dataset)) {
                            Log::warning('Parse warning: data exists ');
                            Log::warning($found_dataset);
                            $doublication = true;
                        }
                    }
                } else {
                    $noJobId = true;
                }
            }
            if ($result && $dbresult) {
                $filesystem->move($sftpfile['path'], './done/EINGELESEN_' . basename($sftpfile['path']));
                Log::debug('Successfull inserted: ' . $sftpfile['path']);
            } else if ($noJobId) {
                $filesystem->move($sftpfile['path'], './done/NOJOBID_' . basename($sftpfile['path']));
                Log::error('No JobID found (PrintStatus): ' . $sftpfile['path']);
            } else if ($doublication) {
                $filesystem->move($sftpfile['path'], './done/DOUBLICATION_' . basename($sftpfile['path']));
                Log::error('Allready read (PrintStatus): ' . $sftpfile['path']);
            } else {
                $filesystem->move($sftpfile['path'], './done/FEHLER_' . basename($sftpfile['path']));
                Log::error('Could not be parsed (PrintStatus): ' . $sftpfile['path']);
            }
        }
        return true;
    }


    private static function upload($loginData, $druckdatei): bool
    {
        Log::Debug("Starting with FTPController:upload for id_job " . $loginData->id_job);

        $adapter = new SftpClient($loginData->host, $loginData->port, $loginData->user, $loginData->pass, $loginData->auth_file, $loginData->root . 'Eingang/');
        $filesystem = $adapter->connect();
        $exists = $filesystem->fileExists($druckdatei->id_job);
        if (!$exists) {
            $filesystem->createDirectory($druckdatei->id_job, new Config());
        }
        $result = $filesystem->write($druckdatei->id_job . '/' . $druckdatei->file_name, FileController::file_entschluesseln($druckdatei->file), new Config());
        if (empty($result)) {
            return true;
        } else {
            return false;
        }
    }
}
