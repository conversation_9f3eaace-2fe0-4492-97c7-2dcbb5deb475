<?php

namespace App\Http\Controllers;

use App\Exceptions\SalesforceApiException;
use App\ModelHelper\JobHelper;
use App\ModelHelper\Mailer;
use App\ModelHelper\SftpHelper;
use App\Models\Aktivitaet;
use App\Models\Datei;
use App\Models\Document;
use App\Models\Dokument2Jobs;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\User;
use App\Models\Weddingmaker;
use App\Models\Editordocument;
use Carbon\Carbon;
use Carbon\Exceptions\Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use App\Jobs\checkContent;

class SalesforceController extends Controller
{
    /** @var User */
    private $user;
    /** @var Job */
    private $job;

    /**
     * Endpunkt zum Überprüfen des Tokens
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checktoken(Request $request)
    {
        try {
            $this->setUpUser($request->bearerToken());
            Log::info('api/salesforce/checktoken success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('api/salesforce/checktoken Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('api/salesforce/checktoken Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }

        return response()->json('true', 200);
    }

    /**
     * Endpunkt zur Übersicht der Projekte
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function projectlist(Request $request)
    {
        try {
            $this->setUpUser($request->bearerToken());
            Log::info('api/salesforce/project/list success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('api/salesforce/project/list Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('api/salesforce/project/list Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }

        return response()->json(array_map(
            function ($array) {
                return $array;
            },
            $this->user->freigegebeneJobs(['id_job', 'jobnummer', 'jobbezeichnung'])->toArray()
        ), 200);
    }

    /**
     * Endpunkt für die Vorschau eines Projekts
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function preview(Request $request, $id)
    {
        try {
            $this->setUpUser($request->bearerToken());
            $this->setUpJob($id);
            Log::info('api/salesforce/' . (!empty($id) ? $id : '-') . '/preview success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/preview Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/preview Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }

        return response()->json(array_map(
            function ($array) {
                $array['sf_preview'] = base64_encode($array['sf_preview']); //PDF Binary
                return $array;
            },
            $this->user->jobs()->where('id_job', intval($id))->get([
                'id_job',
                'jobnummer',
                'jobbezeichnung',
                'jobende',
                'sf_preview'
            ])->toArray()
        ), 200);
    }

    /**
     * Endpunkt für die Vorschau eines Projekts
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkdata(Request $request, $id)
    {
        try {
            $this->setUpUser($request->bearerToken());
            $this->setUpJob($id);
            Log::info('api/salesforce/' . (!empty($id) ? $id : '-') . '/checkdata success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/checkdata Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/checkdata Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }

        $data = [];

        $checkData = Document::getCheckdata($id)->first();
        if (!empty($checkData)) {
            $data = [
                'id_job' => $id,
                'jobbezeichnung' => $this->job->jobbezeichnung,
                'filename' => $checkData->name,
                'checkdata' => base64_encode($checkData->inhalt)

            ];
        }
        if (!empty($data)) {
            return response()->json($data, 200);
        } else {
            return response()->json(['error' => 'No checkdata found'], 404);
        }

    }


    /**
     * Endpunkt für die verwendeten Variablen
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsedVars(Request $request, $id)
    {
        try {
            $this->setUpUser($request->bearerToken());
            $this->setUpJob($id);
            Log::info('api/salesforce/' . (!empty($id) ? $id : '-') . '/get/usedvars success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/get/usedvars Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('api/salesforce/' . (!empty($id) ? $id : '-') . '/get/usedvars Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']');
            }
            return response()->json($e->getMessage(), 401);
        }
        $editorDoc = Editordocument::getLastFrontifyVersion($id);
        if(!empty($editorDoc)){
            if(!empty($editorDoc['used_vars'])){
                $usedVars = json_decode($editorDoc['used_vars']);

                if (!in_array('VERSANDDATUM', $usedVars)) {
                    $usedVars[] = 'VERSANDDATUM';
                }

                sort($usedVars);
                return response()->json($usedVars, 200);
            }
        }
        $weddingDoc = Dokument2Jobs::getWeddingDocument($id);
        if (!empty($weddingDoc)) {
            $idDoc = $weddingDoc['id'];
            $weddingmaker = Weddingmaker::Where('id_dokument', $idDoc)->get('spalten');
            $usedVars = [];
            foreach ($weddingmaker as $values) {
                $tmpVars = explode(';', $values['spalten']);
                foreach ($tmpVars as $value) {
                    if ($value != '_#_' && trim($value) != '' && !in_array($value, $usedVars)) {
                        $usedVars[] = $value;
                    }
                }
            }
            if (!in_array('VERSANDDATUM', $usedVars)) {
                $usedVars[] = 'VERSANDDATUM';
            }

            sort($usedVars);
            return response()->json($usedVars, 200);
        } else {
            return response()->json('Vars not found', 404);
        }

    }

    /**
     * Enpunkt zum Import von Adressdaten per Payload per JSON
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function importJson(Request $request, $id)
    {
        $payload = json_decode($request->getContent());
        if (!isset($payload->file_name) or !isset($payload->file_content)) {
            return response()->json('JSON payload error2', 400);
        }
        if (!isset($payload->step_id)) {
            return response()->json('Missing Step ID', 400);
        }
        return $this->import(
            $request,
            $id,
            $payload->file_name,
            base64_decode($payload->file_content),
            $payload->step_id
        );
    }

    /**
     * Enpunkt zum Import einer CSV von einem SFTP Server
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws SalesforceApiException
     */
    public function importCsv(Request $request, $id)
    {
        $payload = json_decode($request->getContent());
        if (!isset($payload->file_name) or !isset($request->path)) {
            return response()->json('JSON payload/path error', 400);
        }
        if (!isset($payload->step_id)) {
            return response()->json('Missing Step ID', 400);
        }

        $credentials = SftpHelper::getCredentialsById($id);
        if (!$credentials) {
            return response()->json('missing credentials for sftp', 400);
        }
        $getFile = SftpHelper::getFileContentFromSftp(
            $credentials->host,
            $credentials->port,
            $credentials->user,
            $credentials->pass,
            $payload->path . $payload->file_name,
            $credentials->auth_file
        );
        if ($getFile === "") {
            return response()->json('file not found on SFTP', 404);
        } else {
            return $this->import(
                $request,
                $id,
                $payload->file_name,
                $getFile,
                $payload->step_id
            );
        }

    }

    public function importResult(Request $request, $id, $activity_id)
    {
        try {
            Log::info('start check import for activityId: ' . $activity_id);
            $this->setUpUser($request->bearerToken());
            $this->setUpJob($id);

            Log::info('end check import for activityId: ' . $activity_id);
            //$count_datasets = $this->checkContent($content, $fileName, $id);
            $result = '';
            $message = '';
            $datasets = 0;

            if (!empty($activity_id)) {
                $activity = Aktivitaet::Where('id', $activity_id)->Where('id_job', $id)->first();
                if (!empty($activity)) {
                    $datei = Datei::find($activity->id_dateien);
                    if (!empty($datei)) {
                        if ($datei->checkContent === null) {
                            $result = 'pending';
                            $message = 'check not started';
                        } elseif ($datei->checkContent == 2) {
                            $result = 'pending';
                            $message = 'check is running';
                        } elseif ($datei->checkContent == 1) {
                            $result = 'success';
                            $message = 'file is ok';
                            $datasets = $datei->datensaetze;
                        } elseif ($datei->checkContent == 0) {
                            $result = 'error';
                            $message = $datei->checkError;
                        } else {
                            $result = 'error';
                            $message = 'unexpected error';
                        }
                    } else {
                        $result = 'error';
                        $message = 'file not found';
                    }
                } else {
                    $result = 'error';
                    $message = 'record not found';
                }


                return response()->json(array_map(
                    function ($array) {
                        return $array;
                    },
                    [
                        'result' => $result,
                        'message' => $message,
                        'importedDatasets' => $datasets,
                    ]
                ), 200);
            } else {
                return response()->json('no record found for activity_id: ' . $activity_id, 401);
            }
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('/salesforce/check/import/' . (!empty($activity_id) ? $activity_id : '-') . ' Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('/salesforce/check/import/' . (!empty($activity_id) ? $activity_id : '-') . ' Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . '] ');
            }
            return response()->json($e->getMessage(), 401);
        }
    }


    static function checkContent($contentEnc, $fileName, $id, $activity_id)
    {
        if (!empty($activity_id)) {
            $activity = Aktivitaet::find($activity_id);
            $datei = Datei::find($activity->id_dateien);
            $datei->checkContent = 2;
            $datei->save();
            Log::info('start writing content to array: ' . $fileName);
            $content = FileController::file_entschluesseln($contentEnc);
            $arrayContent = explode("\n", $content);
            $count_datasets = count($arrayContent) - 1;
            if (empty(end($arrayContent))) {
                $count_datasets -= 1;
            }
            Log::info('end writing content to array: ' . $fileName);
            Log::info('start encoding check: ' . $fileName);
            foreach ($arrayContent as $key => $value) {
                if (!empty($value)) {
                    $enc = mb_detect_encoding($value, mb_list_encodings(), true);
                    if ($enc != 'UTF-8') {
                        if (substr($value, 0, 3) !== chr(0xEF) . chr(0xBB) . chr(0xBF)) {
                            $datei->checkContent = 0;
                            $datei->checkError = 'Error in row #' . $key + 1;
                            $datei->stopzeit = Carbon::now()->format('Y-m-d H:i:s');
                            $datei->id_stop_user = $activity->id_benutzer;
                            $datei->save();
                            return false;
                        }

                    }
                }
            }
            Log::info('end encoding check: ' . $fileName);
            if ($count_datasets > 0) {
                $datei->datensaetze = $count_datasets;
                $datei->checkContent = 1;
                $datei->save();
                $jobuser = Jobbenutzer::where('id_job', $id)->get();
                $job = Job::find($id);
                foreach ($jobuser as $juser) {
                    $userinfo = User::find($juser->id_benutzer);
                    if (!empty($userinfo) && ($userinfo->is_agentur() || $userinfo->is_kunde()) && $juser->benachrichtigung == 1) {
                        $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                        $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'anzahl' => $count_datasets, 'name' => $userdata->name, 'projekt_id' => '#' . $job->id, 'projektname' => $job->jobbezeichnung, 'link' => config('app.APP_URL') . 'freigabe/' . $job->id);
                        Mailer::sendMailByTemplate('EMAIL_CRM_BELADUNG', $maildata);
                    }
                }
            }
        }
    }

    /**
     * Import
     *
     * @param Request $request
     * @param $id
     * @param string $fileName
     * @param string $content
     * @param $importId
     * @return \Illuminate\Http\JsonResponse
     */
    private
    function import(Request $request, $id, string $fileName, string $content, $importId)
    {
        try {
            Log::info('start import for job: ' . $id);
            $this->setUpUser($request->bearerToken());
            $this->setUpJob($id);

            $jobhelper_result = JobHelper::store(
                trim($fileName),
                trim($content),
                intval($this->job->id),
                $this->user,
                $importId,
                0
            );
            Log::info('end import for job: ' . $id);
            //$count_datasets = $this->checkContent($content, $fileName, $id);

            //Queue::pushOn('emails', new checkContent($content, $fileName, $id));
            Log::info('/salesforce/project/' . (!empty($id) ? $id : '-') . '/import/ success [IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . '] ActivityId:' . $jobhelper_result['activity_id']);

            Log::info('start dispatch for file: ' . $fileName);
            checkContent::dispatch($content, $fileName, $id, $jobhelper_result['activity_id'])->onQueue('checkContent');
            Log::info('end dispatch for file: ' . $fileName);

            return response()->json(array_map(
                function ($array) {
                    return $array;
                },
                ['activity_id' => $jobhelper_result['activity_id']]
            ), 200);
        } catch (SalesforceApiException $e) {
            if (!empty($request->bearerToken())) {
                Log::error('/salesforce/project/' . (!empty($id) ? $id : '-') . '/import/ Unauthorized[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . ']: ' . substr($request->bearerToken(), 0, 3) . '...' . substr($request->bearerToken(), -3));
            } else {
                Log::error('/salesforce/project/' . (!empty($id) ? $id : '-') . '/import/ Empty Bearertoken[IP ' . (!empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '-') . '] ');
            }
            $jobuser = Jobbenutzer::where('id_job', $id)->get();
            foreach ($jobuser as $juser) {
                $userinfo = User::find($juser->id_benutzer);
                if (!empty($userinfo) && $userinfo->is_agentur() && $juser->benachrichtigung == 1) {
                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                    $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'fehler' => 'Fehler bei der Beladung von Daten (Datei: ' . $fileName . ')', 'dateibezeichnung' => $fileName, 'projekt_id' => $this->job->id, 'stepid' => $importId, 'projektname' => $this->job->jobbezeichnung);
                    Mailer::sendMailByTemplate('EMAIL_SYSTEMMELDUNG_TGD', $maildata);
                }
            }
            return response()->json($e->getMessage(), 401);
        }
    }


    /**
     * @param $token
     * @throws SalesforceApiException
     */
    private
    function setUpUser($token)
    {
        if (!$token)
            throw new SalesforceApiException('token missing');
        $this->user = User::where('aktiv', 1)->where('api_token', $token)->first();
        if (!$this->user) {
            Log::error('salesforce API - user not found: ' . substr($token, 0, 3) . '...' . substr($token, -3));
            throw new SalesforceApiException('user not found');
        }
    }

    /**
     * @param $jobId
     * @throws SalesforceApiException
     */
    private
    function setUpJob($jobId)
    {
        $this->job = Job::where('freigegeben', 1)->where('id', $jobId)->first();
        if (!$this->job) {
            Log::error('salesforce API - job not found: ' . $jobId);
            throw new SalesforceApiException('job not found');
        }
    }
}
