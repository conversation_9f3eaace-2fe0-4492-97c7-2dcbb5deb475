<?php

namespace App\Http\Controllers;

use App\ModelHelper\Mailer;
use App\ModelHelper\MailerPayload;
use App\Models\Datei;
use App\Models\Dienstleister;
use App\Models\Druckdateien;
use App\Models\Einzelhashdateien;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\Transmits;
use App\Models\User;
use Carbon\Carbon;

class ProcessController extends Controller
{
    public static function check_processes()
    {
        // CHECK GENERATE PRINT FILES
        $not_processed = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->Where('generiert', 0)->get();
        foreach ($not_processed as $notproc) {
            $error_time = config('timings.send_delay') + config('timings.error_delay');
            $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_time);
            if ($break_even >= Carbon::now()) {
                continue;
            }

            $job = Job::find($notproc->id_job);
            $jobuser = Jobbenutzer::where('id_job', $notproc->id_job)->get();
            foreach ($jobuser as $juser) {
                $userfetch = User::find($juser->id_benutzer);
                if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                    $mailerPayload = new MailerPayload(
                        id: $notproc->id_job,
                        projektname: $job->jobbezeichnung,
                        stepId: $notproc->step_id,
                        fehler: 'Genereierung wurde nicht durchgeführt ( > ' . $error_time . ' Minuten)',
                    );
                    $mailer = new Mailer($userdata, 'EMAIL_SYSTEMMELDUNG_TGD', $mailerPayload);
                    $mailer->execute();
                }
            }
        }

        // CHECK PRINT FILES TO DL
        $not_processed = Druckdateien::WhereNull('geloescht_am')->WhereNull('uebergabe_dienstleister')->get();
        foreach ($not_processed as $notproc) {
            $error_time = config('timings.error_delay');
            $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_time);
            if ($break_even >= Carbon::now()) {
                continue;
            }

            $job = Job::find($notproc->id_job);
            $jobuser = Jobbenutzer::where('id_job', $notproc->id_job)->get();
            $dienstleister = Dienstleister::find($notproc->id_dienstleister);
            foreach ($jobuser as $juser) {
                $userfetch = User::find($juser->id_benutzer);

                if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                    $mailerPayload = new MailerPayload(
                        id: $notproc->id_job,
                        projektname: $job->jobbezeichnung,
                        dienstleister: (!empty($dienstleister) ? $dienstleister->dienstleister : ''),
                        stepId: $notproc->step_id,
                        fehler: 'Datei wurde nicht übergeben (erzeugt ' . Carbon::parse($notproc->created_at)->format('d.m.Y H:i') . ')',
                    );
                    $mailer = new Mailer($userdata, 'EMAIL_SYSTEMMELDUNG_TGD', $mailerPayload);
                    $mailer->execute();
                }
            }
        }
        // CHECK ARCHIV FILES ARE CREATED
        $not_processed = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->get();
        foreach ($not_processed as $notproc) {
            $error_archive_time = config('timings.error_archive_time');
            $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_archive_time);
            if ($break_even >= Carbon::now()) {
                continue;
            }

            $job = Job::find($notproc->id_job);
            $jobuser = Jobbenutzer::where('id_job', $notproc->id_job)->get();
            $count_archiv = Einzelhashdateien::Where('id_job', $notproc->id_job)->Where('step_id', $notproc->step_id)->count();
            if ($count_archiv < $notproc->datensaetze) {
                foreach ($jobuser as $juser) {
                    $userfetch = User::find($juser->id_benutzer);

                    if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                        $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                        $mailerPayload = new MailerPayload(
                            id: $notproc->id_job,
                            projektname: $job->jobbezeichnung,
                            dienstleister: (!empty($dienstleister) ? $dienstleister->dienstleister : ''),
                            stepId: $notproc->step_id,
                            fehler: 'Archiv wurde nicht komplett erzeugt (' . $count_archiv . '/' . $notproc->datensaetze . ')',
                        );
                        $mailer = new Mailer($userdata, 'EMAIL_SYSTEMMELDUNG_TGD', $mailerPayload);
                        $mailer->execute();
                    }
                }
            }
        }

        // CHECK ARCHIV FILES NOT TRANSMITTED
        $not_processed = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->get();
        foreach ($not_processed as $notproc) {
            $error_archivetransmit_time = config('timings.error_archivetransmit_time');
            $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_archivetransmit_time);
            if ($break_even >= Carbon::now()) {
                continue;
            }

            $job = Job::find($notproc->id_job);
            $jobuser = Jobbenutzer::where('id_job', $notproc->id_job)->get();
            $count_archiv = Transmits::LeftJoin('hashdateien_einzeldatei', 'hashdateien_einzeldatei.id', 'transmits.id_datei')
                ->Where('hashdateien_einzeldatei.id_job', $notproc->id_job)->Where('transmits.id_liefertyp', 1)
                ->Where('transmits.status_einlieferung', '202')
                ->Where('hashdateien_einzeldatei.step_id', $notproc->step_id)->count();
            if ($count_archiv < $notproc->datensaetze) {
                foreach ($jobuser as $juser) {
                    $userfetch = User::find($juser->id_benutzer);

                    if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                        $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                        $mailerPayload = new MailerPayload(
                            id: $notproc->id_job,
                            projektname: $job->jobbezeichnung,
                            dienstleister: (!empty($dienstleister) ? $dienstleister->dienstleister : ''),
                            stepId: $notproc->step_id,
                            fehler: 'Archiv wurde nicht komplett übergeben (' . $count_archiv . '/' . $notproc->datensaetze . ')',
                        );
                        $mailer = new Mailer($userdata, 'EMAIL_SYSTEMMELDUNG_TGD', $mailerPayload);
                        $mailer->execute();
                    }
                }
            }

            // CHECK RESPONSE FILES NOT TRANSMITTED
            $not_processed = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->get();
            foreach ($not_processed as $notproc) {
                $error_archivetransmit_time = config('timings.error_archivetransmit_time');
                $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_archivetransmit_time);
                if ($break_even >= Carbon::now()) {
                    continue;
                }

                $job = Job::find($notproc->id_job);
                $received_responses_count = Datei::WhereNull('geloescht_am')->Where('id_job', $notproc->id_job)->Where('step_id', $notproc->step_id)->count();
                $jobuser = Jobbenutzer::where('id_job', $notproc->id_job)->get();


                $count_archiv = Transmits::LeftJoin('responsedateien_einzeldateien', 'responsedateien_einzeldateien.id', 'transmits.id_datei')->Where('responsedateien_einzeldateien.id_job', $notproc->id_job)->Where('transmits.id_liefertyp', 2)->Where('transmits.status_einlieferung', '202')->Where('responsedateien_einzeldateien.step_id', $notproc->step_id)->count();
                if ($count_archiv < $received_responses_count) {
                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);

                        if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->benachrichtigung == 1)) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                            $mailerPayload = new MailerPayload(
                                id: $notproc->id_job,
                                projektname: $job->jobbezeichnung,
                                dienstleister: (!empty($dienstleister) ? $dienstleister->dienstleister : ''),
                                stepId: $notproc->step_id,
                                fehler: 'Responses wurde nicht komplett übergeben (' . $count_archiv . '/' . count($not_processed) . ')',
                                ordnerbezeichnung: $notproc->org_name
                            );
                            $mailer = new Mailer($userdata, 'EMAIL_SYSTEMMELDUNG_TGD', $mailerPayload);
                            $mailer->execute();
                        }
                    }
                }
            }
        }
    }
}
