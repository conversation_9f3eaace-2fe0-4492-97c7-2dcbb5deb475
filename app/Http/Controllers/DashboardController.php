<?php

namespace App\Http\Controllers;

use App\Models\Altejobs;
use App\Models\Document;
use App\Models\Rechte;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index()
    {
        $userdata = User::current()->toArray();
        $userdata['rechte'] = ['typ' => User::current()->role()];
        $user_tmp = Rechte::where('id_benutzer', $userdata)->get()->toArray();
        $last_old_job_obj = Altejobs::Where('email', $userdata['email'])->first();
        $last_old_job = [];
        if (!empty($last_old_job_obj)) {
            $last_old_job = $last_old_job_obj->toArray();
        }

        $userrechte = [];
        foreach ($user_tmp as $rechte) {
            $userrechte[$rechte['id_job']] = $rechte;
        }

        $userdata['rechte']['jobs'] = $userrechte;
        session()->put('userdata', $userdata);
        $jobs = ProjektController::get_jobactivities(Auth::id());
        $aufgaben = ProjektController::get_jobs(Auth::id());

        $daten = [
            'jobs' => $jobs,
            'aufgaben' => $aufgaben,
            'userdata' => $userdata,
            'last_old_job' => $last_old_job,
        ];

        session('all', 0);

        if (config('app.mandant') == 'ACTISALE') {
            return view('dashboardAS', ['daten' => $daten]);
        }
        return view('dashboard', ['daten' => $daten]);
    }


    /**
     * Display a listing of the resource.
     *
     *
     */
    public function archiv()
    {
        $userdata = User::current()->toArray();
        $userdata['rechte'] = ['typ' => User::current()->role()];
        $user_tmp = Rechte::where('id_benutzer', $userdata)->get()->toArray();
        $last_old_job_obj = Altejobs::Where('email', $userdata['email'])->first();
        $last_old_job = [];
        if (!empty($last_old_job_obj)) {
            $last_old_job = $last_old_job_obj->toArray();
        }

        $userrechte = [];
        foreach ($user_tmp as $rechte) {
            $userrechte[$rechte['id_job']] = $rechte;
        }

        $userdata['rechte']['jobs'] = $userrechte;
        session()->put('userdata', $userdata);
        $jobs = ProjektController::get_jobactivities(Auth::id());
        $aufgaben = ProjektController::get_jobs_done(Auth::id());

        $daten = [
            'jobs' => $jobs,
            'aufgaben' => $aufgaben,
            'userdata' => $userdata,
            'last_old_job' => $last_old_job,
        ];
        session('all', 0);

        if (config('app.mandant') == 'ACTISALE') {
            return view('dashboardAS', [
                'daten' => $daten,
            ]);
        }
        return view('archiv', [
            'daten' => $daten,
        ]);
    }
}
