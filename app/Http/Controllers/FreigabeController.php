<?php

namespace App\Http\Controllers;

use App\ModelHelper\Mailer;
use App\ModelHelper\MailerPayload;
use App\Models\Agenturfreigaben;
use App\Models\AktivitaetenDoc;
use App\Models\Autojob;
use App\Models\Datei;
use App\Models\Doctypen;
use App\Models\DokFreigabeA;
use App\Models\DokFreigabeK;
use App\Models\Dokument2Jobs;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\Jobdokumente;
use App\Models\Kundenfreigaben;
use App\Models\Rechte;
use App\Models\Transmitdate;
use App\Models\User;
use App\Models\Editordocument;
use App\Models\EditorRelease;
use App\Models\FrontifyRelease;
use Carbon\Carbon;
use Illuminate\Container\RewindableGenerator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FreigabeController extends Controller
{
    public function projectrelease($id)
    {
        $id_tag = session('id_tag');
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }

        return $this->show($id, null, $id_tag, null, url('/'));
    }

    public function docrelease($id, $id_doc)
    {
        $id_tag = session('id_tag');
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }
        return $this->show($id, $id_doc, $id_tag, null, url('/'));
    }

    public function editorrelease($id)
    {
        $id_tag = session('id_tag');
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }
        return $this->show($id, 0, $id_tag, 0, url('/'));
    }

    public function statistics($id)
    {
        $id_tag = session('id_tag');

        return $this->showautofiles($id, null, $id_tag, null, url('/'));
    }

    public function storeTransmitdate(Request $regeust, $id)
    {
        $id_tag = session('id_tag');
        $postData = $regeust->all();
        if (!empty($postData)) {
            $newTransmitdate = new Transmitdate();
            $newTransmitdate->id_job = $postData['jobId'];
            $newTransmitdate->step_id = $postData['stepId'];
            $newTransmitdate->diff = $postData['diff'];
            $newTransmitdate->user = Auth::id();
            $result = $newTransmitdate->save();
            if ($result) {
                $jobuser = Jobbenutzer::where('id_job', $postData['jobId'])->get();
                $job = Job::find($postData['jobId']);
                $datei = Datei::where('id_job', $postData['jobId'])->where('step_id', $postData['stepId'])->first();
                $transmitDate = Transmitdate::getTransmitDate($postData['jobId'], $postData['stepId']);
                foreach ($jobuser as $juser) {
                    $userfetch = User::find($juser->id_benutzer);
                    if (!empty($userfetch) && ($juser->benachrichtigung == 1)) {
                        $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                        $mailerPayload = new MailerPayload(
                            $job->id,
                            $job->jobbezeichnung,
                            "",
                            "",
                            $postData['stepId'],
                            config('app.APP_URL') . 'freigabe/' . $job->id,
                            "",
                            "",
                            "",
                            "",
                            Carbon::parse($datei->PAL_date)->format('d.m.Y'),
                            Carbon::parse($transmitDate)->format('d.m.Y')
                        );
                        $mailer = new Mailer(
                            $userdata,
                            'TS4SF_EMAIL_AENDERUNG_AUSLIEFERUNGSDATUM',
                            $mailerPayload);
                        $mailer->execute();
                    }
                }
            }
        }
    }

    public function get_job_by_reg($id, $reg)
    {
        $id_tag = session('id_tag');
        return $this->show($id, null, $id_tag, null, url('/'), null, null, $reg);
    }


    public function get_job_by_tag($id, $tag)
    {
        $id_tag = $tag;
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }
        session()->put('id_tag', $id_tag);
        return redirect('freigabe/' . $id);
    }


    public function show_file_by_id($id, $id_doc)
    {
        $id_tag = session('id_tag');
        return $this->show($id, $id_doc, $id_tag, true, url('/'));
    }

    public function show_file_by_document($id, $id_doc, $jobfile)
    {
        $id_tag = session('id_tag');
        return $this->show($id, $id_doc, $id_tag, true, url('/'), false, $jobfile);
    }

    public function get_file_by_id($id, $id_doc)
    {
        $id_tag = session('id_tag');
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }
        return $this->show($id, $id_doc, $id_tag, null, url('/'), true);
    }

    public function showMarketingcloud($id)
    {
        $id_tag = session('id_tag');
        if (User::current()->is_kunde()) {
            $id_tag = 2;
        }
        return $this->show($id, null, $id_tag, null, url('/'), true, null, 'marketingcloud');
    }

    /**
     * Display a listing of the resource.
     *
     *
     */
    public function show($id, $id_doc = null, $id_tag = null, $weddingdoc = false, $path = './', $is_file = false, $jobfile = null, $reg = null)
    {
        $dummyData = false;
        $testData = false;
        $allowCRMRelease = false;
        $allowed_job = User::current()
            ->getJob($id)
            ->first();
        if (empty($allowed_job)) {
            return redirect('/projekte');
        }

        $editorRight = User::current()->getRightFromJob($id, 'editor')['editor'];

        $rechte = Jobbenutzer::Where('id_job', $id)
            ->Where('id_benutzer', Auth::id())
            ->Where('ansehen', 1)
            ->Where('gesperrt', 0)
            ->first();
        if (empty($rechte)) {
            return redirect('/projekte');
        }

        $documents = Job::getDocuments($id, null, $reg);

        $hochdocs = Job::getHochDocs($id);
        $typen = Doctypen::all();
        $job = Job::find($id);
        $users = $job->user();
        $agenturfreigabe = 0;
        $kundefreigabe = 0;

        $freigabecount = 1;

        $doc = '';
        $docaktivitaeten = [];
        $file_info = '';

        $autojob = Autojob::where('id_job', $id)->orderby('id', 'DESC')->first();

        $query = Datei::Where('id_job', $id)->Where('org_name', 'like', '%.csv');
        if ($reg == 'salesforce') {
            $query = $query->Where('generiert', 0)->WhereNull('stopzeit');
        } else {
            $query = $query->orWhere('org_name', 'like', '%.pdf');
        }

        $jobfiles = $query->get();

        $userdata = User::current()->toArray();

        $userdata['rechte'] = ['typ' => User::current()->role()];
        $user_tmp = Rechte::where('id_benutzer', $userdata)
            ->get()->toArray();
        $userrechte = [];
        foreach ($user_tmp as $rechte) {
            $userrechte[$rechte['id_job']] = $rechte;
        }

        $userdata['rechte']['jobs'] = $userrechte;
        session()->put('userdata', $userdata);
        $freigabeK = 0;
        $freigabeA = 0;
        if (!empty($id_doc) && !$is_file && $reg != 'salesforce') {
            $doc = Dokument2Jobs::getDocument($id_doc);

            $freigabeK = Dokument2Jobs::GetCountKundenFreigabe($doc->id);
            $freigabeA = Dokument2Jobs::GetCountAgenturFreigabe($doc->id);

            $subfreigabe_a = [];

            if (User::current()->is_agentur()) {
                $subfreigabe_a = $this->GetAgenturFreigabeHistory($doc->id);
            }

            $subfreigabe_k = $this->GetKundenFreigabeHistory($doc->id);
            $job_fuer_kunde = Job::find($id);

            $docaktivitaeten_tmp = $this->GetDocAktivitaeten($doc, $job_fuer_kunde);

            // ADD FREIGABE AN KUNDEN
            $docaktivitaeten_ffk = $this->GetJobFuerKundenFreigabe($id, $job_fuer_kunde);

            if (!empty($docaktivitaeten_ffk)) {
                $docaktivitaeten[Carbon::parse($docaktivitaeten_ffk->afreigabedatum)->timestamp] = $docaktivitaeten_ffk;
            }

            $file_info = '';

            if (User::current()->is_agentur()) {
                foreach ($docaktivitaeten_tmp as $tmp) {
                    $docaktivitaeten[Carbon::parse($tmp->created_at)->timestamp] = $tmp;
                }
                foreach ($subfreigabe_a as $tmp) {
                    $docaktivitaeten[Carbon::parse($tmp->created_at)->timestamp] = $tmp;
                }
            }

            foreach ($subfreigabe_k as $tmp) {
                $docaktivitaeten[Carbon::parse($tmp->created_at)->timestamp] = $tmp;
            }

            if (!empty($docaktivitaeten)) {
                krsort($docaktivitaeten);
            }
        } else if (!empty($id_doc) && !$is_file && $reg == 'salesforce') {
            $doc = Datei::find($id_doc)->select('name', 'typ', 'preview_img', 'freigabe_noetig');

            $freigabeK = Dokument2Jobs::GetCountKundenFreigabe($id_doc);
            $freigabeA = Dokument2Jobs::GetCountAgenturFreigabe($id_doc);
            $file_info = '';
        } elseif (!empty($id_doc) && $is_file) {
            $file_info = Datei::find($id_doc);
            $agenturfreigabe = Dokument2Jobs::GetCountAgenturFreigabe($id_doc);
            $kundefreigabe = Dokument2Jobs::GetCountKundenFreigabe($id_doc);
        }


        $docfreiagabenkunden = AktivitaetenDoc::Where('dokument_aktivitaeten.id_job', '=', $id)
            ->Where('dokument_aktivitaeten.id_docaktivitaet', 2)
            ->distinct()
            ->get('id_document2job');

        $docfreiagabenagentur = AktivitaetenDoc::Where('dokument_aktivitaeten.id_job', '=', $id)
            ->Where('dokument_aktivitaeten.id_docaktivitaet', 3)
            ->distinct()
            ->get('id_document2job');

        $array_freigaben = [
            'kunde' => [],
            'agentur' => [],
        ];


        foreach ($docfreiagabenagentur as $freigaben) {
            $array_freigaben['agentur'][] = $freigaben->id_document2job;
        }


        foreach ($docfreiagabenkunden as $freigaben) {
            $array_freigaben['kunde'][] = $freigaben->id_document2job;
        }

        $ablehnungenkunde = AktivitaetenDoc::Where('dokument_aktivitaeten.id_job', '=', $id)
            ->Where('dokument_aktivitaeten.id_docaktivitaet', 7)
            ->distinct()
            ->get('id_document2job');

        $array_ablehnnung['kunde'] = [];
        foreach ($ablehnungenkunde as $ablehnung) {
            $array_ablehnnung['kunde'][] = $ablehnung->id_document2job;
        }


        $doctypen = [];
        foreach ($typen as $typ) {
            $doctypen[$typ->id] = $typ->typ;
        }

        $freigabe_dokcount = Jobdokumente::Where('id_job', $job->id)
                ->Where('anzeige_kunde', 1)
                ->count() - count($array_ablehnnung['kunde']);

        $freigabeuser = "";
        if ($job->id_freigabeuser > 0) {
            $user = User::find($job->id_freigabeuser);
            $freigabeuser = (!empty($user->vorname) ? $user->vorname : '') . " " . (!empty($user->name) ? $user->name : '');
        }

        $editor = Editordocument::getLastVersion($job->id);

        $allowCustomerRelease = false;
        if (!empty($documents)) {


            foreach ($documents as $document) {
                if ($document['typ'] == 3) {
                    $dummyData = true;
                }
                if ($document['typ'] == 10) {
                    $testData = true;
                }
            }
            if ($dummyData && $testData) {
                $allowCustomerRelease = true;
            }

        }

        $flagEditorRelease = false;
        if (!empty($editor->frontify) && $editor->frontify == 1 && EditorRelease::getReleaseCount($id) >= 2) {
            $allowCRMRelease = true;
            $flagEditorRelease = true;
        }

        $userEditorRelease = [];

        if ($reg == 'marketingcloud') {
            return view('mcview', [
                'documents' => $documents,
                'job' => $job,
                'bladetitel' => 'Alle',
                'doctypen' => $doctypen,
                'id_doc' => $id_doc,
                'doc' => $doc,
                'count' => $freigabe_dokcount,
                'docaktivitaeten' => $docaktivitaeten,
                'freigaben' => $array_freigaben,
                'ablehnungen' => $array_ablehnnung,
                'druckfreigaben' => [],
                'weddingdoc' => $weddingdoc,
                'path' => $path,
                'jobfiles' => $jobfiles,
                'tag' => $id_tag,
                'hochdocs' => $hochdocs,
                'is_file' => $is_file,
                'file_info' => $file_info,
                'jobfile' => $jobfile,
                'autojob' => $autojob,
                'users' => $users,
                'agenturfreigabe' => $agenturfreigabe,
                'kundefreigabe' => $kundefreigabe,
                'freigabeK' => $freigabeK,
                'freigabeA' => $freigabeA,
                'freigabecount' => $freigabecount,
                'reg' => $reg,
                'freigabeuser' => $freigabeuser,
                'editor' => $editor,
                'allowCustomerRelease' => $allowCustomerRelease,
                'allowCRMRelease' => $allowCRMRelease,
                'flagEditorRelease' => $flagEditorRelease,
                'userEditorRelease' => $userEditorRelease,
            ]);
        }


        if ($id_doc === 0) {
            $docaktivitaeten = [];
            $editorReleases = EditorRelease::where('jobId', $id)->OrderBy('created_at', 'DESC')->get();
            foreach ($editorReleases as $editorRelease) {
                $userData = User::find($editorRelease->user);
                if ($editorRelease->release !== null) {
                    $userEditorRelease [] = $userData->id;
                    $docaktivitaeten[Carbon::parse($editorRelease->created_at)->format('YmdHis')] = [
                        'vorname' => $userData->vorname,
                        'name' => $userData->name,
                        'release' => $editorRelease->release === 1 ? 'Freigabe' : 'Ablehnung',
                        'created_at' => $editorRelease->created_at,
                        'updated_at' => $editorRelease->updated_at,

                    ];
                }

            }
            $frontifyReleases = FrontifyRelease::where('jobId', $id)->OrderBy('created_at', 'DESC')->get();
            foreach ($frontifyReleases as $frontifyRelease) {
                $userData = User::find($frontifyRelease->user);
                if ($frontifyRelease->release !== null) {
                    $userFrontifyRelease [] = $userData->id;
                    $docaktivitaeten[Carbon::parse($frontifyRelease->created_at)->format('YmdHis')] = [
                        'vorname' => $userData->vorname,
                        'name' => $userData->name,
                        'release' => $frontifyRelease->release === 1 ? 'Frontify-Freigabe' : 'Frontify-Rücknahme',
                        'created_at' => $frontifyRelease->created_at,
                        'updated_at' => $frontifyRelease->updated_at,

                    ];
                }

            }

            krsort($docaktivitaeten);

            $freigabeK = EditorRelease::getReleaseCount($id);
            $freigabecount = 2;
        }

        return view('freigabe', [
            'documents' => $documents,
            'job' => $job,
            'bladetitel' => 'Alle',
            'doctypen' => $doctypen,
            'id_doc' => $id_doc,
            'doc' => $doc,
            'count' => $freigabe_dokcount,
            'docaktivitaeten' => $docaktivitaeten,
            'freigaben' => $array_freigaben,
            'ablehnungen' => $array_ablehnnung,
            'druckfreigaben' => [],
            'weddingdoc' => $weddingdoc,
            'path' => $path,
            'jobfiles' => $jobfiles,
            'tag' => $id_tag,
            'hochdocs' => $hochdocs,
            'is_file' => $is_file,
            'file_info' => $file_info,
            'jobfile' => $jobfile,
            'autojob' => $autojob,
            'users' => $users,
            'agenturfreigabe' => $agenturfreigabe,
            'kundefreigabe' => $kundefreigabe,
            'freigabeK' => $freigabeK,
            'freigabeA' => $freigabeA,
            'freigabecount' => $freigabecount,
            'reg' => $reg,
            'freigabeuser' => $freigabeuser,
            'editor' => $editor,
            'editorRight' => $editorRight,
            'allowCustomerRelease' => $allowCustomerRelease,
            'allowCRMRelease' => $allowCRMRelease,
            'flagEditorRelease' => $flagEditorRelease,
            'userEditorRelease' => $userEditorRelease,
        ]);
    }

    /**
     * Display a listing of the resource.
     *
     *
     */
    private function showautofiles($id, $id_doc = null, $id_tag = null, $weddingdoc = false, $path = './', $is_file = false, $jobfile = null)
    {
        $documents = Job::getDocuments($id, $id_tag);
        $hochdocs = Job::getHochDocs($id, $id_tag);
        $typen = Doctypen::all();
        $job = Job::find($id);
        $users = $job->user();

        $autojob = Autojob::where('id_job', $id)->orderby('id', 'DESC')->first();
        $jobfiles = Datei::Where('id_job', $id)->Where('org_name', 'like', '%.csv')->where(function ($q) {
            $q->where('generiert', 1)->orwhereNotNull('stopzeit');
        })->orderBy('created_at', 'DESC')->get(['id', 'org_name', 'created_at', 'dateigroesse', 'geloescht_am', 'step_id', 'stopzeit', 'id_stop_user', 'id_job', 'datensaetze', 'PAL_date']);

        $summe_salesforce = 0;
        foreach ($jobfiles as $jobf) {
            if (empty($jobf->stopzeit)) {
                $summe_salesforce += $jobf->datensaetze;
            }
        }

        $userdata = User::current()->toArray();
        $userdata['rechte'] = ['typ' => User::current()->role()];
        $user_tmp = Rechte::where('id_benutzer', $userdata)->get()->toArray();
        $userrechte = [];
        foreach ($user_tmp as $rechte) {
            $userrechte[$rechte['id_job']] = $rechte;
        }

        $userdata['rechte']['jobs'] = $userrechte;
        session()->put('userdata', $userdata);

        $doc = '';
        $docaktivitaeten = '';
        $file_info = '';
        if (!empty($id_doc) && !$is_file) {
            $doc = Dokument2Jobs::getDocument($id_doc);

            $docaktivitaeten = AktivitaetenDoc::leftJoin('docaktivitaeten', 'dokument_aktivitaeten.id_docaktivitaet', '=', 'docaktivitaeten.id')
                ->leftJoin('users', 'dokument_aktivitaeten.id_benutzer', '=', 'users.id')
                ->Where('dokument_aktivitaeten.id_document2job', '=', $doc->id)
                ->OrderBy('dokument_aktivitaeten.created_at', 'DESC')
                ->get();
        } elseif (!empty($id_doc) && $is_file == true) {
            $file_info = Datei::find($id_doc);
        }

        $query = AktivitaetenDoc::Where('dokument_aktivitaeten.id_job', '=', $id)
            ->Where('dokument_aktivitaeten.id_docaktivitaet', '=', 2);
        if (!empty($id_tag)) {
            $query = $query->leftJoin('tagzuordnung', 'dokument_aktivitaeten.id_document2job', '=', 'tagzuordnung.id_b2j')
                ->Where('tagzuordnung.id_tag', '=', $id_tag);
        }

        $docfreiagaben = $query->get();

        $array_freigaben = [];
        foreach ($docfreiagaben as $freigaben) {
            $array_freigaben[] = $freigaben->id_document2job;
        }

        $doctypen = [];
        foreach ($typen as $typ) {
            $doctypen[$typ->id] = $typ->typ;
        }


        return view('autozahlen', [
            'documents' => $documents,
            'job' => $job,
            'bladetitel' => 'Alle',
            'doctypen' => $doctypen,
            'id_doc' => $id_doc,
            'doc' => $doc,
            'count' => count($documents),
            'docaktivitaeten' => $docaktivitaeten,
            'freigaben' => $array_freigaben,
            'weddingdoc' => $weddingdoc,
            'path' => $path,
            'jobfiles' => $jobfiles,
            'tag' => $id_tag,
            'hochdocs' => $hochdocs,
            'is_file' => $is_file,
            'file_info' => $file_info,
            'jobfile' => $jobfile,
            'autojob' => $autojob,
            'users' => $users,
            'summe_salesforce' => $summe_salesforce
        ]);
    }

    public function set_doc_release(Request $request)
    {
        $requestData = $request->all();
        if ($requestData['id_docfreigabe'] === "0") {
            $editorReleas = new EditorRelease();
            $editorReleas->user = Auth::id();
            $editorReleas->jobId = $requestData['id_job'];
            $editorReleas->release = 1;
            $editorReleas->save();
        } else {
            $freigabecount = (int)($request->id_doctyp == 4);

            if (User::current()->is_kunde()) {
                $freigabe = new DokFreigabeK();
            } else if (User::current()->is_agentur()) {
                $freigabe = new DokFreigabeA();
            }

            $freigabe->id_document2jobs = $request->id_docfreigabe;
            $freigabe->id_user = Auth::id();
            $freigabe->save();

            if (((Dokument2Jobs::GetCountAgenturFreigabe($request->id_docfreigabe) > $freigabecount && User::current()->is_agentur())
                || (Dokument2Jobs::GetCountKundenFreigabe($request->id_docfreigabe) > $freigabecount && User::current()->is_kunde()))) {
                $aktivitaeteninsert = new AktivitaetenDoc();
                $aktivitaeteninsert->id_job = $request->id_job;
                $aktivitaeteninsert->id_document2job = $request->id_docfreigabe;

                $jobuser = Jobbenutzer::where('id_job', $request->id_job)->get();
                $job = Job::find($request->id_job);

                if (User::current()->is_kunde()) {
                    $aktivitaeteninsert->id_docaktivitaet = 2;
                    $aktivitaeteninsert->datetime = Carbon::now();
                    $aktivitaeteninsert->id_benutzer = Auth::id();
                    $aktivitaeteninsert->save();
                    if ($request->id_doctyp == 4) {
                        foreach ($jobuser as $juser) {
                            $userfetch = User::find($juser->id_benutzer);

                            if (
                                !empty($userfetch) && $userfetch->is_kunde()
                                && ($juser->freigabe == 1)
                                && ($juser->benachrichtigung == 1)
                            ) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    id: $request->id_job,
                                    projektname: $job->jobbezeichnung,
                                    link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE', $mailerPayload);
                                $mailer->execute();
                            }

                            if (
                                !empty($userfetch) && $userfetch->is_agentur()
                                && ($juser->freigabe == 1)
                                && ($juser->benachrichtigung == 1)
                            ) {
                                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                                $mailerPayload = new MailerPayload(
                                    id: $request->id_job,
                                    projektname: $job->jobbezeichnung,
                                    link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE_INFOTGD', $mailerPayload);
                                $mailer->execute();
                            }
                        }
                    }
                }
                if (User::current()->is_agentur()) {
                    $aktivitaeteninsert->id_docaktivitaet = 3;
                    $aktivitaeteninsert->datetime = Carbon::now();
                    $aktivitaeteninsert->id_benutzer = Auth::id();
                    $aktivitaeteninsert->save();
                    if ($request->id_doctyp == 4) {
                        foreach ($jobuser as $juser) {
                            $userfetch = User::find($juser->id_benutzer);
                            if (
                                !empty($userfetch) && $userfetch->is_agentur()
                                && ($juser->freigabe == 1)
                                && ($juser->benachrichtigung == 1)
                            ) {
                                $userdata = User::find($juser->id_benutzer, [
                                    'id',
                                    'email',
                                    'name',
                                    'vorname'
                                ]);
                                $mailerPayload = new MailerPayload(
                                    id: $request->id_job,
                                    projektname: $job->jobbezeichnung,
                                    link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                                );
                                $mailer = new Mailer($userdata, 'EMAIL_FREIGABENABSCHLUSS_TGD', $mailerPayload);
                                $mailer->execute();
                            }
                        }
                    }
                }
            } else {
                $job = Job::find($request->id_job);
                $jobuser = Jobbenutzer::where('id_job', $request->id_job)->get();

                if (User::current()->is_kunde()) {
                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);
                        if (
                            !empty($userfetch) && $userfetch->is_kunde()
                            && ($juser->freigabe == 1)
                            && ($juser->benachrichtigung == 1)
                        ) {
                            $userdata = User::find($juser->id_benutzer, [
                                'id',
                                'email',
                                'name',
                                'vorname'
                            ]);
                            $mailerPayload = new MailerPayload(
                                id: $request->id_job,
                                projektname: $job->jobbezeichnung,
                                link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                            );
                            $mailer = new Mailer($userdata, 'EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE', $mailerPayload);
                            $mailer->execute();
                        }
                        if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->freigabe == 1) && ($juser->benachrichtigung == 1)) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                            $mailerPayload = new MailerPayload(
                                id: $request->id_job,
                                projektname: $job->jobbezeichnung,
                                link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                            );
                            $mailer = new Mailer($userdata, 'EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE_INFOTGD', $mailerPayload);
                            $mailer->execute();
                        }
                    }
                } else if (User::current()->is_agentur()) {
                    foreach ($jobuser as $juser) {
                        $userfetch = User::find($juser->id_benutzer);
                        if (!empty($userfetch) && $userfetch->is_agentur() && ($juser->freigabe == 1) && ($juser->benachrichtigung == 1)) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                            $mailerPayload = new MailerPayload(
                                id: $request->id_job,
                                projektname: $job->jobbezeichnung,
                                link: config('app.APP_URL') . 'freigabe/' . $request->id_job,
                            );
                            $mailer = new Mailer($userdata, 'EMAIL_2TE_FREIGABE_TGD', $mailerPayload);
                            $mailer->execute();
                        }
                    }
                }
            }

        }
        return redirect('freigabe/' . $request->id_job);
    }

    public function set_doc_reject(Request $request)
    {
        if (User::current()->is_kunde()) {
            $freigabe = new AktivitaetenDoc();
            $freigabe->id_document2job = $request->id_docablehnung;
            $freigabe->id_docaktivitaet = 7;
            $freigabe->id_job = $request->id_job;
            $freigabe->datetime = now();
            $freigabe->id_benutzer = Auth::id();
            $freigabe->save();
        }

        $job = Job::find($request->id_job);
        $jobuser = Jobbenutzer::where('id_job', $request->id_job)->get();

        if (User::current()->is_kunde()) {
            foreach ($jobuser as $juser) {
                $userfetch = User::find($juser->id_benutzer);
                if (!empty($userfetch) && ($userfetch->is_agentur() || $userfetch->is_kunde()) && ($juser->freigabe == 1) && ($juser->benachrichtigung == 1)) {
                    $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                    $mailerPayload = new MailerPayload(
                        id: $request->id_job,
                        projektname: $job->jobbezeichnung,
                        link: config('app.APP_URL') . 'freigabe/' . $request->id_job
                    );
                    $mailer = new Mailer($userdata, 'EMAIL_FREIGABE_ABGELEHNT', $mailerPayload);
                    $mailer->execute();
                }
            }
        }

        $job->in_bearbeitung = 1;
        $job->save();
        return redirect('freigabe/' . $request->id_job);
    }

    private function set_file_release(Request $request)
    {
        $datei = Datei::find($request->id_dateifreigabe);
        $datei->freigegeben = 1;
        $datei->freigabezeit = Carbon::now();
        $datei->id_benutzer = Auth::id();
        $datei->save();
        return redirect('freigabe/' . $request->id_job . '/file/' . $request->id_dateifreigabe);
    }

    public function set_job_release(Request $request)
    {
        $job = Job::find($request->id_freigabe);
        $job->freigegeben = 1;
        $job->freigabedatum = Carbon::now();
        $job->id_freigabeuser = Auth::id();
        $job->save();

        $jobuser = Jobbenutzer::where('id_job', $request->id_freigabe)->get();

        foreach ($jobuser as $juser) {
            $userfetch = User::find($juser->id_benutzer);

            if (!empty($userfetch) && ($juser->benachrichtigung == 1)) {
                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                $mailerPayload = new MailerPayload(id: $request->id_freigabe, projektname: $job->jobbezeichnung, link: config('app.APP_URL') . 'freigabe/' . $request->id_freigabe);
                $mailer = new Mailer($userdata, 'EMAIL_MC_FREIGABE_ERFOLGT', $mailerPayload);
                $mailer->execute();
            }
        }
        return redirect('freigabe/' . $request->id_freigabe);
    }

    public function stop_delivery(Request $request)
    {
        $datei = Datei::find($request->id_stoppen);
        $datei->stopzeit = Carbon::now();
        $datei->id_stop_user = Auth::id();
        $datei->save();
        return redirect('freigabe/' . $datei->id_job . '/reg/salesforce');
    }

    public function set_customer_job_release(Request $request)
    {
        $job = Job::find($request->id_freigabe);
        $job->fuer_kunde_frei = 1;
        $job->in_bearbeitung = 0;
        $job->afreigabedatum = Carbon::now();
        $job->id_afreigabeuser = Auth::id();
        $result = $job->save();
        if ($result) {
            $jobuser = Jobbenutzer::where('id_job', $request->id_freigabe)->get();
            foreach ($jobuser as $juser) {
                $userfetch = User::find($juser->id_benutzer);
                if (empty($userfetch) || $userfetch->is_agentur() && $juser->benachrichtigung != 1) {
                    continue;
                }

                $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                $mailerPayload = new MailerPayload(
                    id: $request->id_freigabe,
                    projektname: $job->jobbezeichnung,
                    link: config('app.APP_URL') . 'freigabe/' . $request->id_freigabe,
                );
                $mailer = new Mailer($userdata, 'EMAIL_PROJEKTFREIGABE_TGD', $mailerPayload);
                $mailer->execute();
            }
        }
        return redirect('freigabe/' . $request->id_freigabe);
    }

    public static function dokstorno($id)
    {
        //KundenStorno
        $docfreigaben_kunden = AktivitaetenDoc::Where('id_document2job', $id)
            ->Where('id_docaktivitaet', '2')
            ->get();
        foreach ($docfreigaben_kunden as $docfreigabe) {
            $docfreigabe = AktivitaetenDoc::find($docfreigabe->id);
            if (!empty($docfreigabe)) {
                $docfreigabe->id_docaktivitaet = 4;
                $docfreigabe->save();
            }
        }

        $freigaben_kunde = Kundenfreigaben::Where('id_document2jobs', $id)
            ->get();
        foreach ($freigaben_kunde as $freigabe_kunde) {
            $storno_kunde = Kundenfreigaben::find($freigabe_kunde->id);
            $storno_kunde->storno = 1;
            $storno_kunde->save();
        }

        //Agenturstorno
        $docfreigaben_agentur = AktivitaetenDoc::Where('id_document2job', $id)
            ->Where('id_docaktivitaet', '3')
            ->get();
        foreach ($docfreigaben_agentur as $docfreigabe) {
            $docfreigabe = AktivitaetenDoc::find($docfreigabe->id);
            if (!empty($docfreigabe)) {
                $docfreigabe->id_docaktivitaet = 5;
                $docfreigabe->save();
            }
        }

        $freigaben_agentur = Agenturfreigaben::Where('id_document2jobs', $id)
            ->get();
        foreach ($freigaben_agentur as $freigabe_agentur) {
            $storno_agentur = Agenturfreigaben::find($freigabe_agentur->id);
            $storno_agentur->storno = 1;
            $storno_agentur->save();
        }

        $doc2job = Jobdokumente::find($id);
        $add_storno = new AktivitaetenDoc();
        $add_storno->id_job = $doc2job->id_job;
        $add_storno->id_document2job = $id;
        $add_storno->id_docaktivitaet = 6;
        $add_storno->id_benutzer = Auth::id();
        $add_storno->datetime = date('Y-m-d  H:i:s');
        $add_storno->save();
    }

    private function GetAgenturFreigabeHistory($docId)
    {
        return DokFreigabeA::leftJoin('users', 'dok_freigabe_agentur.id_user', '=', 'users.id')
            ->Where('dok_freigabe_agentur.id_document2jobs', '=', $docId)
            ->select('dok_freigabe_agentur.created_at', 'users.vorname', 'users.name')
            ->get();
    }

    private function GetKundenFreigabeHistory($docId)
    {
        return DokFreigabeK::leftJoin('users', 'dok_freigabe_kunde.id_user', '=', 'users.id')
            ->Where('dok_freigabe_kunde.id_document2jobs', '=', $docId)
            ->select('dok_freigabe_kunde.created_at', 'users.vorname', 'users.name')
            ->get();
    }

    private function GetDocAktivitaeten($doc, $job_fuer_kunde)
    {
        $query = AktivitaetenDoc::leftJoin('docaktivitaeten', 'dokument_aktivitaeten.id_docaktivitaet', '=', 'docaktivitaeten.id')
            ->leftJoin('users', 'dokument_aktivitaeten.id_benutzer', '=', 'users.id')
            ->Where('dokument_aktivitaeten.id_document2job', '=', $doc->id)
            ->select('dokument_aktivitaeten.created_at', 'users.vorname', 'users.name', 'docaktivitaeten.taetigkeit_kunde');

        if (!(User::current()->is_agentur()) && !empty($job_fuer_kunde->afreigabedatum)) {
            $query = $query->Where('dokument_aktivitaeten.created_at', '>', $job_fuer_kunde->afreigabedatum);
        }
        return $query->get();
    }

    private function GetJobFuerKundenFreigabe($id, $job_fuer_kunde)
    {
        return Job::leftJoin('users', 'jobs.id_afreigabeuser', '=', 'users.id')
            ->Where('jobs.id', '=', $id)
            ->Where('jobs.afreigabedatum', '=', $job_fuer_kunde->afreigabedatum)
            ->select('jobs.afreigabedatum', 'users.vorname', 'users.name')
            ->first();
    }

}
