<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return redirect('dashboard');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function sendPasswordlink(Request $request)
    {
        $messages = array(
            'user.required' => 'Bitte geben Sie Ihre E-Mail-Adresse an.',
            'user.email' => 'Es wurde keine gültige E-Mailadresse angegeben.',

        );

        $validator = Validator::make($request->all(), [
            'user' => 'required|email',
        ], $messages);
        if ($validator->fails()) {
            session(['error' => $validator->errors()->first()]);
            return redirect()->back()->withInput();
        }

        $userdata['username'] = $request->user;
        $res = $this->getPasswortmail($userdata);
        if ($res == '1') {
            session(['error' => '']);
            session(['succsess' =>
                Lang::get('Sie erhalten in Kürze eine E-Mail an die angegebene Adresse, bitte schauen Sie auch in Ihrem Spam-Ordner nach.')]);
        } else { // TODO: this should display some error most likely
            session(['error' => '']);
            session(['succsess' =>
                Lang::get('Sie erhalten in Kürze eine E-Mail an die angegebene Adresse, bitte schauen Sie auch in Ihrem Spam-Ordner nach.')]);
        }
        return redirect('forgotpassword');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy()
    {
        auth()->logout();
        session()->flush('message', 'Logged out.');
        return redirect('/login');
    }

    /**
     * Display the specified resource.
     *
     * @param array $id
     * @return \Illuminate\Http\Response
     */
    public function getPasswortmail($userdata)
    {
        $result = UserController::sendResetMail($userdata['username']);
        return $result;
    }
}
