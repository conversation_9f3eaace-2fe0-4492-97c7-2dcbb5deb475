<?php

namespace App\Http\Controllers;

use App\Exceptions\ResponseApiException;
use App\Models\Job;
use App\Models\Log as LogDB;
use App\Models\Transmits;
use App\Models\TSoAuth;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ResponseController extends Controller
{
    /**
     * Enpunkt zum Import von Adressdaten per Payload per JSON
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setResponse(Request $request, $recordId)
    {
        $this->checktoken($request);
        $payload = json_decode($request->getContent());
        if (!isset($payload->isArchived)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'isArchived missing'], 400);
        }
        if (!isset($recordId)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'recordId missing'], 400);
        }
        return $this->setRecord(
            $request,
            $recordId,
            $payload->isArchived,
            (!empty($payload->failureType) ? $payload->failureType : null)
        );
    }

    /**
     * Endpunkt zum Überprüfen des Tokens
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checktoken(Request $request)
    {
        try {
            $this->setUpUser($request->bearerToken());
        } catch (ResponseApiException $e) {
            return response()->json($e->getMessage(), 401);
        }
        return response()->json('true', 200);
    }

    /**
     * @param $token
     * @throws ResponseApiException
     */
    private function setUpUser($token)
    {
        if (!$token) {
            throw new ResponseApiException('token missing');
        }
        $this->user = TSoAuth::where('expires_at', '>', now())->where('api_token', $token)->first();
        if (!$this->user) {
            throw new ResponseApiException('token expired', 500);
        }
    }

    /**
     * Import
     *
     * @param Request $request
     * @param $id
     * @param string $fileName
     * @param string $content
     * @param $importId
     * @return \Illuminate\Http\JsonResponse
     */
    private function setRecord($request, $recordId, $status, $failureType = null)
    {
        try {
            $this->setUpUser($request->bearerToken());
            $record = Transmits::find($recordId);
            $record->failureType = null;
            if (!$record) {
                Log::error("Received invalid answer from records management recordID " . $recordId . ", status: " . $status);
                return response()->json(['errorCode' => '400', 'errorMessage' => 'record not found'], 400);
            }
            $this->log(json_encode(['recordID' => $recordId, 'status' => $status]));
            $record->id_status = 0;
            if ($status) {
                $record->id_status = 1;
            }
            $record->antwort_am = now();
            Log::debug("Received valid answer from records management recordID " . $recordId . ", status: " . $status);
            if(!empty($failureType)){
                Log::debug("Received failure. Failure type: " . $failureType);
                $record->failureType = $failureType;
            }
            $result = $record->save();
            if ($result) {
                return response()->json('Accepted', 202);
            }
        } catch (ResponseApiException $e) {
            return response()->json($e->getMessage(), 401);
        }
    }

    private function log($debug)
    {
        $log = new LogDB();
        $log->datetime = now();
        $log->debug = $debug;
        return $log->save();
    }
}
