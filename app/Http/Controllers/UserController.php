<?php

namespace App\Http\Controllers;

use App\Mail\Aktion;
use App\Mail\Angelegt;
use App\Mail\Delete;
use App\ModelHelper\Mailer;
use App\Models\BenutzerA;
use App\Models\BenutzerD;
use App\Models\BenutzerK;
use App\Models\Dienstleister;
use App\Models\Ersteller;
use App\Models\Kunde;
use App\Models\TSoAuth;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use http\Env\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function getcustomerusers($kunde)
    {
        $kuser = $this->get_kunden_user($kunde);
        session()->put(['id_kunde' => $kunde]);
        return $kuser;
    }

    public function getcustomerusersshort($kunde)
    {
        $kuser = $this->get_kunden_user_short($kunde);
        session()->put(['id_kunde' => $kunde]);
        return $kuser;
    }

    public function showcustomer($id)
    {
        if (session('userdata')['rechte']['typ'] == 'ersteller') {
            $kunden = Kunde::orderBy('kunde')->get();
            foreach ($kunden as $kunde) {
                $kunden_ausgabe[$kunde->id] = $kunde;
            }
            return view('kunden', ['kunden' => $kunden_ausgabe, 'id' => $id]);
        }

        return redirect('dashboard');
    }

    public function get_service_provider($id)
    {
        if (session('userdata')['rechte']['typ'] == 'ersteller') {
            $dienstleister = Dienstleister::orderBy('dienstleister')->get();
            $tmp_dienstleister = [];
            foreach ($dienstleister as $dienstleister_single) {
                $tmp_dienstleister[$dienstleister_single->id] = ['dienstleister' => $dienstleister_single->dienstleister, 'aktiv' => $dienstleister_single->aktiv, 'farbe' => $dienstleister_single->farbe];
            }
            return view('dienstleister', ['dienstleister' => $tmp_dienstleister, 'id' => $id]);
        }
        return redirect('dashboard');
    }

    public function get_clients()
    {
        $tmp_kunde = [];
        if (session('userdata')['rechte']['typ'] == 'ersteller' && session('userdata')['superuser_kunden'] == 1) {
            $kunden = Kunde::orderBy('kunde')->get();
            foreach ($kunden as $kunde) {
                $tmp_kunde[$kunde->id] = ['loeschtage' => $kunde->loeschtage, 'kunde' => $kunde->kunde, 'aktiv' => $kunde->aktiv];
            }
            return view('kunden', ['kunden' => $tmp_kunde]);
        }
        return redirect('dashboard');
    }

    public function get_service_providers()
    {
        if (session('userdata')['rechte']['typ'] == 'ersteller' && session('userdata')['superuser_dienstleister'] == 1) {
            $dienstleister = Dienstleister::orderBy('dienstleister')->get();
            $tmp_dienstleister = [];
            foreach ($dienstleister as $dienstleister_single) {
                $tmp_dienstleister[$dienstleister_single->id] = ['dienstleister' => $dienstleister_single->dienstleister, 'aktiv' => $dienstleister_single->aktiv, 'farbe' => $dienstleister_single->farbe];
            }
            return view('dienstleister', ['dienstleister' => $tmp_dienstleister]);
        }
        return redirect('dashboard');
    }

    public function return_user_from_dl($dienstleister)
    {
        $duser = $this->get_dienstleister_user($dienstleister);
        session()->put(['id_dienstleister' => $dienstleister]);
        return $duser;
    }

    public function return_shortuser_from_dl($dienstleister)
    {
        $duser = $this->get_dienstleister_user_short($dienstleister);
        session()->put(['id_dienstleister' => $dienstleister]);
        return $duser;
    }

    public function set_customer(Request $request)
    {
        if (session('userdata')['rechte']['typ'] == 'ersteller') {
            $check_kunde = Kunde::Where('kunde', $request->firma)->get()->toArray();
            if (!empty($check_kunde)) {
                session(['error' => 'Der Kunde existiert bereits.']);
                return redirect()->back();
            }

            $messages = array(
                'firma.required' => 'Der Kunde muss angegeben werden.',
                'loeschtage.numeric' => 'Die Anzahl der Tage bis zur Löschung muss eine Zahl sein.'
            );
            $validator = Validator::make($request->toArray(), [
                'firma' => 'required',
                'loeschtage' => 'numeric',
            ], $messages);


            if ($validator->fails()) {
                session(['error' => $validator->errors()->first()]);
                return redirect()->back();
            }

            $kunde = new KUNDE;
            $kunde->kunde = $request->firma;
            $kunde->aktiv = (int) ($request->aktiv == 'on');
            $kunde->loeschtage = $request->loeschtage;
            $kunde->farbe = $request->farbe;
            $kunde->save();

            $kunde_tmp = KUNDE::orderBy('kunde')->get();
            return view('kunden', ['kunden' => $kunde_tmp->toArray()]);
        }
        return redirect('dashboard');
    }

    public function set_customer_by_id(Request $request)
    {
        if (session('userdata')['rechte']['typ'] == 'ersteller') {
            if (!empty($request->k_id)) {
                $aktiv = (int) (!empty($request->aktiv) && $request->aktiv == 'on');
                $kunde = Kunde::find($request->k_id);
                $messages = array(
                    'firma.required' => 'Der Name muss angegeben werden.',
                    'loeschtage.numeric' => 'Die Anzahl der Tage bis zur Löschung muss eine Zahl sein.'
                );
                $validator = Validator::make($request->toArray(), [
                    'firma' => 'required',
                    'loeschtage' => 'numeric',
                ], $messages);

                if ($validator->fails()) {
                    session(['error' => $validator->errors()->first()]);
                    return redirect()->back();
                }

                $kunde->kunde = $request->firma;
                $kunde->loeschtage = $request->loeschtage;
                $kunde->farbe = $request->farbe;
                $kunde->aktiv = $aktiv;
                $kunde->save();
            }
            $kunden = KUNDE::orderBy('kunde')->get();
            foreach ($kunden as $kunde) {
                $kunden_ausgabe[$kunde->id] = $kunde;
            }

            return view('kunden', ['kunden' => $kunden_ausgabe, 'id' => $request->k_id]);
        }
        return redirect('dashboard');
    }


    public function set_dl_by_id(Request $request, $id)
    {
        if (session('userdata')['rechte']['typ'] != 'ersteller') {
            return redirect('dashboard');
        }

        if (!empty($request->d_id)) {
            $aktiv = (int) (!empty($request->aktiv) && $request->aktiv == 'on');
            $dienstleister = Dienstleister::find($request->d_id);
            $messages = array(
                'firma.required' => 'Der Name muss angegeben werden.',
                'loeschtage.numeric' => 'Der Tage müssen numerisch sein.'
            );
            $validator = Validator::make($request->toArray(), [
                'firma' => 'required',
                'loeschtage' => 'numeric',
            ], $messages);

            if ($validator->fails()) {
                session(['error' => $validator->errors()->first()]);
                return redirect()->back();
            }

            $dienstleister->dienstleister = $request->firma;
            $dienstleister->farbe = $request->farbe;
            $dienstleister->aktiv = $aktiv;
            $dienstleister->save();
        }
        $dienstleister = Dienstleister::orderBy('dienstleister')->get();
        foreach ($dienstleister as $dl) {
            $dl_ausgabe[$dl->id] = $dl;
        }

        return view('dienstleister', ['dienstleister' => $dl_ausgabe, 'id' => $request->d_id]);
    }

    public function set_basic_data_by_id($id)
    {
        session()->forget('error_message');
        session()->forget('error');
        session()->forget('succsess');
        if (!empty($request['loeschen'])) {
            $user_id = $request['del_id'];
            if (!empty($user_id)) {
                $this->delete_user($user_id);
            }
            session()->forget('id_dienstleister');
            session()->forget('id_kunde');
            return redirect('stammdaten');
        }

        if (!empty($request['email'])) {
            $email = $request['email'];
            $geschlecht = $request['geschlecht'];
            $name = $request['name'];
            $vorname = $request['vorname'];
            $user_id = $request['id'];
            $typ = $request['typ'];
            $id_kunde = "";
            if (!empty($request['id_kunde'])) {
                $id_kunde = $request['id_kunde'];
            }

            $id_agentur = "";
            if (!empty($request['id_agentur'])) {
                $id_agentur = $request['id_agentur'];
            }

            $id_dienstleister = "";
            if (!empty($request['id_dienstleister'])) {
                $id_dienstleister = $request['id_dienstleister'];
            }

            session(['stammdaten' => ['id_kunde' => $id_kunde, 'id_agentur' => $id_agentur]]);
            $userdata = [
                'user_id' => $user_id,
                'email' => $email,
                'geschlecht' => $geschlecht,
                'name' => $name,
                'vorname' => $vorname,
                'typ' => $typ,
                'id_kunde' => $id_kunde,
                'id_agentur' => $id_agentur,
                'id_dienstleister' => $id_dienstleister,
            ];
            $messages = array(
                'name.required' => 'Der Name muss angegeben werden.',
                'email.required' => 'Die Emailadresse muss angegeben werden.',
                'geschlecht.required' => 'Bitte geben Sie die Anrede an.',
                'email.email' => 'Die Emailadresse muss gültig sein.',
            );

            $validator = Validator::make($userdata, [
                'name' => 'required',
                'geschlecht.required' => 'Bitte geben Sie die Anrede an.',
                'email' => 'required|email'
            ], $messages);


            if ($validator->fails()) {
                session(['error' => $validator->errors()->first()]);
                return redirect()->back();
            }


            $user_insert = $this->insert_user($userdata, $user_id);
            session(['succsess' => 'Der Benutzer wurde erfolgreich gespeichert.']);

            return redirect('stammdaten/' . $id);
        }
    }

    public function set_basis_data(Request $request)
    {
        session()->forget('error_message');
        session()->forget('error');
        session()->forget('succsess');
        $user_id = $request['id'];

        if (!empty($request['del_id'])) {
            $user_id = $request['del_id'];
            if (!empty($user_id)) {
                $this->delete_user($user_id);
            }
            session()->forget('id_dienstleister');
            session()->forget('id_kunde');
            return redirect('stammdaten');
        }

        $email = $request['email'];

        $exists_tmp = false;
        if (!empty($email) && !empty($user_id)) {
            $exists_tmp = User::where('email', $email)->Where('id', $user_id)->get()->toArray();
        }

        $exists = 0;
        if (!$exists_tmp) {
            $exists = User::where('email', $email)->get()->toArray();
        }

        if (!empty($exists)) {
            session(['error' => 'Der Benutzer existiert bereits!']);
            return redirect()->back()->withInput();
        }

        $geschlecht = $request['geschlecht'];
        $name = $request['name'];
        $vorname = $request['vorname'];
        $user_id = $request['id'];
        $typ = $request['typ'];
        $id_kunde = "";
        if (!empty($request['id_kunde'])) {
            $id_kunde = $request['id_kunde'];
        }

        $id_agentur = "";
        if (!empty($request['id_agentur'])) {
            $id_agentur = $request['id_agentur'];
        }

        $id_dienstleister = "";
        if (!empty($request['id_dienstleister'])) {
            $id_dienstleister = $request['id_dienstleister'];
        }

        session(['stammdaten' => ['typ' => $request['typ']]]);
        $userdata = [
            'user_id' => $user_id,
            'email' => $email,
            'geschlecht' => $geschlecht,
            'name' => $name,
            'vorname' => $vorname,
            'typ' => $typ,
            'id_kunde' => $id_kunde,
            'id_agentur' => $id_agentur,
            'id_dienstleister' => $id_dienstleister,
        ];

        $messages = array(
            'name.required' => 'Der Name muss angegeben werden.',
            'email.required' => 'Die E-Mail-Adresse muss angegeben werden.',
            'geschlecht.required' => 'Bitte geben Sie die Anrede an.',
            'email.email' => 'Die E-Mail-Adresse muss gültig sein.',
        );

        $validator = Validator::make($userdata, [
            'name' => 'required',
            'geschlecht' => 'required',
            'email' => 'required|email:rfc,dns,filter'
        ], $messages);

        if ($validator->fails()) {
            session(['error' => $validator->errors()->first()]);
            return redirect()->back()->withInput();
        }

        if (empty($user_id)) {
            $user_insert = $this->insert_user($userdata, $user_id);
        } else {
            $user_insert = $this->update_user($userdata, $user_id);
        }

        if (!empty($user_insert)) {
            session(['succsess' => 'Der Benutzer wurde erfolgreich gespeichert.']);
        }


        return redirect('stammdaten/' . $user_insert);
    }

    public function get_basic_data_by_id($id)
    {
        if (session('userdata')['rechte']['typ'] != 'ersteller') {
            redirect('dashboard');
        }

        $aktiv['kunde'] = 0;
        $aktiv['agentur'] = 0;
        $aktiv['dienstleister'] = 0;
        $userinfo = $this->index();
        $kunden = $this->get_kunden();
        $dienstleister = $this->get_dienstleister();

        $user = User::find($id);
        if ($user->is_dienstleister()) {
            $duserinfo = $this->get_userFromdienstleister($id);
        } else if ($user->is_kunde()) {
            $kuserinfo = $this->get_userFromkunde($id);
        }


        $id_kunde = "";
        if (!empty($request['id_kunde'])) {
            $id_kunde = $request['id_kunde'];
        }

        $id_dienstleister = "";
        if (!empty($request['id_dienstleister'])) {
            $id_dienstleister = $request['id_dienstleister'];
        }
        if (!empty($duserinfo)) {
            $aktiv['dienstleister'] = $id_dienstleister;
        } elseif (!empty($kuserinfo)) {
            $aktiv['kunde'] = $id_kunde;
        } else {
            $aktiv['agentur'] = 1;
        }


        return view('stammdaten', ['userinfos' => $userinfo, 'selected' => $id, 'kunden' => $kunden, 'dienstleister' => $dienstleister, 'aktiv' => $aktiv]);
    }

    public function get_basic_data()
    {
        if (session('userdata')['rechte']['typ'] != 'ersteller') {
            return redirect('dashboard');
        }

        $aktiv['kunde'] = 0;
        $aktiv['agentur'] = 0;
        $aktiv['dienstleister'] = 0;

        $userinfo = $this->index();
        $kunden = $this->get_kunden();
        $dienstleister = $this->get_dienstleister();

        if (!empty($_GET['id'])) {
            $id = $_GET['id'];

            if (!empty($_GET['id_dienstleister'])) {
                $aktiv['dienstleister'] = $_GET['id_dienstleister'];
            } elseif (!empty($_GET['id_kunde'])) {
                $aktiv['kunde'] = $_GET['id_kunde'];
            } else {
                $aktiv['agentur'] = 1;
            }
            return view('stammdaten', ['userinfos' => $userinfo, 'selected' => $id, 'kunden' => $kunden, 'dienstleister' => $dienstleister, 'aktiv' => $aktiv]);
        }

        return view('stammdaten', ['userinfos' => $userinfo, 'selected' => '', 'kunden' => $kunden, 'dienstleister' => $dienstleister, 'aktiv' => $aktiv]);
    }


    public function call_reset_passwd($token)
    {
        return view('resetpasswd', ['token' => $token]);
    }


    public function set_new_passwd($token, Request $request)
    {
        $reset = $this->resetpasswd($request);
        return $reset;
    }

    public function call_initial_passwd($token)
    {
        return view('initpasswd', ['token' => $token]);
    }

    public function set_dl(Request $request)
    {
        if (session('userdata')['rechte']['typ'] != 'ersteller') {
            return  redirect('dashboard');
        }

        if (empty($request->d_id)) {
            $check_dienstleister = Dienstleister::Where('dienstleister', $request->firma)->get()->toArray();
            if (!empty($check_dienstleister)) {
                session(['error' => 'Der Dienstleister existiert bereits.']);
                return redirect()->back();
            }

            $aktiv = (int)(!empty($request->aktiv) && $request->aktiv == 'on');
            $dienstleister = new Dienstleister();
            $messages = array(
                'firma.required' => 'Der Name muss angegeben werden.',
                'loeschtage.numeric' => 'Der Tage müssen numerisch sein.'
            );
            $validator = Validator::make($request->toArray(), [
                'firma' => 'required',
                'loeschtage' => 'numeric',
            ], $messages);

            if ($validator->fails()) {
                session(['error' => $validator->errors()->first()]);
                return redirect()->back();
            }

            $dienstleister->dienstleister = $request->firma;
            $dienstleister->farbe = $request->farbe;
            $dienstleister->aktiv = $aktiv;
            $dienstleister->save();
        }

        $dienstleister = Dienstleister::orderBy('dienstleister')->get();
        foreach ($dienstleister as $dl) {
            $dl_ausgabe[$dl->id] = $dl;
        }

        return view('dienstleister', ['dienstleister' => $dl_ausgabe]);
    }

    public function index()
    {
        $result = [];
        $alluser = User::where('aktiv', 1)->orderBy('vorname', 'asc')->orderBy('name', 'asc')->get();
        foreach ($alluser as $user) {
            if (User::find($user->id)->is_agentur()) {
                $result['agentur'][] = $user->toArray();
            } else if (User::find($user->id)->is_kunde()) {
                $result['kunde'][] = $user->toArray();
            } else if (User::find($user->id)->is_dienstleister()) {
                $result['dienstleister'][] = $user->toArray();
            }
        }
        return $result;
    }

    public function show($id)
    {
        $payload = "";
        $jobkunden = $this->getCall('/jobkunden/' . $id, $payload);
        $jobagentur = $this->getCall('/jobagentur/' . $id, $payload);
        $jobdienstleister = $this->getCall('/jobdienstleister/' . $id, $payload);
    }

    public static function get_kunden($id = 0)
    {
        return Kunde::where('aktiv', 1)->orderBy('kunde', 'asc')->get();
    }

    public static function get_kunden_user($id)
    {
        $kuser = Kunde::find($id);
        if ($kuser) {
            return $kuser->user();
        }
    }

    public static function get_agentur_user($id)
    {
        $auser = Ersteller::find($id);
        return $auser->user();
    }

    public function get_dienstleister_user($id)
    {
        $duser = Dienstleister::find($id);
        if (!empty($duser)) {
            return ($duser->user());
        }
        return null;
    }

    public function get_dienstleister_user_short($id)
    {
        $payload = "";
        return $this->getCall('/dienstleisterusershort/' . $id, $payload);
    }

    public function get_kunden_user_short($id)
    {
        $payload = "";
        return $this->getCall('/kundenusershort/' . $id, $payload);
    }

    public function get_userFromdienstleister($id)
    {
        $user = null;
        $dienstleister = Dienstleister::find($id);
        if (!empty($dienstleister)) {
            $user = $dienstleister->user();
        }
        return $user;
    }

    public function get_userFromkunde($id)
    {
        $user = null;
        $kunde = Kunde::find($id);

        if (!empty($kunde)) {
            $user = $kunde->user();
        }
        return $user;
    }

    public static function get_dienstleister($id = '')
    {
        return Dienstleister::where('aktiv', 1)->orderBy('dienstleister', 'asc')->get();
    }

    public function sendAnlegenMail($email_address, $token, $name)
    {
        return Mail::to($email_address)->send(new Angelegt($email_address, $token, $name));
    }

    public function update_user($request, $id)
    {
        if (!empty($request)) {
            $user = User::find($id);
            $user->name = $request['name'];
            $user->vorname = $request['vorname'];
            $user->email = $request['email'];
            $user->anrede = $request['geschlecht'];
            $user->save();
        }
        return $id;
    }

    public function insert_user($request, $id)
    {
        if (!empty($request)) {
            $token = md5($request['name'] . time());
            $ablaufdatum = Carbon::now()->addHours(24);
            $user = new User;
            $user->name = $request['name'];
            $user->vorname = $request['vorname'];
            $user->email = $request['email'];
            $user->anrede = $request['geschlecht'];
            $user->passwordreset_token = $token;
            $result = $user->save();

            $insert_id = DB::getPdo()->lastInsertId();


            if ($request['id_agentur']) {
                $zuordnung = new BenutzerA;
                $zuordnung->id_ersteller = $request['id_agentur'];
                $zuordnung->id_benutzer = $insert_id;
                $zuordnung->save();
            } else if ($request['id_kunde']) {
                $zuordnung = new BenutzerK;
                $zuordnung->id_kunde = $request['id_kunde'];
                $zuordnung->id_benutzer = $insert_id;
                $zuordnung->save();
            } else if ($request['id_dienstleister']) {
                $zuordnung = new BenutzerD;
                $zuordnung->id_dienstleister = $request['id_dienstleister'];
                $zuordnung->id_benutzer = $insert_id;
                $zuordnung->save();
            }
        }
        if ($result) {
            $maildata = array('email' => $request['email'], 'vorname' => $request['vorname'], 'name' => $request['name'], 'datum' => $ablaufdatum->format('d.m.Y'), 'uhrzeit' => $ablaufdatum->format('H:i') . " Uhr", 'link' => config('app.APP_URL') . 'initpasswd/' . $token);
            Mailer::sendMailByTemplate('EMAIL_BENUTZER_ANLAGE', $maildata);
        } else {
            session(['error_message' => $result]);
        }
        return $insert_id;
    }

    public function delete_user($id)
    {
        $user = User::find($id);

        $user->aktiv = 0;
        $user->password = null;
        $user->email = time() . $user->email;

        $user->save();
    }

    public function get_token(Request $request)
    {

        $user = urldecode($request->getUser());
        if (empty($user)) {
            return response()->json(['errorCode' => '401', 'errorMessage' => 'user missing'], 401);
        }
        $password = urldecode($request->getPassword());
        if (empty($password)) {
            return response()->json(['errorCode' => '401', 'errorMessage' => 'password missing'], 401);
        }

        try {
            $api_user = TSoAuth::Where('client_user', $user)->Where('client_secret', $password)->first();
            if (empty($api_user)) {
                return response()->json(['errorCode' => '401', 'errorMessage' => 'unauthorized'], 401);
            }

            $now = Carbon::now();
            $token_time = Carbon::parse($api_user->expires_at);
            $expires_in = $now->diffInSeconds($token_time, false);
            $token = $api_user->api_token;
            if ($expires_in <= 0) {
                $token = hash('sha256', Str::random(60));
                $api_user->api_token = $token;
                $api_user->expires_at = $now->addSeconds(3600);
                $api_user->save();
                $expires_in = 3600;
            }
        } catch (Exception $e) {
            return response()->json($e->getMessage(), 401);
        }


        return ['access_token' => $token, 'token_type' => 'Bearer', 'expires_in' => $expires_in];
    }

    public static function sendResetMail($email_address)
    {
        $token = md5(config('security.SALT') . microtime());

        $user = User::WHERE(['email' => $email_address, 'aktiv' => 1])->first();
        if (!empty($user) && $user->name != '') {
            $success = User::Where('email', $email_address)->update(['passwordreset_token' => $token]);
            $maildata = array(
                'email' => $email_address,
                'vorname' => $user->vorname,
                'name' => $user->name,
                'datum' => Carbon::now()->addDay()->format('d.m.Y'),
                'uhrzeit' => Carbon::now()->addDay()->format('H:i'),
                'link' => config('app.APP_URL') . 'resetpasswd/' . $token
            );

            //$mailer = new Mailer($user, 'EMAIL_PW_RESET');
            Mailer::sendMailByTemplate('EMAIL_PW_RESET', $maildata);
            return 1;
        }
        return 0;
    }

    public function resetpasswd(Request $request)
    {
        $user = User::Where('passwordreset_token', $request->token)->first();
        if (!$user) {
            session(['error' => 'Der Link zum Zurücksetzen ist nicht gültig.']);
            return redirect()->back()->withInput();
        }

        $time = Carbon::createFromFormat('Y-m-d H:i:s', $user['updated_at']);
        $time->add(24, 'hour');
        if ($time < Carbon::now()) {
            session(['error' => '<span style="color: #FF0000;">Wichtiger Hinweis</span>: Ihr Link zur Vergabe des Passworts ist leider abgelaufen. Bitte fordern Sie sich <a href="' . config('app.APP_URL') . '/forgotpassword">hier</a> einen neuen Link ab.']);
            return redirect()->back()->withInput();
        }

        $messages = array(
            'passwd.required' => 'Das Passwort muss eingegeben werden.',
            'passwd_wdh.required' => 'Die Passwortwiederholung muss eingegeben werden.',
            'passwd_wdh.same' => 'Die Passwörter stimmen nicht überein.'
        );

        $validator = Validator::make($request->all(), [
            'passwd' => 'required',
            'passwd_wdh' => 'required|same:passwd'
        ], $messages);

        if ($validator->fails()) {
            session(['error' => $validator->errors()->first()]);
            return redirect()->back()->withInput();
        }
        if (!empty($request)) {
            $new_token = md5(config('security.SALT') . microtime());
            $insert = User::where(['passwordreset_token' => $request->token])
                ->where(['aktiv' => 1])
                ->update(['password' => Hash::make($request->passwd), 'passwordreset_token' => $new_token]);
            $maildata = array('email' => $user->email, 'vorname' => $user->vorname, 'name' => $user->name, 'link' => config('app.APP_URL') . 'projekte/');
            Mailer::sendMailByTemplate('EMAIL_PW_AENDERUNG_OK', $maildata);
            return redirect('login');
        }
    }


    public function sendAktionsMail($user, $aktion)
    {
        if (!empty($user->email)) {
            return Mail::to($user->email)->send(new Aktion($user, $aktion));
        }
    }

    public function sendDeleteMail($user, $jobs, $from, $to)
    {
        return  Mail::to($user->email)->send(new Delete($user, $jobs, $from, $to));
    }

    public static function getUserName($id_user)
    {
        $user = User::find($id_user);
        return $user->vorname . ' ' . $user->name;
    }

    public static function getSessionStatus()
    {
        $userId = Auth::id();
        if(!empty($userId)){
            return response()->json(['session' => true], 200);
        }
        else {
            return response()->json(['session' => false], 401);
        }
    }
}
