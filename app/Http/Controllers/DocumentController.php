<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\Image;
use App\Models\Color;
use App\Models\ImageTag;
use App\Models\ImageAssignment;
use Imagick;

class DocumentController extends Controller
{
    public function storeHtml(Request $file)
    {
        $content = $file->getContent();
        $path = config('app.HTML_DIR');
        $hash = hash('sha256', $content);
        $newFileName = $hash;
        $newFileNameWithPath = $path . '/' . $newFileName;
        $result = false;
        if (Storage::disk('local')->exists($newFileNameWithPath)) {
            return response($newFileName, 200);
        }

        $result = Storage::disk('local')->put($newFileNameWithPath, $content);
        if ($result) {
            return response($newFileName, 202);
        }
        return response(null, 400);
    }

    public function storeJson(Request $file)
    {
        $content = $file->getContent();
        $path = config('app.JSON_DIR');
        $hash = hash('sha256', $content);
        $newFileName = $hash;
        $newFileNameWithPath = $path . '/' . $newFileName;
        $result = false;

        if (Storage::disk('local')->exists($newFileNameWithPath)) {
            return response($newFileName, 200);
        }

        $result = Storage::disk('local')->put($newFileNameWithPath, $content);
        if ($result) {
            return response($newFileName, 202);
        }

        return response(null, 400);
    }

    public function storeImage(Request $file)
    {
        Log::info('Starting storeImage method');
        try {
            // Check if content can be retrieved
            try {
                $content = $file->getContent();
                Log::info('File content retrieved, size: ' . strlen($content));
            } catch (\Exception $e) {
                Log::error('Failed to get content from request: ' . $e->getMessage());
                return response('Failed to get content from request: ' . $e->getMessage(), 400);
            }

            // Check Content-Disposition header
            $fileNameTmp = $file->header('Content-Disposition');
            Log::info('Content-Disposition header: ' . $fileNameTmp);

            preg_match('/filename="([^"]+)"/', $fileNameTmp, $matches);
            if (!empty($matches[1])) {
                $fileName = $matches[1];
                Log::info('Extracted filename: ' . $fileName);
            } else {
                Log::error('Missing filename in header');
                return response('Missing filename in header. Content-Disposition: ' . $fileNameTmp, 400);
            }

            // Setup file paths
            $path = config('app.IMAGE_DIR');

            // Check if base image directory exists and is writable
            if (!file_exists(storage_path('app/' . $path))) {
                Log::info('Creating base image directory');
                if (!mkdir(storage_path('app/' . $path), 0755, true)) {
                    Log::error('Failed to create base image directory');
                    return response('Failed to create image directory', 500);
                }
            }

            // Generate file name
            $hash = hash('sha256', $content);
            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
            $newFileName = $hash . '.' . $ext;
            $newFileNameWithPath = $path . '/' . $newFileName;
            $newFileNameWithThumbPath = $path . '/thumb/' . $newFileName;

            Log::info('File info', [
                'path' => $path,
                'hash' => $hash,
                'ext' => $ext,
                'newFileName' => $newFileName
            ]);

            // Create thumb directory if it doesn't exist
            if (!file_exists(storage_path('app/' . $path . '/thumb/'))) {
                Log::info('Creating thumb directory');
                if (!mkdir(storage_path('app/' . $path . '/thumb/'), 0755, true)) {
                    Log::error('Failed to create thumb directory');
                    return response('Failed to create thumb directory', 500);
                }
            }

            // Return existing file if it already exists
            if (Storage::disk('local')->exists($newFileNameWithPath)) {
                Log::info('File already exists, returning existing filename');
                return response($newFileName, 200);
            }

            // Store the file
            Log::info('Storing file: ' . $newFileNameWithPath);
            try {
                $result = Storage::disk('local')->put($newFileNameWithPath, $content);
            } catch (\Exception $e) {
                Log::error('Failed to store file: ' . $e->getMessage());
                return response('Failed to store file: ' . $e->getMessage(), 500);
            }

            if ($result) {
                Log::info('File stored successfully');

                // Check if Imagick extension is available for thumbnail generation
                if (extension_loaded('imagick')) {
                    Log::info('Imagick extension is loaded, creating thumbnail');
                    try {
                        $im = new Imagick(storage_path('app/' . $newFileNameWithPath));
                        $imageprops = $im->getImageGeometry();
                        $width = $imageprops['width'];
                        $height = $imageprops['height'];
                        $newWidth = 200;
                        $newHeight = (200 / $width) * $height;

                        Log::info('Resizing image', [
                            'original' => "$width x $height",
                            'new' => "$newWidth x $newHeight"
                        ]);

                        $im->resizeImage($newWidth, $newHeight, imagick::FILTER_LANCZOS, 0.9, true);
                        $im->writeImage(storage_path('app/' . $newFileNameWithThumbPath));
                    } catch (\Exception $e) {
                        Log::warning('Failed to create thumbnail, but continuing: ' . $e->getMessage());
                        // Copy original as thumbnail as fallback
                        Storage::disk('local')->copy($newFileNameWithPath, $newFileNameWithThumbPath);
                    }
                } else {
                    Log::warning('Imagick extension is not loaded, skipping thumbnail creation');
                    // Copy original as thumbnail as fallback
                    Storage::disk('local')->copy($newFileNameWithPath, $newFileNameWithThumbPath);
                }

                // Save record to database
                try {
                    $imageFile = new Image();
                    $imageFile->jobId = $file->input('jobId', 0); // Get jobId from request or default to 0
                    $imageFile->filename = $newFileName;
                    $imageFile->extension = $ext;
                    $imageFile->originalFileName = $fileName;
                    $imageFile->path = $path;

                    Log::info('Saving image record to database');
                    $imageFile->save();


                    // Handle folder assignments
                    $folderIds = [];
                    
                    // Check for single folderId header (backward compatibility)
                    if (!empty($file->header('folderId'))) {
                        $folderIds[] = (integer) $file->header('folderId');
                    }
                    
                    // Check for multiple folderIds header (comma-separated)
                    if (!empty($file->header('folderIds'))) {
                        $folderIdsString = $file->header('folderIds');
                        $additionalFolderIds = array_map('intval', explode(',', $folderIdsString));
                        $folderIds = array_merge($folderIds, $additionalFolderIds);
                    }
                    
                    // Remove duplicates
                    $folderIds = array_unique($folderIds);
                    
                    // Save folder assignments
                    foreach ($folderIds as $folderId) {
                        if ($folderId > 0) { // Only process valid positive IDs
                            try {
                                // Validate that the folder exists
                                $folder = ImageTag::find($folderId);
                                if (!$folder) {
                                    Log::warning("Folder not found with ID: $folderId, skipping assignment");
                                    continue;
                                }
                                
                                $imageAssignment = new ImageAssignment();
                                $imageAssignment->imageId = $imageFile->id;
                                $imageAssignment->imageTagId = $folderId;
                                $imageAssignment->save();
                                Log::info('Image folder assignment saved', ['imageId' => $imageFile->id, 'folderId' => $folderId]);
                            } catch (\Exception $e) {
                                Log::warning('Failed to save folder assignment: ' . $e->getMessage());
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to save image record to database: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
                    return response('Failed to save image record to database: ' . $e->getMessage(), 500);
                }

                return response($newFileName, 202);
            }

            Log::error('Failed to store file');
            return response('Failed to store file - unknown error', 400);
        } catch (\Exception $e) {
            Log::error('Exception in storeImage: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response('Error: ' . $e->getMessage(), 500);
        }
    }

    public function storeCss(Request $file)
    {
        $content = $file->getContent();
        $path = config('app.CSS_DIR');
        $hash = hash('sha256', $content);
        $newFileName = $hash;
        $newFileNameWithPath = $path . '/' . $newFileName;

        if (Storage::disk('local')->exists($newFileNameWithPath)) {
            return response($newFileName, 200);
        }

        $result = Storage::disk('local')->put($newFileNameWithPath, $content);
        if ($result) {
            return response($newFileName, 202);
        }

        return response(null, 400);
    }

    public function getImages()
    {
        $path = "/api/image/get";
        $thumbPath = "/api/thumb/get";

        $images = Image::orderBy('updated_at', 'DESC')->get();
        $imageArray = [];
        foreach ($images as $image) {

            $imageAssignments = ImageAssignment::where('imageId', $image->id)->get();
            $folders = [];
            foreach ($imageAssignments as $assignment) {
                $folders[] = $assignment->imageTagId;
            }

            $imageArray[] = [
                'id' => $image->id,
                'folders' => $folders,
                'thumb' => $thumbPath . '/' . $image->fileName,
                'image' => $path . '/' . $image->fileName,
                'originalName' => $image->originalFileName,
            ];

        }
        if (!empty($imageArray)) {
            return response()->json($imageArray, 200);
        }

        return response()->json(['error' => 'No Images found'], 404);
    }

    public function getColors()
    {
        $colors = Color::orderBy('sort', 'ASC')->get();
        $colorArray = [];
        foreach ($colors as $color) {

            $colorArray[] = [
                'name' => $color->name ?? null,
                'hex' => $color->hex ?? null,
                'rgb' => $color->rgb ?? null,
                'cmyk' => $color->cmyk ?? null,
            ];

        }
        if (!empty($colorArray)) {
            return response()->json($colorArray, 200);
        }

        return response()->json(['error' => 'No colors found'], 404);
    }

    public function getImageFolders()
    {
        $imageTags = ImageTag::orderBy('imageTag', 'ASC')->get();

        $folderArray = [];
        foreach ($imageTags as $imageTag) {
            $folderArray[] = [
                'id' => $imageTag->id ?? null,
                'folderName' => $imageTag->imageTag ?? null,
                'parentId' => $imageTag->parentId ?? null,
            ];
        }
        
        // Always return an array, even if empty - empty folders list is valid
        return response()->json($folderArray, 200);
    }

    public function createImageFolder(Request $request)
    {
        Log::info('Starting createImageFolder method');
        try {
            $data = $request->json()->all();
            
            if (!isset($data['folderName']) || trim($data['folderName']) === '') {
                Log::error('Invalid request: folderName is required');
                return response()->json(['error' => 'Folder name is required'], 400);
            }

            $folderName = trim($data['folderName']);
            $parentId = $data['parentId'] ?? null;

            // Check if folder with same name already exists at the same level
            $existingFolder = ImageTag::where('imageTag', $folderName)
                ->where('parentId', $parentId)
                ->first();

            if ($existingFolder) {
                Log::warning("Folder with name '$folderName' already exists at this level");
                return response()->json(['error' => 'A folder with this name already exists'], 409);
            }

            // If parentId is provided, validate that the parent exists
            if ($parentId !== null) {
                $parentFolder = ImageTag::find($parentId);
                if (!$parentFolder) {
                    Log::warning("Parent folder not found with ID: $parentId");
                    return response()->json(['error' => 'Parent folder not found'], 404);
                }
            }

            // Create the folder
            $imageTag = new ImageTag();
            $imageTag->imageTag = $folderName;
            $imageTag->parentId = $parentId;
            $imageTag->save();

            Log::info("Created folder '$folderName' with ID: {$imageTag->id}");

            return response()->json([
                'id' => $imageTag->id,
                'folderName' => $imageTag->imageTag,
                'parentId' => $imageTag->parentId,
                'message' => 'Folder created successfully'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Exception in createImageFolder: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to create folder: ' . $e->getMessage()], 500);
        }
    }

    public function deleteImageFolder($folderId)
    {
        Log::info('Starting deleteImageFolder method', ['folderId' => $folderId]);
        try {
            $folder = ImageTag::find($folderId);
            if (!$folder) {
                Log::warning("Folder not found with ID: $folderId");
                return response()->json(['error' => 'Folder not found'], 404);
            }

            // Check if folder has any child folders
            $childFolders = ImageTag::where('parentId', $folderId)->count();
            if ($childFolders > 0) {
                Log::warning("Cannot delete folder $folderId: has child folders");
                return response()->json(['error' => 'Cannot delete folder that contains subfolders'], 400);
            }

            // Check if folder has any images assigned
            $imageAssignments = ImageAssignment::where('imageTagId', $folderId)->count();
            if ($imageAssignments > 0) {
                Log::warning("Cannot delete folder $folderId: has assigned images");
                return response()->json(['error' => 'Cannot delete folder that contains images. Move images to another folder first.'], 400);
            }

            // Delete the folder
            $folderName = $folder->imageTag;
            $folder->delete();

            Log::info("Deleted folder '$folderName' with ID: $folderId");

            return response()->json([
                'message' => 'Folder deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            Log::error('Exception in deleteImageFolder: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to delete folder: ' . $e->getMessage()], 500);
        }
    }

    public function getImage($filenameHash)
    {
        $isThumb = strpos($filenameHash, 'thumb/') === 0;
        $basePath = config('app.IMAGE_DIR');

        if ($isThumb) {
            // Adjust path for thumbnail
            $path = $basePath . '/thumb';
            // Remove 'thumb/' prefix from filename
            $actualFilenameHash = substr($filenameHash, strlen('thumb/'));
            Log::info('Serving thumbnail', ['requested' => $filenameHash, 'actual' => $actualFilenameHash]);
        } else {
            $path = $basePath;
            $actualFilenameHash = $filenameHash;
            Log::info('Serving full image', ['requested' => $filenameHash]);
        }

        $fileNameWithPath = $path . '/' . $actualFilenameHash;
        Log::info('Attempting to access file', ['path' => $fileNameWithPath]);

        if (Storage::disk('local')->exists($fileNameWithPath)) {
            Log::info('File found', ['path' => $fileNameWithPath]);
            $content = Storage::disk('local')->get($fileNameWithPath);

            // Get file extension to set proper content type
            $extension = pathinfo($actualFilenameHash, PATHINFO_EXTENSION);
            $contentType = 'image/jpeg'; // Default

            // Map common extensions to MIME types
            $contentTypes = [
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp',
                'svg' => 'image/svg+xml',
                'bmp' => 'image/bmp'
            ];

            if (isset($contentTypes[strtolower($extension)])) {
                $contentType = $contentTypes[strtolower($extension)];
            } else {
                Log::warning('Unknown extension, using default content type', ['extension' => $extension]);
            }


            // Return the image with appropriate headers
            return response($content)
                ->header('Content-Type', $contentType)
                ->header('Content-Length', strlen($content));
        }

        Log::error('Image not found', ['path' => $fileNameWithPath]);
        // Return a 404 response when the image doesn't exist
        return response()->json(['error' => 'Image not found'], 404);
    }

    public function getThumbnail($filenameHash)
    {
        $path = config('app.IMAGE_DIR');
        $fileNameWithPath = $path . '/thumb/' . $filenameHash;
        if (Storage::disk('local')->exists($fileNameWithPath)) {
            $content = Storage::disk('local')->get($fileNameWithPath);

            // Get file extension to set proper content type
            $extension = pathinfo($filenameHash, PATHINFO_EXTENSION);
            $contentType = 'image/jpeg'; // Default

            // Map common extensions to MIME types
            $contentTypes = [
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp',
                'svg' => 'image/svg+xml',
                'bmp' => 'image/bmp'
            ];

            if (isset($contentTypes[strtolower($extension)])) {
                $contentType = $contentTypes[strtolower($extension)];
            }

            // Return the image with appropriate headers
            return response($content)
                ->header('Content-Type', $contentType)
                ->header('Content-Length', strlen($content));
        }

        // Return a 404 response when the image doesn't exist
        return response()->json(['error' => 'Thumbnail not found'], 404);
    }

    public function getHtml($filenameHash)
    {
        $path = config('app.HTML_DIR');
        $fileNameWithPath = $path . '/' . $filenameHash;
        if (Storage::disk('local')->exists($fileNameWithPath)) {
            return (Storage::disk('local')->get($fileNameWithPath));
        }
    }

    public function getJson($filenameHash)
    {
        $path = config('app.JSON_DIR');
        $fileNameWithPath = $path . '/' . $filenameHash;
        if (Storage::disk('local')->exists($fileNameWithPath)) {
            return (Storage::disk('local')->get($fileNameWithPath));
        }
    }

    public function getCss($filenameHash)
    {
        $path = config('app.CSS_DIR');
        $fileNameWithPath = $path . '/' . $filenameHash;
        if (Storage::disk('local')->exists($fileNameWithPath)) {
            return (Storage::disk('local')->get($fileNameWithPath));
        }
    }

    public function updateImageFolders(Request $request)
    {
        Log::info('Starting updateImageFolders method');
        try {
            $data = $request->json()->all();
            
            if (!isset($data['images']) || !is_array($data['images'])) {
                Log::error('Invalid request: images array is required');
                return response()->json(['error' => 'Images array is required'], 400);
            }

            $updatedImages = [];

            foreach ($data['images'] as $imageUpdate) {
                if (!isset($imageUpdate['imageId'])) {
                    Log::warning('Skipping image update: imageId is required');
                    continue;
                }

                $imageId = $imageUpdate['imageId'];
                $folderIds = $imageUpdate['folderIds'] ?? [];

                // Validate that the image exists
                $image = Image::find($imageId);
                if (!$image) {
                    Log::warning("Image not found with ID: $imageId");
                    continue;
                }

                // Remove existing folder assignments for this image
                ImageAssignment::where('imageId', $imageId)->delete();

                // Add new folder assignments
                foreach ($folderIds as $folderId) {
                    // Validate that the folder exists
                    $folder = ImageTag::find($folderId);
                    if (!$folder) {
                        Log::warning("Folder not found with ID: $folderId, skipping");
                        continue;
                    }

                    $assignment = new ImageAssignment();
                    $assignment->imageId = $imageId;
                    $assignment->imageTagId = $folderId;
                    $assignment->save();
                }

                $updatedImages[] = [
                    'imageId' => $imageId,
                    'folderIds' => $folderIds
                ];

                Log::info("Updated folder assignments for image $imageId", ['folderIds' => $folderIds]);
            }

            return response()->json([
                'message' => 'Image folders updated successfully',
                'updatedImages' => $updatedImages
            ], 200);

        } catch (\Exception $e) {
            Log::error('Exception in updateImageFolders: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'Failed to update image folders: ' . $e->getMessage()], 500);
        }
    }

}

