<?php

namespace App\Http\Controllers;

use App\Models\Datei;
use App\Models\Document;
use App\Models\Tag2Dokument;
use App\Models\Job;
use App\Models\Typen;
use App\Models\Vorproduktion;
use Illuminate\Http\Request;
use App\Models\Tags;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Imagick;
use Symfony\Component\ErrorHandler\Debug;


class DokumentenController extends Controller
{
    const date = "Y-m-d";
    const eindruckdatei = 7;

    public function showdoclist($id)
    {
        return is_numeric($id)
            ? $this->show($id, 'documentlist')
            : $this->show('new');
    }


    public function showdocument($id)
    {
        return is_numeric($id)
            ? $this->show($id)
            : $this->show('new');
    }

    public function get_sf_preview($id)
    {
        $preview = Job::find($id);
        return Response::make($preview->sf_preview, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $preview->sf_preview_name . '"'
        ]);
    }

    public function set_document(Request $request, $id)
    {
        $max_filesize = 40000;
        if ($request->doktyp == self::eindruckdatei) {
            $max_filesize = config('pdf.SET_MAX_UPLOAD_EINDRUCK') ?? 25;
        }
        $messages = ['doktyp.required' => 'Eine Dokumententyp muss angegeben werden.'];
        $array_validates = ['doktyp' => 'required'];
        if (!is_numeric($id)) {
            $messages['filedata.required'] = 'Eine Datei muss angegeben werden.';
            $messages['filedata.max'] = 'Die Datei darf ' . $max_filesize . 'KB nicht überschreiten.';

            $array_validates['filedata'] = 'required';
            if ($request->doktyp == self::eindruckdatei) {
                $array_validates['filedata'] = 'required|max:' . $max_filesize;
            }
        }

        $validator = Validator::make($request->all(), $array_validates, $messages);
        if ($validator->fails()) {
            session(['error' => $validator->errors()->first()]);
            return redirect()->back()->withInput();
        }

        if ($id === 'new') {
            Log::debug('path of uploaded file is: ' . $request->filedata->getRealPath());
            if (!file_exists($request->filedata->getRealPath())) {
                Log::error('ERROR: cannot access uploaded temporary file ' . $request->filedata->getRealPath());
            }
            $content = file_get_contents($request->filedata->getRealPath());
            if ($content === false) {
                Log::error('ERROR: file_get_contents failed.');
            } else {
                Log::debug('Read '. strlen($content) .' bytes for new document with name '. $request->filedata->getClientOriginalName() .' successfully.');
            }
            $dokument = new Document();
            $dokument->inhalt = $content;
            $dokument->name = $request->filedata->getClientOriginalName();
            $dokument->mime = $request->filedata->getClientMimeType();
            $dokument->id_benutzer = Auth::id();
            $dokument->id_kunde = config('app.CUSTOMER_ID');

            if (!$dokument->save()) {
                Log::error('$dokument->safe failed for document uploaded to TS4SF document archive.');
            }
            $id = $dokument->getKey();
	        Log::info('Uploaded new document '. $request->filedata->getClientOriginalName() .' by user '. Auth::id() .' with '. strlen($content) .' bytes for customer '. config('app.CUSTOMER_ID') .', ID: '. $id);
        }

        $tags = [];
        if ($request->tags) {
            foreach ($request->tags as $tag) {
                if (!in_array($tag, $tags)) {
                    $tags[] = $tag;
                }
            }
        }

        $delete = Tag2Dokument::Where('id_dokument', $id);
        $delete->delete();

        foreach ($tags as $value) {
            $write = new Tag2Dokument();
            $write->id_dokument = $id;
            $write->id_tag = $value;
            $write->id_benutzer = Auth::id();
            $write->save();
        }

        $dokument = Document::find($id);
        $dokument->typ = $request->doktyp;
        $dokument->id_benutzer = Auth::id();
        $dokument->save();

        if ($request->vp == 'on' && (!empty($request->anzahl) && !empty($request->datum))) {
            $vp = new Vorproduktion();
            $vp->id_dokument = $id;
            $vp->anzahl = $request->anzahl;
            $vp->id_benutzer = Auth::id();
            $vp->datum = Carbon::parse($request->datum)->format(self::date);
            $vp->save();
        }
        return redirect('dokumente/');
    }

    function readPdfBlob($fileContent)
    {
        if (!empty($fileContent)) {
            $file = '/tmp/' . md5(time());
            if (!empty($fileContent)) {
                $handle = fopen($file, "w+");
                fwrite($handle, $fileContent);
            }
            try {
                $imagick = new Imagick($file . '[0]');
                $imagick->setImageBackgroundColor('white');
                $imagick->setImageAlphaChannel(Imagick::ALPHACHANNEL_REMOVE);
                $imagick->mergeImageLayers(Imagick::LAYERMETHOD_FLATTEN);
            } catch (ImagickException $error) {
                Log::error('Imagemagick error: ' . $error);
                return null;
            }

            $imagick->setImageFormat("jpg");
            //$imagick->resizeImage(200,200,1,0);
            unlink($file);
            return ($imagick);
        } else {
            return null;
        }

    }

    public function index()
    {
        $kunde = 3;
        $dokumente = Document::Where('id_kunde', $kunde)->OrderBy('id', 'DESC')->select('id','id_kunde', 'name', 'typ', 'mime', 'id_benutzer', 'created_at', 'updated_at', 'preview_img', 'freigabe_noetig', 'aktiv')->get();
        $doktypen = Typen::Where('aktiv', 1)->get();
        $array_typen = [];

        foreach ($doktypen as $doktyp) {
            $array_typen[$doktyp->id] = $doktyp->typ;
        }

        $tags = [];
        foreach ($dokumente as $dokument) {
            if (empty($dokument->preview_img) && $dokument->mime == 'application/pdf') {
                $singleDoc = Document::find($dokument->id);
                if(!empty($singleDoc->inhalt)){
                    Log::info('Imagemagick file (PDF to preview JPG): ' . $dokument->name);
                    $dokument->preview_img = $this->readPdfBlob($singleDoc->inhalt);
                    $dokument->save();
                }


            }


            $zuordnung = $dokument->zuordnung();
            $tmp_tag = [];
            foreach ($zuordnung as $ordnung) {
                $tmp_tag[] = $ordnung->tag;
            }
            $tags[$dokument->id] = $tmp_tag;
        }
        $array_jobs = [];


        return view('dokumente', [
            'dokumente' => $dokumente,
            'tags' => $tags,
            'array_jobs' => $array_jobs,
            'array_typen' => $array_typen
        ]);
    }

    public function index_list()
    {
        $kunde = 3;
        $dokumente = Document::Where('id_kunde', $kunde)->OrderBy('id', 'DESC')->get();
        $tags = [];
        $doktypen = Typen::Where('aktiv', 1)->get();
        $array_typen = [];
        foreach ($doktypen as $doktyp) {
            $array_typen[$doktyp->id] = $doktyp->typ;
        }
        foreach ($dokumente as $dokument) {
            $zuordnung = $dokument->zuordnung();
            $tmp_tag = [];
            foreach ($zuordnung as $ordnung) {
                $tmp_tag[] = $ordnung->tag;
            }
            $tags[$dokument->id] = $tmp_tag;
        }
        $array_jobs = [];
        return view('documentlist', [
            'dokumente' => $dokumente,
            'tags' => $tags,
            'array_jobs' => $array_jobs,
            'array_typen' => $array_typen
        ]);
    }


    function getImage($docId){
        $preview = Document::Where('id', $docId)->select('preview_img')->first();
        if(!empty($preview)){
             return $preview->preview_img;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id, $view = 'dokumente')
    {
        $kunde = 3;
        $dokumente = Document::Where('id_kunde', $kunde)->OrderBy('id', 'DESC')->select('id','id_kunde', 'name', 'typ', 'mime', 'id_benutzer', 'created_at', 'updated_at', 'preview_img', 'freigabe_noetig', 'aktiv')->get();
        $akt_dokument = [];
        $jobs = [];
        if ($id !== 'new') {
            $akt_dokument = Document::Where('id', $id)->get()->first();
            $jobs = $akt_dokument->get_jobs([
                'jobs.id',
                'jobs.jobbezeichnung',
                'document2jobs.created_at'
            ]);
        }

        $tags = [];
        foreach ($dokumente as $dokument) {
            $zuordnung = $dokument->zuordnung();
            $tmp_tag = [];
            foreach ($zuordnung as $ordnung) {
                $tmp_tag[$ordnung->id_tag] = $ordnung->tag;
            }
            $tags[$dokument->id] = $tmp_tag;
        }
        $array_jobs = [];

        foreach ($jobs as $job) {
            if (!in_array($job->id, $array_jobs)) {
                $array_jobs[] = $job->id;
            }
        }

        $date = date(self::date);

        $vorproduktion = Vorproduktion::Where('id_dokument', $id)
            ->Where('datum', '>', $date)
            ->sum('anzahl');
        $vorproduktion_datum = Vorproduktion::Where('id_dokument', $id)
            ->Where('datum', '>', $date)
            ->OrderBY('datum', 'DESC')
            ->get('datum')
            ->first();
        $produziert = Vorproduktion::Where('id_dokument', $id)
            ->Where('datum', '<=', $date)
            ->sum('anzahl');
        $verbrauch = Datei::whereIn('id_job', $array_jobs)
            ->sum('datensaetze');
        $betsand = $produziert - $verbrauch;
        $doktypen = Typen::Where('aktiv', 1)->get();
        $array_typen = [];
        foreach ($doktypen as $doktyp) {
            $array_typen[$doktyp->id] = $doktyp->typ;
        }
        $atags = Tags::Where('aktiv', 1)->get();
        $array_tags = [];
        foreach ($atags as $atag) {
            $array_tags[$atag->id] = $atag->tag;
        }

        return view($view, [
            'dokumente' => $dokumente,
            'tags' => $tags,
            'id_dok' => $id,
            'jobs' => $jobs,
            'vorproduktion' => $vorproduktion,
            'vorproduktion_datum' => $vorproduktion_datum,
            'bestand' => $betsand,
            'verbrauch' => $verbrauch,
            'akt_dokument' => $akt_dokument,
            'array_jobs' => $array_jobs,
            'doktypen' => $doktypen,
            'array_typen' => $array_typen,
            'array_tags' => $array_tags
        ]);
    }
}
