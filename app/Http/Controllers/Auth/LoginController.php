<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;

use App\Models\User;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\Kunde;
use App\Models\BenutzerK;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $messages = array(
            'user.required' => 'Bitte geben Sie Ihre E-Mail-Adresse an.',
            'user.email' => 'Es wurde keine gültige E-Mailadresse angegeben.',
            'pass.required' => 'Das Passwort wird benötigt.',
        );

        $validator = Validator::make($request->all(), [
            'user' => 'required|email',
            'pass' => 'required'
        ], $messages);
        if ($validator->fails()) {
            session(['error' => $validator->errors()->first()]);
            return redirect()->back()->withInput();
        }

        if (Auth::attempt(['email' => $request->user, 'password' => $request->pass, 'aktiv' => 1])) {
            // The user is active, not suspended, and exists.
        }


        if (Auth::check()) {
            if (User::current()->is_kunde()) {
                $gesperrte_kunden = Kunde::Where('aktiv', '0')->get()->ToArray();
                $array_ksperre = [];
                foreach ($gesperrte_kunden as $gk) {
                    $array_ksperre[] = $gk['id'];
                }
                $benutzer2kunden = BenutzerK::Where('id_benutzer', Auth::id())->first()->ToArray();
                session(['error' => '']);
                if (in_array($benutzer2kunden['id_kunde'], $array_ksperre)) {
                    session()->flush();
                    session(['error' => 'Der Login ist nicht korrekt.']);
                }
                session()->put('id_tag', 2);
                return redirect('projekte');
            }
            session(['error' => '']);
            session()->put('id_tag', 0);
        } else {
            session()->flush();
            session(['error' => 'Der Login ist nicht korrekt.']);
        }
        return redirect('dashboard');
    }
}
