<?php

namespace App\Http\Controllers;

use App\Models\Datei;
use App\Models\Dienstleister;
use App\Models\Druckdateien;
use App\Models\Einzelhashdateien;
use App\Models\Einzelresponses;
use App\Models\Printstatus;
use App\Models\Transmits;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class StatusController extends Controller
{

    public function getStatus($monitoringSecret)
    {
        if(!(!empty(config('security.monitoringSecret')) && ($monitoringSecret ===  config('security.monitoringSecret')))){
            Log::info('SharedSecret ist wrong');
            die('restricted access');
        }
        $status = [];
        $result = [];
        $globalResult = true;
        // CHECK GENERATE PRINT FILES
        $not_processed = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->Where('generiert', 0)->get();
        if ($not_processed->count()) {
            $result['allWeddingDocsProcessed']['result'] = false;
            $globalResult = false;
            foreach ($not_processed as $notproc) {
                $error_time = config('timings.send_delay') + config('timings.error_delay');
                $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_time);
                if ($break_even < Carbon::now()) {
                    $string = 'JobID #' . $notproc->id_job . ' / StepID #' . $notproc->step_id . ': Generierung der Datei ' . $notproc->org_name . ' wurde nicht durchgeführt ( > ' . $error_time . ' Minuten)';
                    $result['allWeddingDocsProcessed']['details'][] = $string;
                }
            }
        } else {
            $result['allWeddingDocsProcessed']['result'] = true;
        }

        // CHECK PRINT FILES TO DL
        $not_processed = Druckdateien::WhereNull('geloescht_am')->WhereNull('uebergabe_dienstleister')->get();
        if ($not_processed->count()) {
            foreach ($not_processed as $notproc) {
                $error_time = config('timings.error_delay');
                $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_time);
                if ($break_even < Carbon::now()) {
                    $result['allFilesDelivered']['result'] = false;
                    $globalResult = false;
                    $dienstleister = Dienstleister::find($notproc->id_dienstleister);
                    $result['allFilesDelivered']['result'] = false;
                    foreach ($not_processed as $notproc) {
                        $error_time = config('timings.send_delay') + config('timings.error_delay');
                        $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_time);
                        if ($break_even < Carbon::now()) {
                            $string = 'JobID #' . $notproc->id_job . ' / StepID #' . $notproc->step_id . ': Datei ' . $notproc->file_name . ' wurde nicht an ' . $dienstleister->dienstleister . ' übergeben (erzeugt ' . Carbon::parse($notproc->created_at)->format('d.m.Y H:i') . ')';
                            $result['allFilesDelivered']['details'][] = $string;
                        }
                    }
                }
                else{
                    $result['allFilesDelivered']['result'] = true;
                }

            }
        } else {
            $result['allFilesDelivered']['result'] = true;
        }

        // CHECK ARCHIV FILES ARE CREATED
        $not_processed = Datei::WhereNull('geloescht_am')->Where('hasherzeugt', 0 )->WhereNull('stopzeit')->get();
        if ($not_processed->count()) {
            foreach ($not_processed as $notproc) {
                $error_archive_time = config('timings.error_archive_time');
                $break_even = Carbon::parse($notproc->created_at)->addMinutes($error_archive_time);
                if ($break_even < Carbon::now()) {
                    $result['allArchiveFilesGenerated']['result'] = false;
                    $globalResult = false;
                    $count_archiv = Einzelhashdateien::Where('id_job', $notproc->id_job)->Where('step_id', $notproc->step_id)->count();
                    if ($count_archiv < $notproc->datensaetze) {
                        $string = 'JobID #' . $notproc->id_job . ' / StepID #' . $notproc->step_id . ': Archiv wurde nicht komplett erzeugt (' . $count_archiv . '/' . $notproc->datensaetze . ')';
                        $result['allArchiveFilesGenerated']['details'][] = $string;
                    }
                }
                else {
                    $result['allArchiveFilesGenerated']['result'] = true;
                }
            }
        } else {
            $result['allArchiveFilesGenerated']['result'] = true;
        }


        // CHECK ARCHIV FILES NOT TRANSMITTED (only generated and added to transmits / first alert next night at 05:00 after end of transmit time)
        $processed = Datei::WhereNull('geloescht_am')->Where('hasherzeugt', 1)->Where('transmit_erzeugt', 1)->WhereRaw( 'DATE_FORMAT(DATE_ADD(created_at , INTERVAL 2 DAY), "%Y-%m-%d 05:00:00") < now()')->WhereNull('stopzeit')->get();
        if ($processed->count()) {
            foreach ($processed as $proc) {
                $error_archivetransmit_time = config('timings.error_archivetransmit_time');
                $transmitDate = \App\Models\Transmitdate::getTransmitDate($proc->id_job , $proc->step_id);
                $break_even = Carbon::parse($transmitDate)->addMinutes($error_archivetransmit_time);
                if ($break_even < Carbon::now()) {
                    $count_archiv = DB::table('transmits')
                        ->LeftJoin('hashdateien_einzeldatei', 'hashdateien_einzeldatei.id', 'transmits.id_datei')
                        ->Where('hashdateien_einzeldatei.id_job', $proc->id_job)
                        ->Where('transmits.id_liefertyp', 1)
                        ->Where('transmits.status_einlieferung', '202')
                        ->WhereNotNull('transmits.antwort_am')
                        ->Where('hashdateien_einzeldatei.step_id', $proc->step_id)
                        ->count();
                    if ($count_archiv < $proc->datensaetze) {
                        $result['allArchiveFilesTransmitted']['result'] = false;
                        $globalResult = false;
                        $string = 'JobID #' . $proc->id_job . ' / StepID #' . $proc->step_id . ': Archiv wurde nicht komplett übergeben (' . $count_archiv . '/' . $proc->datensaetze . ')';
                        $result['allArchiveFilesTransmitted']['details'][] = $string;
                    }
                    else{
                        $result['allArchiveFilesTransmitted']['result'] = true;
                    }
                }
                else {
                    $result['allArchiveFilesTransmitted']['result'] = true;
                }
            }
        } else {
            $result['allArchiveFilesTransmitted']['result'] = true;
        }

        // CHECK RESPONSE FILES NOT TRANSMITTED
        $allDeliveredFiles = Datei::Where('generiert', 1)->WhereNull('stopzeit')->GroupBy('id_job')->get();
        if ($allDeliveredFiles->count()) {
            foreach ($allDeliveredFiles as $deliveredFile) {
                $error_archivetransmit_time = config('timings.error_archivetransmit_time');
                $break_even = Carbon::parse($deliveredFile->created_at)->addMinutes($error_archivetransmit_time);
                if ($break_even < Carbon::now()) {
                    $received_responses_count = Einzelresponses::WhereNull('geloescht_am')->Where('id_job', $deliveredFile->id_job)->WhereRaw( 'DATE_FORMAT(DATE_ADD(created_at , INTERVAL 1 DAY), "%Y-%m-%d 05:00:00") < now()')->count();
                    $count_archiv = DB::table('transmits')
                        ->LeftJoin('responsedateien_einzeldateien', 'responsedateien_einzeldateien.id', 'transmits.id_datei')
                        ->Where('responsedateien_einzeldateien.id_job', $deliveredFile->id_job)
                        ->Where('transmits.id_liefertyp', 2)
                        ->Where('transmits.status_einlieferung', '202')
                        ->WhereNotNull('transmits.id_status')
                        ->count();
                    if ($count_archiv < $received_responses_count) {
                        // only set to false if $count_archiv < $received_responses_count
                        $result['allResponseFilesTransmitted']['result'] = false;
                        $globalResult = false;
                        $string = 'Responses (JobId #'.$deliveredFile->id_job.') wurden nicht komplett übergeben (' . $count_archiv . '/' . $received_responses_count . ')';
                        $result['allResponseFilesTransmitted']['details'][] = $string;
                    }
                    else{
                        $result['allResponseFilesTransmitted']['result'] = true;
                    }
                }
                else {
                    $result['allResponseFilesTransmitted']['result'] = true;
                }
            }
        } else {
            $result['allResponseFilesTransmitted']['result'] = true;
        }

        $status['globalResult'] =  $globalResult;
        $status['detailedResult'] =  $result;
        return $status;
    }

    public function getPrintStatus(Request $request){
        if(!(!empty(config('security.monitoringSecret')) && ($request->monitoringSecret ===  config('security.monitoringSecret')))){
            Log::info('SharedSecret ist wrong');
            die('restricted access');
        }
        $status = [];
        $result = [];
        $result['printCheck']['details'] = [];
        $result['rightPAL']['details'] = [];
        $result['palWarning']['details'] = [];
        $result['palAlert']['details'] = [];
        // CHECK GENERATE PRINT FILES
        $toBeWatched = Datei::WhereNull('geloescht_am')->WhereNull('stopzeit')->Where('generiert', 1)->get();
        if ($toBeWatched->count()) {
            foreach ($toBeWatched as $datei) {

                // PRÜFUNG DER DRUCKDATEN
                $error_time = config('timings.printCheckHours');
                $printStatus = Printstatus::Where('jobId', $datei->id_job )->Where('stepId', $datei->step_id )->Where('process', 'data check')->Where('success', 1)->orderBY('id', 'desc')->first();
                if (empty($printStatus)) {
                    $string = 'JobID #' . $datei->id_job . ' / StepID #' . $datei->step_id . ': Die Datei ' . $datei->org_name . ' wurde noch nicht verarbeitet ( > ' . $error_time . ' Stunden)';
                    $result['printCheck']['details'][] = $string;
                    $result['printStatus']['result'] = false;
                }

                $dateiPAL = $datei->PAL_date. ' '. config('timings.palMaxTime');

                // PRÜFUNG DES PAL-DATUMS FALSCHES PAL DATUM
                $printStatus = Printstatus::Where('jobId', $datei->id_job )->Where('stepId', $datei->step_id )->orderBY('id', 'desc')->first();
                if(!empty($printStatus) && !empty($datei->PAL_date)){
                    if ($printStatus->palDate != $dateiPAL) {
                        $string = 'JobID #' . $datei->id_job . ' / StepID #' . $datei->step_id . ': Für die Datei ' . $datei->org_name . ' wurde ein falsches PAL-Datum (IST:' . $printStatus->pp_datum . ' | SOLL: '. $printStatus .') hinterlegt';
                        $result['rightPAL']['details'][] = $string;
                        $result['printStatus']['result'] = false;
                    }
                }

                // PRÜFUNG DES PAL-DATUMS WARNING
                $printWarning = config('timings.printWarningHours');
                $printAlert = config('timings.printAlertHours');
                $printStatus = Printstatus::Where('jobId', $datei->id_job )->Where('process', 'pal started')->Where('stepId', $datei->step_id )->orderBY('id', 'desc')->first();
                if(!empty($datei->PAL_date)){
                    $diffHours = Carbon::now()->diffInHours(Carbon::parse($datei->PAL_date), false);
                    if(empty($printStatus) && ( $diffHours < $printAlert)) {
                        $string = 'JobID #' . $datei->id_job . ' / StepID #' . $datei->step_id . ': Die Datei ' . $datei->org_name . ' wurde noch nicht gedruckt ( < ' . $printAlert . ' Stunden bis PAL)';
                        $result['palAlert']['details'][] = $string;
                        $result['printStatus']['result'] = false;
                    }
                    else if(empty($printStatus) && ($diffHours < $printWarning)) {
                        $string = 'JobID #' . $datei->id_job . ' / StepID #' . $datei->step_id . ': Die Datei ' . $datei->org_name . ' wurde noch nicht gedruckt ( < ' . $printWarning . ' Stunden bis PAL)';
                        $result['palWarning']['details'][] = $string;
                        $result['printStatus']['result'] = false;
                    }
                }

            }
        } else {
            $result['printStatus']['result'] = true;
        }
        return $result;
    }

}
