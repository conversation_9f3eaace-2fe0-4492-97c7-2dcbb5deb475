<?php

namespace App\Http\Controllers;

use App\ModelHelper\Mailer;
use App\Models\Aktivitaet;
use App\Models\Datei;
use App\Models\Document;
use App\Models\Dokument2Jobs;
use App\Models\Druckdateien;
use App\Models\File;
use App\Models\Hashdateien;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\PWLs;
use App\Models\Responses;
use App\Models\SonstigeResponses;
use App\Models\Transmitdate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Carbon;

class FileController extends Controller
{
    const dateTime = "Y-m-d H:i:s";

    public function getpreviewarchivepdf($id)
    {
        $rueckgabe = [];
        $file = Hashdateien::find($id);
        $content = $this->file_entschluesseln($file->file);
        $rueckgabe['file'] = base64_encode($content);
        return "[" . json_encode($rueckgabe) . "]";
    }

    public function getresponsefile($id)
    {
        $preview = Responses::find($id);
        return Response::make($this->file_entschluesseln($preview->file), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $preview->file_name . '"'
        ]);
    }

    public function getarchivepdf($id)
    {
        $preview = Hashdateien::find($id);
        return Response::make($this->file_entschluesseln($preview->file), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $preview->file_name . '"'
        ]);
    }

    public function get_csv_by_id_and_linenr($id, $linenr)
    {
        $id_adressdaten = $_GET['id_adressdaten'] ?? "";
        $suche = $_GET['suche'] ?? "";
        $feld = $_GET['feld'] ?? "";
        $page = $_GET['page'] ?? 1;

        $filedetails = Datei::show($id, $suche, $feld, $page);

        $data = Datei::dataset($id, $linenr, $suche, $feld, $page);
        $afiledata = Datei::showdaten($id, $linenr);

        $afiles = Datei::showadressdaten($id, $linenr);
        $afiledata['dataset'] = $data;
        $afiledetails = [];

        return view('filedaten', [
            'filedetails' => $filedetails,
            'filedetails_adr' => $filedetails,
            'afiledetails' => $afiledetails,
            'afiles' => $afiles,
            'id_adressdaten' => $id_adressdaten,
            'afiledata' => $afiledata,
            'zeile_akt' => $linenr,
            'getpage' => $page
        ]);
    }


    function get_file_by_search(Request $request, $id, $page)
    {
        $feld = '*';
        if (!empty($request->feld)) {
            $feld = $request->feld;
        }
        $filedetails = Datei::show($id, $request->suche, $feld, $page);
        return view('file', ['filedetails' => $filedetails]);
    }

    public function get_csv_data(Request $request, $id, $linenr = 1)
    {
        $id_adressdaten = $request->id_adressdaten;
        $hiddenzeile = $request->hiddenzeile;

        $suche = $request->suche;
        $feld = $request->feld;
        if ($feld == '*') {
            $feld = '';
        }
        $pdfpage = $request->akt_pdfpage;

        if (!empty($request->pdfpage)) {
            $pdfpage = $request->pdfpage;
        }

        if (!empty($hiddenzeile)) {
            $linenr = $hiddenzeile;
        }

        if ($linenr < 1) {
            $linenr = 1;
        }
        $filedetails = Datei::show($id, $suche, $feld, $request->page);
        $filedetails_adr = Datei::show($id_adressdaten, $suche, $feld, $request->page);

        $data = Datei::dataset($id_adressdaten, $linenr, $suche, $feld, $request->page);
        $afiledata = Datei::showdaten($id_adressdaten, $linenr);
        $afiles = Datei::showadressdaten($id, $linenr);


        $afiledata['dataset'] = $data;
        $afiledetails = [];
        return view('filedaten', [
            'filedetails' => $filedetails,
            'filedetails_adr' => $filedetails_adr,
            'afiles' => $afiles,
            'afiledetails' => $afiledetails,
            'id_adressdaten' => $id_adressdaten,
            'afiledata' => $afiledata,
            'zeile_akt' => $linenr,
            'pdfpage' => $pdfpage,
            'error' => '',
            'getpage' => $request->page
        ]);
    }

    public function wedding_auto_preview($id_doc, $id_file, $showDataSetNr = null, $showVars = null)
    {
        return view('weddingpreview', ['id_doc' => $id_doc, 'id_file' => $id_file, 'showDataSetNr' => $showDataSetNr, 'showVars' => $showVars]);
    }

    public function editor_auto_preview($id_file, $showDataSetNr = null, $showVars = null)
    {

        return view('editorpreview', ['id_file' => $id_file, 'showDataSetNr' => $showDataSetNr, 'showVars' => $showVars]);
    }

    public function print_preview($id_doc)
    {
        return view('druckpreview', ['id_doc' => $id_doc]);
    }

    public function archive_preview($id_hash)
    {
        return view('hashpreview', ['id_hash' => $id_hash]);
    }

    public function index()
    {
        return view('upload');
    }

    public function get_pwl_list()
    {
        $userinfo = $this->index();
        return view('upload', [
            'userinfos' => $userinfo,
            'selected' => ''
        ]);
    }

    public function upload_pwl(Request $request)
    {
        $filedata = $request->filedata;
        if (empty($filedata)) {
            session(['error' => 'Bitte eine korrekte Datei wählen.']);
            return redirect('freigabe/' . $request->pwl_id_job);
        }

        $content = file_get_contents($filedata->getRealPath());
        $file = new PWLs();
        $file->id_job = $request->pwl_id_job;
        $file->file_name = $filedata->getClientOriginalName();
        $file->mime = $filedata->getMimeType();
        $file->dateigroesse = strlen($content);
        $file->id_dienstleister = config('connections.STANDARD_DL');
        $file->file = $this->file_verschluesseln($content);
        $file->save();
        $result = FTPController::ausliefern_pwl('../');
        if ($result) {
            $job = Job::find($request->pwl_id_job);

            $jobuser = Jobbenutzer::where('id_job', $job->id)->get();

            foreach ($jobuser as $juser) {
                $userdata = User::find($juser->id_benutzer, [
                    'id',
                    'email',
                    'name',
                    'vorname'
                ]);
                if (!empty($userdata) && $juser->benachrichtigung == 1) {
                    $maildata = [
                        'email' => $userdata->email,
                        'vorname' => $userdata->vorname,
                        'name' => $userdata->name,
                        'projekt_id' => '#' . $job->id,
                        'projektname' => $job->jobbezeichnung,
                        'link' => config('app.APP_URL') . 'freigabe/' . $job->id
                    ];

                    Mailer::sendMailByTemplate('EMAIL_PWL_UPLOAD', $maildata);
                }
            }
        } else {
            Log::error("fileController: pwl could not be delivered jobID: " . $request->pwl_id_job);
        }

        return redirect('freigabe/' . $request->pwl_id_job);
    }

    public static function gendelmail($days = 7, $to)
    {
        $userdata = User::Where('aktiv', 1)->get(['id', 'email', 'name', 'vorname', 'anrede']);
        $to = $to->subDay();
        $from = Carbon::parse($to->format('Y-m-d'));
        $from = $from->subDays($days);
        foreach ($userdata as $user) {
            $jobs = $user->jobs();
            $not_empty = [];

            foreach ($jobs as $job) {
                if (!empty($job->id)) {
                    $return_dateien = Job::get_del_dateien_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['Adressdaten'] = $return_dateien;
                    }
                    $return_dateien = Job::get_del_druckdateien_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['Druckdateien'] = $return_dateien;
                    }
                    $return_dateien = Job::get_del_response_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['Response'] = $return_dateien;
                    }
                    $return_dateien = Job::get_del_pwl_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['PWL'] = $return_dateien;
                    }
                    $return_dateien = Job::get_del_archive_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['Archivdateien'] = $return_dateien;
                    }
                    $return_dateien = Job::get_del_response_ok_from_job($job->id, $from, $to);
                    if (!empty($return_dateien)) {
                        $not_empty[$job->id]['Responsedatei_ok'] = $return_dateien;
                    }
                }
            }
            $return_dateien = Job::get_del_sonstigeresponse_from_job($from, $to);
            if (!empty($return_dateien)) {
                $not_empty['-']['Sonstige Responses'] = $return_dateien;
            }
            $ausgabe = "";
            if (empty($not_empty)) {
                return;
            }

            foreach ($not_empty as $job_id => $typ) {
                $ausgabe .= '<b>Projekt #' . $job_id . ":</b><br />";
                foreach ($typ as $bezeichnung => $job) {
                    if ($bezeichnung != "Archivdateien" && $bezeichnung != 'Responsedatei_ok') {
                        $ausgabe .= "<br/>";
                        $ausgabe .= $bezeichnung . ":<br/><br/>";
                        foreach ($job as $data) {
                            $ausgabe .= $data['org_name'] . " | " . $data['step_id'] . '<br />';
                            $ausgabe .= '(gelöscht ' . Carbon::parse($data['geloescht_am'])->format('d.m.Y H:i') . ')<br /><br />';
                        }
                    }
                }
                foreach ($typ as $bezeichnung => $job) {
                    if ($bezeichnung == "Archivdateien" || $bezeichnung == 'Responsedatei_ok') {
                        $ausgabe .= "<br/>";
                        $ausgabe .= $bezeichnung . ":<br/><br/>";

                        foreach ($job as $stepID => $data) {
                            if ($bezeichnung != 'Responsedatei_ok') {
                                $ausgabe .= "StepID " . $stepID . '<br />';
                            }
                            $ausgabe .= Carbon::parse($from)->format('d.m.Y H:i') . ' - ' . Carbon::parse($to)->format('d.m.Y H:i') . ':  ' . count($data) . ' Datensätze gelöscht<br /><br />';
                        }
                    }
                }
                $ausgabe .= '<br />';
            }
            $maildata = [
                'email' => $user->email,
                'vorname' => $user->vorname,
                'name' => $user->name,
                'DELETED' => $ausgabe,
                'link' => config('app.APP_URL') . 'projekte',
                'von' => Carbon::parse($from)->format('d.m.Y'),
                'bis' => Carbon::parse($to)->format('d.m.Y')
            ];

            Mailer::sendMailByTemplate('EMAIL_LOESCHBESTAETIGUNG', $maildata);
        }
    }

    function generateDateigroesse()
    {
        $dateien = Druckdateien::Where('dateigroesse', 0)->get();
        foreach ($dateien as $datei) {
            $file = Druckdateien::find($datei->id);
            if (!empty($file->file)) {
                $file->dateigroesse = strlen($this->file_entschluesseln($file->file));
            }
            $file = $file->save();
        }
        $pwls = PWLs::Where('dateigroesse', 0)->get();
        if (!empty($pwls)) {
            foreach ($pwls as $pwl) {
                $file = PWLs::find($pwl->id);
                if (!empty($file->file)) {
                    $file->dateigroesse = strlen($this->file_entschluesseln($file->file));
                }
                $file = $file->save();
            }
        }
    }

    public function show_file_by_id($id, Request $request)
    {
        $feld = '*';
        if (!empty($request->feld)) {
            $feld = $request->feld;
        }

        $page = 1;
        if (!empty($request->feld)) {
            $page = $request->page;
        }

        $error = '';
        $filedetails = Datei::show($id, ($request->suche), $feld, $page);
        if ((empty($filedetails) || $filedetails['searchcount'] == 0) && !empty($request->suche)) {
            $error = 'Es wurde kein passender Datensatz gefunden';
        }

        return view('file', ['filedetails' => $filedetails, 'error' => $error]);
    }

    public function showcsv($id, $search = null, $feld = '*', $page = 1)
    {
        $searchcount = 0;
        if ($search == '.') {
            $search = '';
        }
        session(['aktiv' => $page]);
        #dd(session('aktiv'));
        $header = [];
        if (empty($search)) {
            $content = Druckdateien::find($id);

            $file = '/tmp/' . md5(time());
            if (!empty($content['file'])) {
                $handle = fopen($file, "w+");
                fwrite($handle, ($content['file']));
            }

            $filedetails['id'] = $id;
            $filedetails['searchcount'] = 0;
            $tmp_array = Druckdateien::return_file_daten($id);
            $filedetails['jobnummer'] = $content->id_job;
            $filedetails['name'] = $content->file_name;
            $filedetails['datacount'] = 0;
            if (!empty($tmp_array)) {
                $filedetails['datacount'] = count($tmp_array);
            }
            if ($searchcount == 0 && !empty($tmp_array[0])) {
                $filedetails['searchcount'] = count($tmp_array);
            }
            $result_array = [];

            $start = 0;
            if ($page > 1) {
                $start = 6 * ($page - 1);
            }
            if (!empty($tmp_array)) {
                foreach ($tmp_array[0] as $key => $inhalt) {

                    $header[] = $inhalt;
                }
            }
            $header[] = "qx_zeilen_nr";

            array_shift($tmp_array);
            for ($i = $start; $i < (6 + $start); $i++) {
                if (!empty($tmp_array[$i])) {
                    $tmp_array[$i]['qx_zeilen_nr'] = $i + 2;
                    $result_array[$i + 1] = ($tmp_array[$i]);
                }
            }
            $filedetails['data'] = $result_array;


            $filedetails['header'] = $header;
        } else if (substr($search, 0, 1) != '#') {
            $filedetails = Datei::searchfiledata($id, $search, $feld, $page);
        }

        $filedetails['array_taetigkeiten'] = Aktivitaet::get_taetigkeiten($id);
        $filedetails['array_taetigkeiten_usergroup'] = Aktivitaet::get_taetigkeiten_usergroup($id);
        $filedetails['jobnummer'] = $content->id_job;
        $filedetails['id_job'] = $content->id_job;
        return $filedetails;
    }

    public function get_filedata_by_id_and_page($id, $page)
    {
        $file = Datei::getpdffiledata($id, $page);
        $content = ($file);
        return response(str_replace("\n", '', $content));
    }

    public static function getPalDates()
    {
        $dateien = Datei::WhereNull('PAL_date')->WhereNull('geloescht_am')->WhereNull('stopzeit')->get();
        foreach ($dateien as $datei) {
            try{
                $date = FileController::getPalFromFileData($datei->id);
                if (!empty($date)) {
                    $datei->Pal_date = $date;
                    $datei->save();
                }
            }
            catch(Exception $e){
                Log::error('Error parsing file #' . $datei->id);
            }

        }
    }

    public static function getPalFromFileData($id)
    {
        $data = Datei::dataset($id, 1);
        if (!empty($data[0]['VERSANDDATUM'])) {
            $tmp_date = explode('.', $data[0]['VERSANDDATUM']);
            return $tmp_date[2] . '-' . $tmp_date[1] . '-' . $tmp_date[0];
        } else {
            Log::error('File has no VERSANDDATUM - #' . $id);
            return null;
        }
        return $data[0]['VERSANDDATUM'];
    }

    public function get_base64_printdata($id)
    {
        $file = Druckdateien::find($id);
        $content_tmp = $file->file;
        $content = $this->file_entschluesseln($content_tmp);
        $rueckgabe['file'] = base64_encode($content);
        return "[" . json_encode($rueckgabe) . "]";
    }


    public function set_release($id)
    {
        $freigabe = $this->freigeben($id);
        return redirect('jobdetails/' . $freigabe . '');
    }

    public function set_rejection($id)
    {
        $ablehnung = $this->ablehnen($id);
        return redirect('jobdetails/' . $ablehnung . '');
    }

    public function show_csv_file($id)
    {
        $page = 1;
        if (!empty($_POST['page'])) {
            $page = $_POST['page'];
        }
        $filedetails = $this->showcsv($id, '', '', $page);
        return view('file', ['filedetails' => $filedetails]);
    }


    public function directDownload($id)
    {
        $file = Document::find($id);
        return response(($file['inhalt']))
            ->header('Content-Disposition', 'attachment; filename=' . $file['name'])
            ->header('Content-Type', $file['mime']);
    }

    public function download_document($id)
    {
        $file = Dokument2Jobs::docdownload($id);
        return response(base64_decode($file['file']))
            ->header('Content-Disposition', 'attachment; filename=' . $file['file_name'])
            ->header('Content-Type', $file['mime']);
    }

    public static function download_response($id)
    {
        $file = Responses::responsedownload($id);
        return response(base64_decode($file['file']))
            ->header('Content-Disposition', 'attachment; filename=' . $file['file_name'])
            ->header('Content-Type', $file['mime']);
    }

    public function download_other_responses($id)
    {
        if (User::current()->is_kunde()) {
            $file = SonstigeResponses::sonstigerdownload($id);
            return response(base64_decode($file['file']))
                ->header('Content-Disposition', 'attachment; filename=' . $file['file_name'])
                ->header('Content-Type', $file['mime']);
        }
    }

    public function dowload_document($id)
    {
        $file = Document::find($id);
        return response($file['inhalt'])
            ->header('Content-Disposition', 'attachment; filename=' . $id . '_' . $file['name'])
            ->header('Content-Type', $file['mime']);
    }

    public static function file_verschluesseln($filecontents)
    {
        $key = config('security.filekey');
        $ivlen = openssl_cipher_iv_length($cipher = "AES-256-CBC");
        $iv = openssl_random_pseudo_bytes($ivlen);
        $ciphertext_raw = openssl_encrypt($filecontents, $cipher, $key, OPENSSL_RAW_DATA, $iv);
        $hmac = hash_hmac('sha256', $ciphertext_raw, $key, true);
        return base64_encode($iv . $hmac . $ciphertext_raw);
    }

    public static function file_entschluesseln($file)
    {
        $key = config('security.filekey');
        $c = base64_decode($file);
        $ivlen = openssl_cipher_iv_length($cipher = "AES-256-CBC");
        $iv = substr($c, 0, $ivlen);
        $hmac = substr($c, $ivlen, $sha2len = 32);
        $ciphertext_raw = substr($c, $ivlen + $sha2len);
        $original_file = openssl_decrypt($ciphertext_raw, $cipher, $key, OPENSSL_RAW_DATA, $iv);
        $calcmac = hash_hmac('sha256', $ciphertext_raw, $key, true);
        if (hash_equals($hmac, $calcmac)) // PHP 5.6+ Rechenzeitangriff-sicherer Vergleich
        {
            $hexContent = bin2hex($original_file);
            if (substr($hexContent, 0, 6) === 'efbbbf') {
                $original_file = substr($original_file, 3);
            }
            return $original_file;
        }
    }

    public function get_file_from_id($id)
    {
        $datei = Datei::find($id);
        $filedata = File::find($datei->id_file);
        $rueckgabe['file'] = base64_encode($this->file_entschluesseln($filedata->file));
        $rueckgabe['mime'] = $filedata->mime;
        return "[" . json_encode($rueckgabe) . "]";
    }

    public function get_file_by_id($id)
    {
        $page = 1;
        if (!empty($_GET['page'])) {
            $page = $_GET['page'];
        }
        $filedetails = Datei::show($id, '', '', $page);
        return view('file', ['filedetails' => $filedetails]);
    }

    public static function delete_datasets($model, $datasets)
    {
        foreach ($datasets as $dataset) {
            $file = $model::find($dataset->id);
            $string_length = strlen($file);
            Log::debug('delete_files: Going to delete file id ' . $dataset->id . ', strlen is ' . $string_length);
            $file->file = null;
            $file->geloescht_am = now();
            $file->save();
        }
    }

    public static function getExcelDaten($file_path)
    {
        $inputFileType = IOFactory::identify($file_path);
        $reader = IOFactory::createReader($inputFileType);
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet(0);
        return $worksheet->toArray();
    }

    public static function checkEncoding($checkString, $debugInfo = '')
    {
        $returnString = $checkString;
        $enc = mb_detect_encoding($checkString, mb_list_encodings(), true);
        // CHECK IF UTF-8 WITHOUT BOM
        if ($enc != 'UTF-8') {
            // CHECK IF NOT UTF-8 WITH BOM
            if (substr($checkString, 0, 3) !== chr(0xEF) . chr(0xBB) . chr(0xBF)) {
                $returnString = utf8_encode($checkString);
                Log::error('File with wrong encoding: ' . $debugInfo);
            }
        }

        return $returnString;

    }

}
