<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\SalesforceApiException;
use App\Models\Font;
use App\Models\Document;
use App\Models\Job;
use App\Models\Datei;
use App\Models\File;

class CsvController extends Controller
{
    public static function getAdressDataHeader($idJob)
    {
        $adressData = Document::getAdressdata($idJob)->first();
        $file_contents = $adressData->inhalt;
        $summe_delimiters = array(0, 0, 0, 0);
        $delim = ';';
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }
        $trenner = array_keys($summe_delimiters, max($summe_delimiters));
        $delim = $delimiters[$trenner[0]];
        $file_array_tmp = explode("\n", $file_contents);
        $bom = substr($file_array_tmp[0], 3);
        if ($bom == b"\xEF\xBB\xBF") {
            $header = substr($file_array_tmp[0], 3);
        } else {
            $header = trim($file_array_tmp[0]);
        }
        $headerArray = explode(';', $header);
        return $headerArray;
    }

    public static function getAdressTestData($idJob, $dataset = null)
    {
        $adressData = Document::getAdressdata($idJob)->first();
        $file_contents = $adressData->inhalt;
        $summe_delimiters = array(0, 0, 0, 0);
        $delim = ';';
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }
        $trenner = array_keys($summe_delimiters, max($summe_delimiters));
        $delim = $delimiters[$trenner[0]];
        $file_array_tmp = explode("\n", $file_contents);
        $bom = substr($file_array_tmp[0], 3);
        if ($bom != b"\xEF\xBB\xBF") {
            $header = substr($file_array_tmp[0], 3);
        } else {
            $header = trim($file_array_tmp[0]);
        }
        $headerArray = explode(';', $header);
        $data = [];
        $lineCount=0;
        foreach($file_array_tmp AS $line){
            $lineArray =  explode(';', $line);
            if(!empty(trim($lineArray[0]))){
            foreach($lineArray AS $key => $lineData){
                $data[$lineCount][$headerArray[$key]] = $lineData;
            }
            $lineCount++;
            }
        }
        if(!empty($dataset)){
            return [$data[$dataset]];
        }
        return $data;
    }
    public static function getAdressData($fileId, $dataset = null)
    {
        $datei = Datei::find($fileId);
        $file = File::find($datei->id_file);
        $file_contents = FileController::file_entschluesseln($file->file);
        $summe_delimiters = array(0, 0, 0, 0);
        $delim = ';';
        $delimiters = array(0 => ";", 1 => ",", 2 => "\t", 3 => "|");
        foreach ($delimiters as $key => $delimiter) {
            $summe_delimiters[$key] += substr_count($file_contents, $delimiter);
        }
        $trenner = array_keys($summe_delimiters, max($summe_delimiters));
        $delim = $delimiters[$trenner[0]];
        $file_array_tmp = explode("\n", $file_contents);
        $bom = substr($file_array_tmp[0], 3);
        if ($bom === b"\xEF\xBB\xBF") {
            $header = substr($file_array_tmp[0], 3);
        } else {
           $header = trim($file_array_tmp[0]);
        }
        $headerArray = explode($delim, $header);

        $data = [];
        $lineCount=0;
        array_shift($file_array_tmp);
        foreach($file_array_tmp AS $line){
            $lineArray =  explode($delim, $line);
            if(!empty(trim($lineArray[0]))){
                foreach($lineArray AS $key => $lineData){
                    //Log::info($key . ": " . $lineData);
                    if(!empty($headerArray[$key])){
                        $data[$lineCount][$headerArray[$key]] = $lineData;
                    }

                }
                $lineCount++;
            }
        }
        if(!empty($dataset)){
            return [$data[$dataset-1]];
        }
        return $data;
    }


}
