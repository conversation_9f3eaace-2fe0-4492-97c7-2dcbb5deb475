<?php

namespace App\Http\Controllers;

use Bschmitt\Amqp\Facades\Amqp;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\Editordocument;
use App\Models\Datei;
use App\Models\Druckdateien;
use App\Models\Job;
use Illuminate\Support\Facades\Storage;
use Dompdf\Dompdf;
use Dompdf\Options;
use Mpdf\HTMLParserMode;
use setasign\Fpdi\Fpdi;
use Illuminate\Support\Facades\Log;
use Mpdf\Mpdf;
use TCPDF;
use setasign\Fpdi\PdfParser\StreamReader;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class EditorController extends Controller
{

    public function createSinglePagePdf($paperSize, $html)
    {
        $widthMM = $paperSize[0] * 28.35;
        $heightMM = $paperSize[1] * 28.35;
        $options = new Options();
        $options->setChroot(storage_path('/app/images/'));
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', false);
        $dompdf = new Dompdf($options);
        $dompdf->setPaper([0, 0, $widthMM, $heightMM]);
        $dompdf->loadHtml($html);
        $dompdf->render();

        return $dompdf->output();
    }


    function mergePdfs(array $streams)
    {
        $pdf = new Fpdi();
        $pdf->SetCompression(true);
        foreach ($streams as $stream) {
            $pageCount = $pdf->setSourceFile($stream);
            for ($i = 1; $i <= $pageCount; $i++) {
                //Log::info('merging page ' . $i);;
                $templateId = $pdf->importPage($i);
                $size = $pdf->getTemplateSize($templateId);
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);
            }
        }
        $pdf->cleanUp();
        return $pdf->Output('S');
    }

    private function cleanupHtml($html)
    {
        // Entferne leere style-Attribute
        $html = preg_replace('/\s*style=[\'"]\s*[\'"]/', '', $html);

        $html = preg_replace('/page-break-after\s*:\s*always\s*;?/', '', $html);

        // Entferne data-element-id Attribute
        $html = preg_replace('/\s*data-element-id=[\'"][^\'"]*[\'"]/', '', $html);
        $html = preg_replace('/\s*data-element-data=[\'"][^\'"]*[\'"]/', '', $html);


        // Entferne box-shadow Styles
        $html = preg_replace('/box-shadow:\s*0\s+0\s+10px\s+rgba\(0\s*,\s*0\s*,\s*0\s*,.\)\s*;?/', '', $html);
        $html = preg_replace('/box-shadow:\s*0\s+0\s+10px\s+rgba?\((\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*[0-9\.]+)?\)\s*;?/', '', $html);

        // Entferne leere class-Attribute
        $html = preg_replace('/\s*class=[\'"]\s*[\'"]/', '', $html);

        // Entferne leere Span-Tags ohne Attribute
        $html = preg_replace('/<span>\s*<\/span>/', '', $html);

        // Entferne überflüssige Whitespaces
        $html = preg_replace('/\s+/', ' ', $html);

        // Entferne leere div-Tags ohne Attribute
        $html = preg_replace('/<div>\s*<\/div>/', '', $html);

        $result = preg_replace('/<p\b([^>]*)>\s*<\/p>/i', '<p$1>&nbsp;</p>', $html);


        $html = str_replace('cursor-move', '', $html);
        //$html = str_replace('<p></p>', '<p>&nbsp;</p>', $html);
        $html = str_replace('line-height: 1.2', 'line-height: 0.875', $html);


        return trim($html);
    }

    function rpcCall($payload)
    {
        $connection = new AMQPStreamConnection(config('amqp.properties.production.host'), config('amqp.properties.production.port'), config('amqp.properties.production.username'), config('amqp.properties.production.password'));
        $channel = $connection->channel();
        $channel->queue_declare(config('amqp.properties.production.preview_queue'), false, false, false, false);


        list($replyQueue, ,) = $channel->queue_declare("", false, false, true, true);


        $correlation_id = uniqid();

        $msg = new AMQPMessage($payload, [
            'correlation_id' => $correlation_id,
            'reply_to' => $replyQueue
        ]);

        $channel->basic_publish($msg, '', config('amqp.properties.production.preview_queue'));

        $response = null;
        Log::info('queue: ' . $replyQueue);
        Log::info('correlation_id: ' . $correlation_id);
        $channel->basic_consume(
            $replyQueue,
            '',
            false,
            true,
            false,
            false,
            function ($rep) use (&$response, $correlation_id) {
                if ($rep->get('correlation_id') === $correlation_id) {
                    $response = $rep->body;
                }
            }
        );

        while (!$response) {
            $channel->wait();
        }

        $channel->close();
        $connection->close();

        return $response;
    }

    public function generateTestPDF($jobId = null, $dataset = null)
    {
        $pdfName = 'TestPdf_for_Job_' . $jobId . '_' . date('YmdHis') . '.pdf';

        $jobArray = [
            'REF' => $pdfName,
            'Job' => $jobId ?? null,
            'TestDataset' => $dataset ?? null,
        ];

        $response = $this->rpcCall(json_encode($jobArray));

        return response($response)
            ->header('Content-Disposition', 'inline; filename=' . $pdfName)
            ->header('Content-Type', 'application/pdf');

        /*
         Amqp::publish('routing-key', json_encode($jobArray) , ['queue' => 'pdf-editor-job']);
        */

    }

    public function downloadTestPDF($jobId = null, $dataset = 1)
    {
        $job = Job::find($jobId);
        if (!empty($job)) {
            
            $pdfName = $jobId . '-' . $job->jobbezeichnung . '-' . date('YmdHis') . '.pdf';

            $jobArray = [
                'REF' => $pdfName,
                'Job' => $jobId ?? null,
                'TestDataset' => $dataset ?? null,
            ];

            $response = $this->rpcCall(json_encode($jobArray));

            return response($response)
                ->header('Content-Disposition', 'attachment; filename=' . $pdfName)
                ->header('Content-Type', 'application/pdf');

        }
    }

    public function getPDF($jobId = null, $dataset = 1)
    {
        $jobArray = [
            'REF' => 'DownloadPdf_for_Job_' . $jobId . '_' . date('YmdHis'),
            'Job' => $jobId ?? null,
            'TestDataset' => $dataset ?? null,
        ];

        $response = $this->rpcCall(json_encode($jobArray));

        return response($response);

    }

    public function generatePDF($fileId = null, $dataset = 1, $showVars = null)
    {

        $pdfName = 'SinglePdf_for_Job_' . $fileId . '_' . date('YmdHis') . '.pdf';

        $fileInfo = Datei::find($fileId);
        $jobArray = [
            'REF' => 'CustomerPdf_for_Job_' . $fileId . '_' . date('YmdHis'),
            'Job' => $fileInfo->id_job ?? null,
            'CustomerFile' => $fileId ?? null,
            'Dataset' => $dataset ?? null,
            'showVars' => $showVars ?? null,
            'StepId' => $fileInfo->step_id ?? null,
        ];

        $response = $this->rpcCall(json_encode($jobArray));

        return response($response)
            ->header('Content-Disposition', 'inline; filename=' . $pdfName)
            ->header('Content-Type', 'application/pdf');

    }


    public function returnSingleArchivePDF($fileId = null, $dataset = 1, $showVars = null)
    {

        $pdfName = 'Archive_for_CustomerFile_' . $fileId . '_Dataset_' . $dataset . '_' . date('YmdHis') . '.pdf';

        $fileInfo = Datei::find($fileId);
        $jobArray = [
            'REF' => $pdfName,
            'Job' => $fileInfo->id_job ?? null,
            'CustomerFile' => $fileId ?? null,
            'Dataset' => $dataset ?? null,
            'showVars' => $showVars ?? null,
            'StepId' => $fileInfo->step_id ?? null,
        ];

        $response = $this->rpcCall(json_encode($jobArray));

        return $response;

    }


    public function generatePrintPdf($fileId = null)
    {
        $fileInfo = Datei::find($fileId);
        $jobArray = [
            'REF' => 'CustomerPdf_for_Job_' . $fileInfo->id_job . '_' . $fileInfo->step_id . '_' . date('YmdHis'),
            'Job' => $fileInfo->id_job ?? null,
            'CustomerFile' => $fileId ?? null,
            'StepId' => $fileInfo->step_id ?? null,
        ];

        return Amqp::publish('routing-key', json_encode($jobArray), ['queue' => config('amqp.properties.production.generate_queue')]);

    }

    public function storePDF(Request $file, $jobId = null, $stepId = null, $format = null)
    {
        Log::info('storePDF');
        if (empty($jobId)) {
            return response()->json(['error' => 'jobId not set'], 400);
        } elseif (empty($stepId)) {
            return response()->json(['error' => 'stepId not set'], 400);
        } elseif (empty($format)) {
            return response()->json(['error' => 'format not set'], 400);
        }

        $content = $file->getContent();
        $druckdatei_org = new Druckdateien();
        $org_filename = 'Druckdatei_#' . $jobId . '_' . $stepId . '_' . $format . '.pdf';
        $result_org = $druckdatei_org->store($jobId, $stepId, $org_filename, 0, $content, 'application/pdf');

        if ($result_org) {
            return response()->json(['result' => 'success'], 200);
        } else {
            return response()->json(['error' => 'could not be saved'], 400);
        }
    }

    public static function generateMcPreview($jobId)
    {
        $job = Job::find($jobId);
        $array_docs = json_decode($job['gui_merge']);
        if (!empty($jobId)) {
            $sf_content = null;
            if (!empty($array_docs)) {
                $jobcobtroller = new JobController();
                $sf_content = $jobcobtroller->generate_sfPreview($array_docs, $jobId);
            }
            $sf_preview_name = "";
            if (!empty($sf_content)) {
                $sf_preview_name = 'MC_Preview_' . $jobId . '.pdf';
            }

            $job->sf_preview_name = $sf_preview_name;
            $job->sf_preview = $sf_content;
            $job->save();

        }
    }

    public function donePDF($customerFileId = null)
    {
        Log::info('donePDF');
        if (empty($customerFileId)) {
            return response()->json(['error' => '$customerFileId not set'], 400);
        }

        $datei = Datei::find($customerFileId);
        $datei->generiert = 1;
        $result = $datei->save();

        if ($result) {
            return response()->json(['result' => 'success'], 200);
        } else {
            return response()->json(['error' => 'could not be set'], 400);
        }
    }


    public function generatePrintdoc()
    {
        $dateien = Datei::Where('created_at', '<', Carbon::now()->subMinute(config('timings.send_delay'))->format('Y-m-d H:i:s'))
            ->Where('generiert', '0')->Where('datensaetze', '>', 0)->WhereNull('stopzeit')->get();

        Log::info("Editor (generatePrintDoc) Found " . count($dateien) . " CSV-files");

        foreach ($dateien as $datei) {
            Log::info("Generating for CSV:  " . $datei->org_name);
            $addressDataController = new AddressDataController();
            $addressData = $addressDataController->getAdressData($datei->id);
            $pdfContents = $this->generatePdfWithData($datei->id_job, $addressData, $version = null, false);
            foreach ($pdfContents as $dateiIndex => $pdfContent) {
                $druckdatei = new Druckdateien();
                $org_filename = 'Druckdatei_#' . $datei->id_job . '_' . $datei->step_id . '.' . $dateiIndex . '.pdf';
                $result_org = $druckdatei->store($datei->id_job, $datei->step_id, $org_filename, config('connections.STANDARD_DL'), $pdfContent, 'application/pdf');

            }
            if ($result_org) {
                unset($hoch_datei);
                unset($druckdatei_org);
                $csv_filename = 'Steuerdatei_#' . $datei->id_job . '_' . $datei->step_id . '.csv';
                $csvdatei = new Druckdateien();
                $result_csv = $csvdatei->store($datei->id_job, $datei->step_id, $csv_filename, $doc2job->anzeige_dienstleister, $csv_content, 'text/csv');
            }

            if ($result_csv) {
                unset($csvdatei);
                $filename = 'Eindruck_#' . $datei->id_job . '_' . $datei->step_id . '.pdf';
                $druckdatei = new Druckdateien();
                try {
                    $result_druck = $druckdatei->store($datei->id_job, $datei->step_id, $filename, $doc2job->anzeige_dienstleister, $inhalt, 'application/pdf');
                } catch (\Illuminate\Database\QueryException $ex) {
                    $job = Job::find($datei->id_job);
                    $jobuser = Jobbenutzer::where('id_job', $datei->id_job)->get();
                    foreach ($jobuser as $juser) {
                        $userinfo = User::find($juser->id_benutzer);
                        if (!empty($userinfo) && $userinfo->is_agentur() && $juser->benachrichtigung == 1) {
                            $userdata = User::find($juser->id_benutzer, ['id', 'email', 'name', 'vorname']);
                            $maildata = array('email' => $userdata->email, 'vorname' => $userdata->vorname, 'name' => $userdata->name, 'fehler' => 'Fehler bei der Speicherung der Eindruckdatei', 'dateibezeichnung' => $filename, 'projekt_id' => $job->id, 'stepid' => $datei->step_id, 'projektname' => $job->jobbezeichnung);

                            Mailer::sendMailByTemplate('EMAIL_SYSTEMMELDUNG_TGD', $maildata);
                        }
                    }
                    Log::error("Could not save file to DB. ID job: " . $datei->id_job);
                }


                if ($result_druck) {
                    unset($inhalt);
                    unset($druckdatei);
                    $job = Job::find($datei->id_job);
                    $content = $job->sf_preview;
                    $filename = 'Ansicht_#' . $datei->id_job . '_' . $datei->step_id . '.pdf';
                    $druckdatei = new Druckdateien();
                    $druckdatei->store($datei->id_job, $datei->step_id, $filename, $doc2job->anzeige_dienstleister, $content, 'application/pdf');
                }
            }
            if ($result_csv && $result_org && $result_druck) {
                unset($druckdatei);
                $generiert = Datei::find($datei->id);
                $generiert->generiert = 1;
                $generiert->save();
            }
        }
        if ($result_csv && $result_org && $result_druck) {
            return 'done';
        }
        return 'error';

    }

    public function generatePdfWithData($jobId, $addressData, $version = null, $preview = true, $showVars = 0)
    {
        $cssPath = config('app.CSS_DIR');
        $htmlPath = config('app.HTML_DIR');
        $jsonPath = config('app.JSON_DIR');

        // Seiten abrufen
        if (!empty($jobId) && empty($version)) {
            $pages = Editordocument::getLastVersion($jobId);
        } else {
            $pages = Editordocument::getPagesByVersion($jobId, $version);
        }

        $css = Storage::disk('local')->get($cssPath . "/" . $pages->css);
        $css = $this->cleanupHtml($css);
        $cssHeader = $this->generateHtmlWithStyles($css);
        $arrayPages = [];

        // Controller für Variablen und Testdaten
        $varsController = new VarsController();
        $usedVars = $varsController->get($jobId)->getData();
        $datasetCounter = 1;
        $htmlContent = [];
        $jsonContent = [];
        $pageData = json_decode($pages->pages);

        foreach ($pageData->hashes as $htmlPage) {
            if (!empty($htmlPage->html)) {
                $pageContent = Storage::disk('local')->get($htmlPath . "/" . $htmlPage->html);

                $htmlOutput = $this->replaceImageUrls($pageContent);

                if (!empty($htmlPage->json)) {
                    $jsonContent[$htmlPage->json] = Storage::disk('local')->get($jsonPath . "/" . $htmlPage->json);
                }
                $width = json_decode($jsonContent[$htmlPage->json])->page->width;
                $height = json_decode($jsonContent[$htmlPage->json])->page->height;

                $htmlContent[$width . $height][] = [
                    'html' => $htmlOutput,
                    'json' => $jsonContent[$htmlPage->json],
                ];
            }

        }


        $arrayPages = [];
        foreach ($htmlContent as $indexFormat => $seitenformat) {
            $startTime = microtime(true);
            $options = new Options();
            $options->setChroot(storage_path('/app/images/'));
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);
            //$options->set('fontHeightRatio', 1);
            //$options->set('isFontSubsettingEnabled', false);
            $dompdf = new Dompdf($options);

            $jsonOutput = $seitenformat[0]['json'];
            $data = json_decode($jsonOutput)->page;

            $width = $data->width + ($data->trim_left ?? 0) + ($data->trim_right ?? 0);
            $height = $data->height + ($data->trim_top ?? 0) + ($data->trim_bottom ?? 0);

            $widthMM = $width * 28.35;
            $heightMM = $height * 28.35;
            $dompdf->setPaper([0, 0, $widthMM, $heightMM]);
            $outputPath = 'Editor2025_TestPDF_' . $datasetCounter . '_' . $indexFormat . '.pdf';
            $completeHTML = $cssHeader;
            $datasetCounter = 0;
            foreach ($addressData as $dataset) {

                if (!file_exists(storage_path($outputPath))) {
                    if ($datasetCounter % 100 == 0) {
                        Log::info('Generating Dataset (HTML) #' . $datasetCounter);
                    }
                    $pageCount = 1;
                    foreach ($seitenformat as $htmlPage) {
                        $pageContent = '';
                        if (!empty($htmlPage['html'])) {
                            $pageContent = $htmlPage['html'];
                        }


                        // Variablen ersetzen
                        foreach ($usedVars as $usedVar) {
                            if (!empty($dataset[$usedVar])) {
                                if ($showVars == 1) {
                                    $pageContent = str_replace('{{' . $usedVar . '}}', '<span style="color: #ff00ff !important;">' . $dataset[$usedVar] . '</span>', $pageContent);
                                } else {
                                    $pageContent = str_replace('{{' . $usedVar . '}}', $dataset[$usedVar], $pageContent);
                                }
                            } else {
                                $pageContent = str_replace('{{' . $usedVar . '}}', '', $pageContent);
                            }

                        }

                        $pageContent = $this->cleanupHtml($pageContent);

                        /*
                        if (!empty($htmlPage->json)) {
                            $jsonOutput = $jsonContent[$htmlPage->json];
                            $data = json_decode($jsonOutput)->page;

                            // Seitenmaße berechnen
                            $width = $data->width + ($data->trim_left ?? 0) + ($data->trim_right ?? 0);
                            $height = $data->height + ($data->trim_top ?? 0) + ($data->trim_bottom ?? 0);

                            $pdfContent = $this->createSinglePagePdf([$width, $height], $pageContent);
                            $stream = StreamReader::createByString($pdfContent);
                            $arrayPages[] = $stream;
                        }
                        */


                        //$pageContent = rtrim($pageContent, 'page-break-after: always;');
                        $completeHTML .= $pageContent;
                    }
                    // $mergedPdf = $this->mergePdfs($arrayPages, $outputPath);


                } else {
                    Log::info('already generated Dataset #' . $datasetCounter);
                }
                $datasetCounter++;
                if ($datasetCounter > 800) {
                    //break;
                }

            }
            //echo $completeHTML; die();

            // $dompdf->render();
            //$dompdf->stream(    $outputPath, ["Attachment" => false]);
            Log::info('HTML generated in ' . (microtime(true) - $startTime) . ' seconds | Memory ' . (memory_get_usage() / (1024 * 1024)) . 'MB');
            $startTime = microtime(true);
            $dompdf->loadHtml($completeHTML);
            $dompdf->render();
            Log::info('PDF generated in ' . (microtime(true) - $startTime) . ' seconds | Memory ' . (memory_get_usage() / (1024 * 1024)) . 'MB');
            # if ($preview === true) {
            $arrayPages[] = StreamReader::createByString($dompdf->output());
            #} else {
            #  $arrayPages[] = $dompdf->output();
            # }

            //$fileData = $dompdf->output();
            //Storage::disk('local')->put( $outputPath, $fileData);

            //die();


        }
        // PDFs zusammenführen
        if ($preview === true) {
            $outputPath = 'Editor2025_TestPDF_' . date("YmdHis") . '.pdf';
            $startTime = microtime(true);
            $mergedPdf = $this->mergePdfs($arrayPages);
            Log::info('PDF mergerd in ' . (microtime(true) - $startTime) . ' seconds | Memory ' . (memory_get_usage() / (1024 * 1024)) . 'MB');
            #dd($startTime - microtime(true));
            return response($mergedPdf)->header('Content-Type', 'application/pdf');
        } else {
            $outputPath = 'Editor2025_TestPDF_' . date("YmdHis") . '.pdf';
            $startTime = microtime(true);
            $mergedPdf = $this->mergePdfs($arrayPages);
            Log::info('PDF mergerd in ' . (microtime(true) - $startTime) . ' seconds | Memory ' . (memory_get_usage() / (1024 * 1024)) . 'MB');
            #dd($startTime - microtime(true));
            return response($mergedPdf)
                ->header('Content-Type', 'application/pdf')
                ->header(
                    'Content-Disposition',
                    'attachment; filename="' . $jobId . '_' . date('YmdHis') . '.pdf"'
                );

        }

    }

    public function generateJsonWithData($jobId, $addressData, $version = null, $preview = true, $showVars = 0)
    {
        $cssPath = config('app.CSS_DIR');
        $htmlPath = config('app.HTML_DIR');
        $jsonPath = config('app.JSON_DIR');
        $outputArray = [];
        // Seiten abrufen
        if (!empty($jobId) && empty($version)) {
            $pages = Editordocument::getLastVersion($jobId);
        } else {
            $pages = Editordocument::getByVersion($jobId, $version);
        }


        $outputArray['structure'] = $pages;

        $css = Storage::disk('local')->get($cssPath . "/" . $pages->css);

        $outputArray['content']['css'][$pages->css] = $css;

        $pagData = json_decode($pages->pages);
        foreach ($pagData->hashes as $page) {
            $html = Storage::disk('local')->get($htmlPath . "/" . $page->html);
            $json = Storage::disk('local')->get($jsonPath . "/" . $page->json);
            $outputArray['content']['json'][$page->json] = $json;
            $outputArray['content']['html'][$page->html] = $html;
        }
        $outputArray['content']['css'][$pages->css] = $css;

        return $outputArray;


    }

    private function generateHtmlWithStyles($css)
    {
        return '
        <head>
        <meta charset="UTF-8">
        <style>' . $css . '</style>
        <style>
            @font-face { font-family: NeoSansforeprimo-Bold; src: url(' . url("/fonts/pdf/NeoSansforeprimo-Bold.ttf") . ') format("truetype"); font-weight: normal; font-style: normal; font-display: swap; }
            @font-face { font-family: NeoSansforeprimo-Italic; src: url(' . url("/fonts/pdf/NeoSansforeprimo-Italic.ttf") . ') format("truetype"); font-weight: normal; font-style: normal; font-display: swap; }
            @font-face { font-family: NeoSansforeprimo-Medium; src: url(' . url("/fonts/pdf/NeoSansforeprimo-Medium.ttf") . ') format("truetype"); font-weight: normal; font-style: normal; font-display: swap; }
            @font-face { font-family: NeoSansforeprimo-Regular; src: url(' . url("/fonts/pdf/NeoSansforeprimo-Regular.ttf") . ') format("truetype"); font-weight: normal; font-style: normal; font-display: swap; }
        </style>
        </head>';
    }

    private function replaceImageUrls($html)
    {
        return preg_replace_callback(
            '/<img\s+src="\/api\/image\/get\/([a-f0-9]{64}\.jpg)"([^>]*)>/i',
            function ($matches) {
                $filename = $matches[1];
                $attributes = $matches[2];
                //$filePath = url('/images/pdf/') . '/' . $filename;
                $filePath = storage_path('/app/images/') . '/' . $filename;
                return '<img src="' . $filePath . '" ' . $attributes . '>';
            },
            $html
        );
    }

    public function getGenarationTestData($jobId, $version = null)
    {

        $testDataController = new TestDataController();
        $addressData = $testDataController->getArray($jobId);

        $content = $this->generateJsonWithData($jobId, $addressData, $version, true);
        $content['data'] = $addressData;
        return response()->json($content, 200);
    }

    public function getGenarationDataAndFileId($jobId, $fileId = null, $version = null)
    {
        $addressDataController = new AddressDataController();
        $addressData = $addressDataController->getAdressData($fileId);

        $content = $this->generateJsonWithData($jobId, $addressData, $version, true);
        $content['data'] = $addressData;
        return response()->json($content, 200);
    }

    public function getGenarationDataAndFileIdAndDataset($jobId, $fileId = null, $dataset = null, $version = null)
    {
        $addressDataController = new AddressDataController();
        $addressData = $addressDataController->getAdressData($fileId, $dataset);

        $content = $this->generateJsonWithData($jobId, $addressData, $version, true);
        $content['data'] = $addressData;
        return response()->json($content, 200);
    }


    public function getGenarationTestDataSingleDataset($jobId, $dataset, $version)
    {

        $testDataController = new TestDataController();
        $addressData = $testDataController->getArray($jobId, $dataset);

        $content = $this->generateJsonWithData($jobId, $addressData, $version, true);
        $content['data'] = $addressData;

        return response()->json($content, 200);
    }
}
