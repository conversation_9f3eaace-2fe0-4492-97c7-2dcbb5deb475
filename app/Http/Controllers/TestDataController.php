<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\SalesforceApiException;
use App\Models\Font;

class TestDataController extends Controller
{
    public function get($idJob)
    {
        $data = CsvController::getAdressTestData($idJob);
        return response()->json($data, 200);
    }

    public function getArray($idJob, $dataset = null)
    {

        $data = CsvController::getAdressTestData($idJob, $dataset);

        return $data;
    }
}
