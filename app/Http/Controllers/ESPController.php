<?php

namespace App\Http\Controllers;

use App\Models\Datei;
use App\Models\Liefertypen;
use App\Models\Log as LogDB;
use App\Models\Transmitdate;
use App\Models\Transmits;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ESPController extends Controller
{
    private function transmit_doc($id_transmit)
    {
        Log::debug("Transmitting id_transmit: " . $id_transmit);
        $start_time = microtime(true);
        $token_response = $this->getToken(
            config('connections.esp_user'),
            config('connections.esp_pw'),
            config('connections.esp_token_url')
        );
        $transmit_data = Transmits::find($id_transmit);
        if (empty($transmit_data)) {
            Log::warning("transmit_id " . $id_transmit . " has empty transmit_data.");
            return false;
        }

        $liefertyp = Liefertypen::find($transmit_data->id_liefertyp);
        $datei = DB::table($liefertyp->tabelle)->find($transmit_data->id_datei);
        if (empty($datei) || empty($datei->file)) {
            Log::warning("transmit_id " . $id_transmit . " has empty file.");
            return false;
        }

        $duration = microtime(true) - $start_time;
        Log::debug("transmit_id " . $id_transmit . " found and file not empty. Time up to here: " . $duration);

        $content = base64_encode(FileController::file_entschluesseln($datei->file));
        $vertragskonto = $datei->vertragskonto;
        $liefertyp = Liefertypen::find($transmit_data->id_liefertyp);

        $deadline = Carbon::now()->addHours(config('connections.ESP_RETRY'))
            ->format('c');

        $data = [];
        $data['documentType'] = $liefertyp->apityp;
        $data['contentFormat'] = 'PDF';
        $data['content'] = $content;
        $data['callback'] = [
            'system' => 'transfersafe',
            'recordId' => (string)$id_transmit,
            'deadline' => $deadline
        ];
        $duration = microtime(true) - $start_time;
        Log::debug("transmit_id " . $id_transmit . " ready to be sent. Time up to here: " . $duration);

        $ch = curl_init(config('connections.esp_transmit_api') . '/accounts/' . $vertragskonto . '/documents');

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt(
            $ch,
            CURLOPT_HTTPHEADER,
            [
                'Content-Type: application/json;charset=UTF-8',
                'Accept: application/json;charset=UTF-8',
                'Authorization: Bearer ' . $token_response->access_token,
                'Content-Length: ' . strlen(json_encode($data))
            ]
        );
        $rawApiReturn = json_decode(curl_exec($ch));
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        $duration = microtime(true) - $start_time;
        Log::debug("transmit_id " . $id_transmit . " got result code: " . $code . ". Time up to here: " . $duration);

        $debug_tmp = [
            'id_transmit' => $id_transmit,
            'code' => $code,
            'rawApiReturn' => $rawApiReturn
        ];

        $log = new LogDB();
        $log->datetime = Carbon::now();
        $log->debug = json_encode($debug_tmp);
        $log->save();

        $transmit = Transmits::find($id_transmit);
        $transmit->einlieferung_am = now();
        $transmit->status_einlieferung = $code;
        $transmit->debug = json_encode($rawApiReturn);
        $transmit->save();

        $duration = microtime(true) - $start_time;
        Log::info("transmit_id " . $id_transmit . " saved results. Time up to here: " . $duration);

        return $code == 202;
    }

    private function getToken($user, $pass, $url)
    {
        Log::debug("Get token with clientId(esp_user): ..." . substr($user, -3) . " and clientSercert(esp_pw): ..." . substr($pass, -3));
        $ch = curl_init();
        $headers = [
            'Authorization: Basic ' . base64_encode($user . ":" . $pass)
        ];
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, $user . ":" . $pass);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $rawApiReturn = curl_exec($ch);
        $token_response = json_decode($rawApiReturn);
        if (empty($token_response->access_token)) {
            Log::error("No token received: " . $token_response->error_description);
        }
        return $token_response;
    }

    //REMOVE AFTER CHECK
    public function checkGetToken($monitoringSecret)
    {
        if (!(!empty(config('security.monitoringSecret')) && ($monitoringSecret === config('security.monitoringSecret')))) {
            Log::info('SharedSecret ist wrong');
            die('restricted access');
        }
        $token_response = $this->getToken(
            config('connections.esp_user'),
            config('connections.esp_pw'),
            config('connections.esp_token_url')
        );
        if (!empty($token_response->access_token)) {
            Log::info('GetTokenCheck passed');
        }

    }

    const dateTime = "Y-m-d H:i:s";

    public function processTransmit()
    {
        Log::notice("processTransmit() started.");
        $allFiles = Datei::Where('generiert', 1)
            ->Where('hasherzeugt', 1)
            ->Where('transmit_erzeugt', 1)
            ->WhereNull('stopzeit')->get();

        $readyForTransmits = [];
        foreach ($allFiles as $file) {
            $transmitDate = Transmitdate::getTransmitDate($file->id_job, $file->step_id);
            if (date('Y-m-d') >= $transmitDate) {
                $readyForTransmits[] = $file;
            }
        }

        $transmits = [];

        foreach ($readyForTransmits as $readyForTransmit) {
            $readyId_job = $readyForTransmit->id_job;
            $readyStep_id = $readyForTransmit->step_id;
            $transmitsTmps = DB::table('transmits')->where('status_einlieferung', '!=', '202')
                ->leftJoin('hashdateien_einzeldatei', 'transmits.id_datei', '=', 'hashdateien_einzeldatei.id')
                ->where('id_liefertyp', 1)
                ->where('hashdateien_einzeldatei.id_job', '=', $readyId_job)
                ->where('hashdateien_einzeldatei.step_id', '=', $readyStep_id)
                ->where('einlieferung_am', '<', Carbon::now()->subHours(config('connections.ESP_RETRY'))->format(self::dateTime))
                ->orWhere(function ($q) use ($readyId_job, $readyStep_id) {
                    $q->whereNull('status_einlieferung')
                        ->whereNull('einlieferung_am')
                        ->where('id_liefertyp', 1)
                        ->where('hashdateien_einzeldatei.id_job', '=', $readyId_job)
                        ->where('hashdateien_einzeldatei.step_id', '=', $readyStep_id);
                })
                ->orWhere(function ($q) use ($readyId_job, $readyStep_id) {
                    $q->where('einlieferung_am', '<', Carbon::now()->subHours(config('connections.ESP_RETRY'))->format(self::dateTime))
                        ->whereNull('antwort_am')
                        ->where('id_liefertyp', 1)
                        ->where('hashdateien_einzeldatei.id_job', '=', $readyId_job)
                        ->where('hashdateien_einzeldatei.step_id', '=', $readyStep_id);
                })
                ->limit(config('connections.esp_transmit_limit'))->get('transmits.id');

            foreach ($transmitsTmps as $transmitsTmp) {
                $transmits[] = $transmitsTmp;
                if (count($transmits) >= config('connections.esp_transmit_limit')) {
                    break;
                }
            }
        }
        foreach ($transmits as $transmit) {
            $this->transmit_doc($transmit->id);
        }
    }

    public function processTransmitResponses()
    {
        Log::notice("processTransmitResponses() started.");
        $transmits = Transmits::where('status_einlieferung', '!=', '202')
            ->where('id_liefertyp', 2)
            ->where('einlieferung_am', '<', Carbon::now()->subHours(config('connections.ESP_RETRY'))->format(self::dateTime))
            ->orWhere(function ($q) {
                $q->whereNull('status_einlieferung')
                    ->whereNull('einlieferung_am')
                    ->where('id_liefertyp', 2);
            })
            ->orWhere(function ($q) {
                $q->where('einlieferung_am', '<', Carbon::now()->subHours(config('connections.ESP_RETRY'))->format(self::dateTime))
                    ->whereNull('antwort_am')
                    ->where('id_liefertyp', 2);
            })
            ->limit(config('connections.esp_transmit_response_limit'))->get();
        foreach ($transmits as $transmit) {
            $this->transmit_doc($transmit->id);
        }
    }
}
