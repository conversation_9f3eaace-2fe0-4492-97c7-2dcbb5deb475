<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\SalesforceApiException;
use App\Models\Font;
use App\Models\Page;
use App\Models\Editordocument;
use App\Models\BlockedArea;
use App\Models\FrontifyRelease;
use App\Jobs\GenerateMcPreview;


class PageController extends Controller
{
    public function get()
    {
        $pages = Page::Where('active', 1)->select('name', 'description', 'height', 'width', 'trim_top', 'trim_right', 'trim_bottom', 'trim_left', 'address_x', 'address_y', 'address_w', 'address_h', 'fold_marks', 'pagenumber_w', 'pagenumber_y', 'sender_y', 'recipient_y', 'blocker_y')->get();
        return response()->json($pages, 200);
    }

    public function getBlockedAreas($pageFormatId = null, $jobId = null)
    {
        $pages = BlockedArea::Where('pageFormatId', $pageFormatId)->Where('jobId', $jobId)->select('jobId', 'pageFormatId', 'block_x', 'block_x', 'block_w', 'block_h')->get();
        return response()->json($pages, 200);
    }

    public function storePages(Request $request)
    {
        $data = $request->all();
        $version = 0;
        if (!empty($data['id']) && !empty($data['css']) && !empty($data['pages'])) {
            $latestDoc = Editordocument::getLastVersion($data['id']);
            if($latestDoc->frontify !== 1) {
                if (!empty($latestDoc)) {
                    $version = $latestDoc->version;
                }
                $version++;
                $newDoc = new Editordocument();
                $newDoc->jobId = $data['id'];
                $newDoc->version = $version;
                $newDoc->css = $data['css'];
                $newDoc->pages = json_encode($data['pages']);
                $newDoc->used_vars = json_encode($data['used_vars']) ?? null;
                $newDoc->user = Auth::id();
                $result = $newDoc->save();

                GenerateMcPreview::dispatch($data['id'])->onQueue('processPrintData');

                if ($result) {
                    return response($version, 200);
                } else {
                    return response('Could not be saved', 409);
                }
            }
            else {
                return response('Already released', 409);
            }
        }
        return response('Bad Request', 400);
    }


    public function getLastPages($jobId)
    {
        $lastEditorDoc = Editordocument::getLastVersion($jobId);
        $editorDoc = [];
        $pages = [];

        if (!empty($lastEditorDoc)) {
            if (!empty($lastEditorDoc->pages)) {
                $pages = json_decode($lastEditorDoc->pages);
            }

            $editorDoc = [
                'id' => $lastEditorDoc->jobId,
                'css' => $lastEditorDoc->css,
                'version' => $lastEditorDoc->version,
                'pages' => $pages,
                'frontify' => $lastEditorDoc->frontify,
                'role' => Auth::user()->getRole(),
                'allowRelease' => Session('userdata')['rechte']['jobs'][$jobId]['freigabe'] ?? 0,
            ];

            return response()->json($editorDoc, 200);
        } else {
            return response()->json('Editor document not found', 404);
        }
    }


    public function setFrontify($jobId)
    {
        $lastEditorDoc = Editordocument::getLastVersion($jobId);
        if (!empty($lastEditorDoc)) {
            $lastEditorDoc->frontify = 1;
            $result = $lastEditorDoc->save();
            $frontifyRelease = new FrontifyRelease;
            $frontifyRelease->user = Auth::id();
            $frontifyRelease->jobId = $jobId;
            $frontifyRelease->release = 1;
            $frontifyRelease->save();

            if ($result) {
                return response('Successful set frontify flag', 200);
            } else {
                return response('frontify flag could not be set', 400);
            }
        } else {
            return response('Document not found', 404);
        }
    }

    public function getFrontify($jobId)
    {
        $lastEditorDoc = Editordocument::getLastVersion($jobId);
        if (!empty($lastEditorDoc)) {
            return response()->json(['frontify' => $lastEditorDoc->frontify], 200);
        } else {
            return response('Document not found', 404);
        }
    }

    public function getPagesHistory($jobId)
    {
        $versionHistory = Editordocument::getVersionHistory($jobId);
        if (!empty($versionHistory)) {
            return response()->json($versionHistory, 200);
        } else {
            return response('Document not found', 404);
        }
    }

    public function deleteFrontify($jobId)
    {
        $lastEditorDoc = Editordocument::getLastVersion($jobId);
        if (!empty($lastEditorDoc)) {
            $lastEditorDoc->frontify = 0;
            $result = $lastEditorDoc->save();
            $frontifyRelease = new FrontifyRelease;
            $frontifyRelease->user = Auth::id();
            $frontifyRelease->jobId = $jobId;
            $frontifyRelease->release = 0;
            $frontifyRelease->save();
            if ($result) {
                return response('Successful deleted frontify flag', 200);
            } else {
                return response('frontify flag could not be deleted', 400);
            }
        } else {
            return response('Document not found', 404);
        }
    }

    public function getPagesByVersion($jobId, $version)
    {
        $lastEditorDoc = Editordocument::getByVersion($jobId, $version);
        $editorDoc = [];
        $pages = [];

        if (!empty($lastEditorDoc)) {
            if (!empty($lastEditorDoc->pages)) {
                $pages = json_decode($lastEditorDoc->pages);
            }

            $editorDoc = [
                'id' => $lastEditorDoc->jobId,
                'css' => $lastEditorDoc->css,
                'version' => $lastEditorDoc->version,
                'pages' => $pages,
                'frontify' => $lastEditorDoc->frontify,
                'role' => Auth::user()->getRole(),
                'allowRelease' => Session('userdata')['rechte']['jobs'][$jobId]['freigabe'] ?? 0,
            ];

            return response()->json($editorDoc, 200);
        } else {
            return response()->json('Editor document not found', 404);
        }
    }

}
