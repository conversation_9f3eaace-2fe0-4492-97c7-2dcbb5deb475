<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Font;

class FontController extends Controller
{
    public function store(Request $file)
    {
        // DUMMY FUNCTION
        $data = $file->all();
        $path = config('app.FONT_DIR');
        $hash = hash('sha256', $data['content']);
        $ext = pathinfo($data['filename'], PATHINFO_EXTENSION);
        $newFileName = $hash . '.' . $ext;
        $newFileNameWithPath = $path . '/' . $newFileName;
        if (!Storage::disk('local')->exists($newFileNameWithPath)) {
            $result = Storage::disk('local')->put($newFileNameWithPath, base64_decode($data['content']));
            if ($result) {
                $imageFile = new Font();
                $imageFile->jobId = $data['jobId'] ?? 0;
                ;
                $imageFile->filename = $newFileName;
                $imageFile->extension = $ext;
                $imageFile->originalFileName = $data['filename'];
                $imageFile->path = $path;
                $imageFile->save();
            }
        }
        return $newFileName;
    }

    public function get($filenameHash)
    {
        $path = config('app.FONT_DIR');
        $fileNameWithPath = $path . '/' . $filenameHash;
        if (Storage::disk('local')->exists($fileNameWithPath)) {
            return base64_encode(Storage::disk('local')->get($fileNameWithPath));
        }
    }


    public function getFonts()
    {
        $relativePath = 'fonts/pdf';
        $absolutePath = public_path($relativePath);
        $fonts = scandir($absolutePath);
        $fontArray = [];

        foreach ($fonts as $font) {
            if (substr($font, 0, 1) !== '.') {
                $fontName = pathinfo(basename($font), PATHINFO_FILENAME);
                $fontArray[] = [
                    'name' => $fontName,
                    'path' => asset($relativePath . '/' . $font),
                    'originalName' => $font,
                ];
            }
        }

        if (!empty($fontArray)) {
            return response()->json($fontArray, 200);
        }
        return response()->json(['error' => 'No fonts found'], 404);
    }


}
