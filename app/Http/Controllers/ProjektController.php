<?php

namespace App\Http\Controllers;

use App\Models\Aktivitaet;
use App\Models\AktivitaetenDoc;
use App\Models\Datei;
use App\Models\Favorit;
use App\Models\Job;
use App\Models\Jobbenutzer;
use App\Models\Jobdokumente;
use App\Models\Kunde;
use App\Models\Rechte;
use App\Models\Taetigkeit;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ProjektController extends Controller
{
    public function get_jobs_by_customer($id)
    {
        if (empty($_GET['suche'])) {
            $daten = $this->kundenjobs($id);
        } else {
            $daten = $this->kundenjobs($id, $_GET['suche']);
        }
        $kunden_tmp = [];
        foreach ($daten['kundenliste'] as $kunde) {
            $kunden_tmp[$kunde->id] = $kunde->kunde;
        }
        return view('projekte', ['daten' => $daten, 'bladetitel' => $kunden_tmp[$id]]);
    }

    public function show($tag = null)
    {
        $userdata = $this->get_userdata();
        if (!empty($_GET['suche'])) {
            $jobs = $this->get_jobs($userdata->id, $_GET['suche'], $tag);
        } else {
            $jobs = $this->get_jobs($userdata->id, null, $tag);
        }
        $freigaben = 0;
        foreach ($jobs as $job) {
            if ($job->freigegeben) {
                $freigaben++;
            }
        }
        $userdata['rechte'] = ['typ' => $userdata['typ']];
        $user_tmp = Rechte::where('id_benutzer', $userdata->id)->get()->toArray();
        $userrechte = [];
        foreach ($user_tmp as $rechte) {
            $userrechte[$rechte['id_job']] = $rechte;
        }
        $daten = ['jobs' => $jobs, 'aufgaben' => $jobs, 'userdata' => $userdata, 'countfreigaben' => $freigaben];
        return view('projekte', ['daten' => $daten, 'bladetitel' => 'Alle', 'userrechte' => $userrechte]);
    }

    private function kundenjobs($id_kunde)
    {
        $userdata = $this->get_userdata();
        $jobs = $this->get_kundenjobs($id_kunde);
        if (!empty($_GET['suche'])) {
            $aufgaben = $this->get_kundenaufgaben($id_kunde, $_GET['suche']);
        } else {
            $aufgaben = $this->get_kundenaufgaben($id_kunde);
        }
        $kundenliste = $this->get_kundenliste($userdata->id);
        $summejobs = 0;
        foreach ($kundenliste as $kunde) {
            $summejobs += $kunde->anzahl_jobs;
        }
        $alle['id'] = '0';
        $alle['kunde'] = 'Alle';
        $alle['aktiv'] = '1';
        $alle['anzahl_jobs'] = $summejobs;
        array_unshift($kundenliste, (object)$alle);
        return ['jobs' => $jobs, 'aufgaben' => $aufgaben, 'kundenliste' => $kundenliste, 'userdata' => $userdata];
    }

    public function favoriten()
    {
        $jobs = $this->get_jobliste(1);
        return view('favoriten', ['jobs' => $jobs]);
    }

    public function archiv()
    {
        $jobs = $this->get_archivliste();
        return view('archiv', ['jobs' => $jobs]);
    }

    private function get_userdata()
    {
        $user = User::current();
        $jobs = [];

        $user['typ'] = $user->role();
        $user_rechte = Jobbenutzer::Where('id_benutzer', $user->id)->get();
        if ($user_rechte) {
            foreach ($user_rechte as $value) {
                $jobs[$value->id_job] = $value;
            }
        }
        $user['jobs'] = $jobs;
        return $user;
    }

    private function get_jobliste($fav)
    {
        return $this->getCall('/jobs/favs/' . $fav, '');
    }

    // TODO: this function returns an empty array
    private function get_archivliste()
    {
        return [];
    }

    private function get_kundenliste($id)
    {
        return $this->getCall('/kunden/user/' . $id, '');
    }

    public static function get_jobactivities($id)
    {
        $jobinfos = [];
        $jobs = Jobbenutzer::where('id_benutzer', '=', $id)->get();
        foreach ($jobs as $job) {
            $job_tmp = Job::find($job['id_job']);
            if (!empty($job_tmp['id_kunde'])) {
                $kunde = Kunde::find($job_tmp['id_kunde']);
            }
            if (!empty($job_tmp) && !empty($kunde) && ($job_tmp['dateien_geloescht'] < 1)) {
                $loeschdatum = Carbon::createFromDate($job_tmp['jobende'])->addDays($kunde['loeschtage'])->format('Y-m-d');
                $diff = Carbon::now()->diffInDays(Carbon::parse($job_tmp['jobende']), false);
                $job_tmp['diff'] = $diff;
                $job_tmp['loeschdatum'] = $loeschdatum;
                if ($kunde['kunde'] != '') {
                    $jobinfos[$kunde['kunde']][] = $job_tmp;
                }
            }
        }
        ksort($jobinfos);
        return $jobinfos;
    }

    private function get_kundenjobs($id)
    {
        return  $this->getCall('/jobs/kunde/' . $id, '');
    }

    public static function get_jobs($id, $suche = '', $tag = null)
    {
        $id_user = Auth::id();
        $jobarray = [];
        $user = User::find($id_user);
        $jobs = $user->jobs;
        foreach ($jobs as $value) {
            $value->last_action = ProjektController::get_lasttaetigkeiten($value->id);
            $fav = Favorit::where('id_user', $id_user)->where('id_job', $value->id)->get()->toArray();
            $letzte_lieferung = Datei::where('id_job', $value->id)->orderBy('created_at', 'DESC')->first();
            $count = 0;
            if (!empty($letzte_lieferung)) {
                $count = $letzte_lieferung->datensaetze;
            }
            $docs = Jobdokumente::where('id_job', $value->id)
                ->get();
            $doccount = 0;
            foreach ($docs as $doc) {
                if (!empty($doc) && ($doc->anzeige_kunde == 1)) {
                    $doccount++;
                }
            }
            $ablehnungenkunde = AktivitaetenDoc::Where('dokument_aktivitaeten.id_job', $value->id)
                ->Where('dokument_aktivitaeten.id_docaktivitaet', 7)
                ->distinct()
                ->get('id_document2job');
            $doccount -= count($ablehnungenkunde);

            $id_docaktivitaet = 2;
            if (User::current()->is_agentur()) {
                $id_docaktivitaet = 3;
            }

            $docfreigaben = AktivitaetenDoc::where('id_job', $value->id)
                ->where('id_docaktivitaet', $id_docaktivitaet)
                ->distinct()
                ->count('id_document2job');

            $value->j_favorit = (int) (!empty($fav));
            $value->count = $count;
            $value->doccount = $doccount;

            $value->count_freigaben = $docfreigaben;
            if (User::current()->is_agentur()) {
                $value->count_freigaben = $docfreigaben - count($ablehnungenkunde);
            }
            if ($value->count_freigaben < 0) {
                $value->count_freigaben = 0;
            }
            if (User::current()->is_agentur() || (User::current()->is_kunde() && $value->fuer_kunde_frei)) {
                $jobarray[] = $value;
            }
        }
        rsort($jobarray);
        return $jobarray;
    }

    public static function get_jobs_done($id, $suche = '')
    {
        $id_user = Auth::id();
        $jobarray = [];
        $user = User::find($id_user);
        $jobs = $user->jobs_done;

        foreach ($jobs as $value) {
            $value->last_action = ProjektController::get_lasttaetigkeiten($value->id);
            $fav = Favorit::where('id_user', $id_user)->where('id_job', $value->id)->get()->toArray();
            $letzte_lieferung = Datei::where('id_job', $value->id)->orderBy('created_at', 'DESC')->first();

            $count = 0;
            if (!empty($letzte_lieferung)) {
                $count = $letzte_lieferung->datensaetze;
            }

            $value->j_favorit = (int) (!empty($fav));
            $value->count = $count;
            $jobarray[] = $value;
        }

        return $jobarray;
    }

    public static function get_lasttaetigkeiten($id)
    {
        $jobinfo = Job::find($id);
        $id_aktivitaet = 0;
        if (User::current()->is_agentur()) {
            $id_aktivitaeten = Aktivitaet::Where('id_job', $id)->pluck('id')->toArray();
            if (!empty($id_aktivitaeten)) {
                $id_aktivitaet = max($id_aktivitaeten);
            }
        } else if (User::current()->is_kunde()) {
            $id_aktivitaet = $jobinfo->letzte_aktivitaet_kunde();
        } else if (User::current()->is_dienstleister()) {
            $id_aktivitaet = $jobinfo->letzte_aktivitaet_dienstleister();
        }
        if (!empty($id_aktivitaet)) {
            $laktivitaet = Aktivitaet::find($id_aktivitaet);
        }

        $dateiinfo = [];
        $userinfo = [];
        if (!empty($laktivitaet->id_dateien)) {
            $dateiinfo = Datei::find($laktivitaet->id_dateien);
            $userinfo = User::find($laktivitaet->id_benutzer);
        }

        $aktionsinfo = [];
        if (!empty($laktivitaet->id_taetigkeit)) {
            $aktionsinfo = Taetigkeit::find($laktivitaet->id_taetigkeit);
        }

        $laktivitaet['filename'] = [];
        if (!empty($dateiinfo['org_name'])) {
            $laktivitaet['filename'] = $dateiinfo['org_name'];
        }

        $laktivitaet['aktion'] = [];
        if (!empty($aktionsinfo['taetigkeit'])) {
            $laktivitaet['aktion'] = $aktionsinfo['taetigkeit'];
            if (User::current()->is_kunde()) {
                $laktivitaet['aktion'] = $aktionsinfo['taetigkeit_kunde'];
            } else if (User::current()->is_dienstleister()) {
                $laktivitaet['aktion'] = $aktionsinfo['taetigkeit_dienstleister'];
            }
        }

        $laktivitaet['user'] = [];
        if (!empty($userinfo['name'])) {
            $laktivitaet['user'] = $userinfo['vorname'] . ' ' . $userinfo['name'];
        }

        return $laktivitaet;
    }

    private function get_kundenaufgaben($id, $suche = '')
    {
        $payload = '';
        if (!empty($suche)) {
            return $this->getCall('/aufgaben/kunde/' . $id . '/suche/' . $suche, $payload);
        }

        return $this->getCall('/aufgaben/kunde/' . $id, $payload);
    }
}
