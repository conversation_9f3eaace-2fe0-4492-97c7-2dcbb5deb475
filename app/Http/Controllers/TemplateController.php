<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Template;
use App\Models\User;

class TemplateController extends Controller
{
    public function getTemplates()
    {
        $pages = Template::Where('active', 1)->orderBy('created_at', 'desc')->get();
        $returnPages = [];
        foreach ($pages as $page) {
            $returnPages[] = [
                'id' => $page->id,
                'name' => $page->name,
                'json' => $page->json,
                'user' => User::find($page->user)->name ?? '-',
                'active' => $page->active,
                'createdAt' => $page->created_at->format('Y-m-d H:i:s'),
            ];
        }
        return response()->json($returnPages, 200);
    }

    public function storeTemplate(Request $request)
    {
        $data = $request->all();
        if (!empty($data['name']) && !empty($data['json'])) {
            if (!empty($data['id'])) {
                $template = Template::find($data['id']);
            }
            if (empty($template)) {
                $template = new Template();
            }
            $template->name = $data['name'];
            $template->json = $data['json'];
            $template->user = Auth::user()->id;
            $template->active = 1;
            $result = $template->save();
            if ($result) {
                return response('Successful stored', 200);
            } else {
                return response('Could not be saved', 409);
            }
        }
        return response('Bad Request name and html should be set', 400);
    }

}
