<?php

namespace App\Http\Controllers;

use App\Exceptions\ResponseApiException;
use App\Models\Job;
use App\Models\Log as LogDB;
use App\Models\Printstatus;
use App\Models\Transmits;
use App\Models\TSoAuth;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PrintstatusController extends Controller
{
    /**
     * Enpunkt zum Import von Adressdaten per Payload per JSON
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setStatus(Request $request)
    {
        $this->checktoken($request);
        $payloadContent = $request->getContent();
        $payload = json_decode($payloadContent);
        Log::info('setPrintstatus: ' . $payloadContent);
        if (!isset($payload->jobId)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'jobId missing'], 400);
        }
        if (!isset($payload->stepId)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'stepId missing'], 400);
        }
        if (!isset($payload->process)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'process missing'], 400);
        }
        if (!isset($payload->success)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'success missing'], 400);
        }
        if (!isset($payload->datetime)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'datetime missing'], 400);
        }
        if (!isset($payload->employee)) {
            return response()->json(['errorCode' => 400, 'errorMessage' => 'employee missing'], 400);
        }
        return $this->setPrintstatus(
            $request,
            $payload->jobId,
            $payload->stepId,
            $payload->process,
            $payload->success,
            ( !empty($payload->palDate) ? $payload->palDate : null),
            $payload->datetime,
            $payload->employee,
            ( !empty($payload->remark) ? $payload->remark : null)
        );
    }

    /**
     * Endpunkt zum Überprüfen des Tokens
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checktoken(Request $request)
    {
        try {
            $this->setUpUser($request->bearerToken());
        } catch (ResponseApiException $e) {
            return response()->json($e->getMessage(), 401);
        }
        return response()->json('true', 200);
    }

    /**
     * @param $token
     * @throws ResponseApiException
     */
    private function setUpUser($token)
    {
        if (!$token) {
            throw new ResponseApiException('token missing');
        }
        $this->user = TSoAuth::where('expires_at', '>', now())->where('api_token', $token)->first();
        if (!$this->user) {
            throw new ResponseApiException('token expired', 500);
        }
    }

    /**
     * Import
     *
     * @param Request $request
     * @param $id
     * @param string $fileName
     * @param string $content
     * @param $importId
     * @return \Illuminate\Http\JsonResponse
     */
    private function setPrintstatus($request,
                                    $jobId = null,
                                    $stepIds = null,
                                    $process = null,
                                    $success = null,
                                    $palDate = null,
                                    $datetime = null,
                                    $employee = null,
                                    $remark = null,
    )
    {
        try {
            $this->setUpUser($request->bearerToken());
            Log::info("Received valid setPrintstatus");
            foreach($stepIds AS $stepId){
                $record = new Printstatus();
                $record->jobId =$jobId;
                $record->stepId =$stepId;
                $record->process =$process;
                $record->success = $success;
                $record->palDate = $palDate;
                $record->datetime = $datetime;
                $record->employee = $employee;
                $record->remark = $remark;
                $result = $record->save();
            }
            if ($result) {
                return response()->json( ['message' => 'Created'], 202);
            }
        } catch (ResponseApiException $e) {
            return response()->json($e->getMessage(), 401);
        }
    }

    private function log($debug)
    {
        $log = new LogDB();
        $log->datetime = now();
        $log->debug = $debug;
        return $log->save();
    }
}
