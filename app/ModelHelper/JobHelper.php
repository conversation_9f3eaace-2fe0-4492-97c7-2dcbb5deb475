<?php

namespace App\ModelHelper;


use App\Models\Aktivitaet;
use App\Models\Datei;
use App\Models\File;
use App\Http\Controllers\FileController;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JobHelper
{
    /**
     * @param string $file_name
     * @param string $payload
     * @param int $project_id
     * @param User $user
     * @return integer insertet Activity Id
     */
    public static function store(string $file_name, string $payload, int $project_id, User $user, $stepId, $count_datasets=0)
    {
        Log::info('start file store: ' . $file_name);
        $file = new File;
        $file->file_name = $file_name;
        $file->mime = 'text/csv';
        $size = strlen($payload);
        Log::info('start encrypting file: ' . $file_name);
        $file->file = FileController::file_verschluesseln($payload);
        Log::info('stop encrypting file: ' . $file_name);
        $fileupload = $file->save();
        $insert_id = DB::getPdo()->lastInsertId();

        $dateien = new Datei;
        $dateien->id_job = $project_id;
        $dateien->id_file = $insert_id;
        $dateien->alias_name = $file_name;
        $dateien->org_name = $file_name;
        $dateien->id_kategorie = 0;
        $dateien->dateigroesse = $size;
        $dateien->step_id = $stepId;
        $dateien->save();

        $dateien_id = DB::getPdo()->lastInsertId();
        $tempCount = Datei::return_file_daten($dateien_id);
        $count_datasets = count($tempCount[0]);
        $dateien->datensaetze = $count_datasets;
        $dateien->save();

        $aktivitaeteninsert = new Aktivitaet();
        $aktivitaeteninsert->id_job = $project_id;
        $aktivitaeteninsert->id_dateien = $dateien_id;
        $aktivitaeteninsert->id_taetigkeit = 2;
        $aktivitaeteninsert->datetime = Carbon::now();
        $aktivitaeteninsert->id_benutzer = $user->id;

        $aktivitaeteninsert->save();
        Log::info('stop file store: ' . $file_name);
        return array('activity_id' => DB::getPdo()->lastInsertId(), 'count_datasets' => $count_datasets);
    }
}
