<?php


namespace App\ModelHelper;

use App\Exceptions\SalesforceApiException;
use App\Models\Ftpkunden;
use League\Flysystem\Filesystem;
use League\Flysystem\PhpseclibV2\SftpConnectionProvider;
use League\Flysystem\PhpseclibV2\SftpAdapter;
use Illuminate\Support\Facades\Log;

class SftpHelper
{
    /**
     * @param string $host
     * @param string $port
     * @param string $user
     * @param string $pass
     * @param string $pathAndPath
     * @param string $keyfilepath
     * @return string Content of Remote File
     * @throws SalesforceApiException
     */
    public static function getFileContentFromSftp(string $host, string $port, string $user, string $pass, string $pathAndPath, string $keyfilepath): string
    {
        try {
            Log::info('start fetching file : ' . $pathAndPath);
            $adapter = new SftpAdapter(
                new  SftpConnectionProvider(
                $host,
                $user,
                (!empty($pass) ? $pass : ''),
                (!empty($keyfilepath) ? $keyfilepath : null),
                '',
                $port,
                false,
                10,
                3,
                    '',
                    null
            ),
            '/'
            );
            Log::info('trying to connect to host ' . $host . ' (' . substr($user, 0, 4) . '...) with keyfilepath: ' . $keyfilepath);
            $fileExists = $adapter->fileExists($pathAndPath);
            if(!$fileExists){
                return false;
            }
            else{
                $result = $adapter->read($pathAndPath);
                if (!is_string($result)) {
                    throw new SalesforceApiException('Remote file read failed');
                }
                Log::info('finished fetching file : ' . $pathAndPath);
                return $result;
            }

        } catch (\Exception $e) {
            throw new SalesforceApiException($e->getMessage());
        }
    }

    public static function getCredentialsById(int $id)
    {
        return Ftpkunden::where('id_job', $id)->first();
    }
}
