<?php

namespace App\ModelHelper;

use App\Exceptions\SalesforceApiException;
use Illuminate\Support\Facades\Log;
use League\Flysystem\Filesystem;
use League\Flysystem\PhpseclibV2\SftpConnectionProvider;
use League\Flysystem\PhpseclibV2\SftpAdapter;

class SftpClient
{
    /**
     * @var string
     */
    private $host;
    /**
     * @var string
     */
    private $port;
    /**
     * @var string
     */
    private $user;
    /**
     * @var string
     */
    private $pass;
    /**
     * @var string|null
     */
    private $keyfilepath;
    /**
     * @var string
     */
    private $root;
    /**
     * @var SftpAdapter
     */
    private $adapter;

    public function __construct(string $host, string $port, string $user, string $pass, string|null $keyfilepath, string $root)
    {
        $this->host = $host;
        $this->port = $port;
        $this->user = $user;
        $this->pass = $pass;
        $this->keyfilepath = $keyfilepath;
        $this->root = $root;
    }

    public function connect()
    {
        try {
            $adapter = new SftpAdapter(
                new  SftpConnectionProvider(
                    $this->host,
                    $this->user,
                    (!empty($this->pass) ? $this->pass : ''),
                    (!empty($this->keyfilepath) ? $this->keyfilepath : null),
                    '',
                    $this->port,
                    false,
                    10,
                    3,
                    '',
                    null
                ),
                $this->root,
            );

            $using_password = !empty($this->pass) ? "using a password" : "not using a password";

            Log::info('trying to connect to host ' . $this->host . ' (' . substr($this->user, 0, 4) . '...) with keyfilepath: ' . $this->keyfilepath .' and '. $using_password);
            return $adapter;
        } catch (\Exception $e) {
            Log::info('failed to connect to host ' . $this->host . ' (' . substr($this->user, 0, 4) . '...) with keyfilepath: ' . $this->keyfilepath . ': ' . $e->getMessage());
        }
    }

    public function getAdapter(){
        return $this->adapter;
    }
}
