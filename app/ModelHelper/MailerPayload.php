<?php

namespace App\ModelHelper;

class MailerPayload
{

    /**
     * @var String
     */
    private $id;
    /**
     * @var String
     */
    private $projektname;
    /**
     * @var String
     */
    private $dienstleister;
    /**
     * @var String
     */
    private $anzahlDatensaetze;
    /**
     * @var String
     */
    private $stepId;
    /**
     * @var String
     */
    private $link;
    /**
     * @var String
     */
    private $fehler;
    /**
     * @var String
     */
    private $dateibezeichnung;
    /**
     * @var String
     */
    private $ordnerbezeichnung;
    /**
     * @var String
     */
    private $aktion;


    /**
     * @var String
     */
    private $PAL;


    /**
     * @var String
     */
    private $auslieferungsdatum;

    public function __construct(
        string $id = "",
        string $projektname = "",
        string $dienstleister = "",
        string $anzahlDatensaetze = "",
        string $stepId = "",
        string $link = "",
        string $fehler = "",
        string $dateibezeichnung = "",
        string $ordnerbezeichnung = "",
        string $aktion = "",
        string $PAL = "",
        string $auslieferungsdatum = "")
    {
        if (empty($id)) {
            $id = '-';
        }
        $this->id = $id;
        $this->projektname = $projektname;
        $this->dienstleister = $dienstleister;
        $this->anzahlDatensaetze = $anzahlDatensaetze;
        $this->stepId = $stepId;
        $this->link = $link;
        $this->fehler = $fehler;
        $this->dateibezeichnung = $dateibezeichnung;
        $this->ordnerbezeichnung = $ordnerbezeichnung;
        $this->aktion = $aktion;
        $this->PAL = $PAL;
        $this->auslieferungsdatum = $auslieferungsdatum;
    }

    /**
     * @return String
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return String
     */
    public function getProjektname(): string
    {
        return $this->projektname;
    }

    /**
     * @return String
     */
    public function getDienstleister(): string
    {
        return $this->dienstleister;
    }

    /**
     * @return String
     */
    public function getAnzahlDatensaetze(): string
    {
        return $this->anzahlDatensaetze;
    }

    /**
     * @return String
     */
    public function getStepId(): string
    {
        return $this->stepId;
    }

    /**
     * @return String
     */
    public function getLink(): string
    {
        return $this->link;
    }

    /**
     * @return String
     */
    public function getFehler(): string
    {
        return $this->fehler;
    }

    /**
     * @return String
     */
    public function getDateibezeichnung(): string
    {
        return $this->dateibezeichnung;
    }

    /**
     * @return String
     */
    public function getOrdnerbezeichnung(): string
    {
        return $this->ordnerbezeichnung;
    }

    /**
     * @return String
     */
    public function getAktion(): string
    {
        return $this->aktion;
    }


    /**
     * @return String
     */
    public function getPAL(): string
    {
        return $this->PAL;
    }

    /**
     * @return String
     */
    public function getAuslieferungsdatum(): string
    {
        return $this->auslieferungsdatum;
    }

}
