<?php

namespace App\ModelHelper;

use App\Models\App;
use App\Models\User;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use SendinBlue\Client\Api\TransactionalEmailsApi;
use SendinBlue\Client\Configuration;
use SendinBlue\Client\Model\SendSmtpEmail;
use const PHP_EOL;

class Mailer
{
    /**
     * @var App\Models\User
     */
    private $user;
    /**
     * @var String
     */
    private $template;
    /**
     * @var array
     */
    private $payload;

    public function __construct(User $user, string $template, MailerPayload $payload)
    {
        $this->user = $user;
        $this->template = $template;
        $this->payload = $payload;
    }

    public function execute()
    {
        $maildata = array('email' => $this->user->email,
            'vorname' => $this->user->vorname,
            'name' => $this->user->name,
            'projekt_id' => '#' . $this->payload->getId(),
            'projektname' => $this->payload->getProjektname(),
            'dienstleister' => $this->payload->getDienstleister(),
            'datensaetze' => $this->payload->getAnzahlDatensaetze(),
            'stepid' => $this->payload->getStepId(),
            'link' => config('app.APP_URL') . 'freigabe/' . $this->payload->getId(),
            'pal_datum' => $this->payload->getPAL(),
            'auslieferungsdatum' => $this->payload->getAuslieferungsdatum()
        );
        $this->sendMailByTemplate($this->template, $maildata);
        Log::info('Maildata: ', $maildata);
    }

    public static function sendMailByTemplate(string $template, $data = null)
    {
        if (!empty($template) && !empty($data['email'])) {

            $config = Configuration::getDefaultConfiguration()->setApiKey('api-key', config('connections.SENDINBLUE_API_KEY'));
            $apiInstance = new TransactionalEmailsApi(
                new Client(),
                $config
            );
            $sendSmtpEmail = new SendSmtpEmail(); // \SendinBlue\Client\Model\SendSmtpEmail | Values to send a transactional email
            //$sendSmtpEmail['sender'] = ["email" => config('connections.SENDINBLUE_FROM_EMAIL'), "name" => config('connections.SENDINBLUE_FROM_NAME')];
            $sendSmtpEmail['to'] = array(array('email' => $data['email'], 'name' => $data['vorname'] . ' ' . $data['name']));
            $sendSmtpEmail['templateId'] = (int) config('mailtemplates.'.$template);
            $sendSmtpEmail['params'] = $data;
            $sendSmtpEmail['headers'] = array('X-Mailin-custom' => 'custom_header_1:custom_value_1|custom_header_2:custom_value_2');

            try {
                $result = $apiInstance->sendTransacEmail($sendSmtpEmail);
                //print_r($result);
            } catch (Exception $e) {
                dump($template);
                dd($e->getMessage());
                echo 'Exception when calling TransactionalEmailsApi->sendTransacEmail: ', $e->getMessage(), PHP_EOL;
            }
        }
    }
}
