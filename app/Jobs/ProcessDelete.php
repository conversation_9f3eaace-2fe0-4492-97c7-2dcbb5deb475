<?php

namespace App\Jobs;

use App\Models\Datei;
use App\Models\Druckdateien;
use App\Models\Einzelhashdateien;
use App\Models\Einzelresponses;
use App\Models\PWLs;
use App\Models\Responses;
use App\Models\SonstigeResponses;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessDelete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('queue/delete - starting.');
        Log::debug('queue/delete - starting delete_files');
        Datei::delete_files(config('timings.delfiles'));
        Log::debug('queue/delete - starting delete_druckdaten');
        Druckdateien::delete_druckdaten(config('timings.deldruck'));
        Log::debug('queue/delete - starting delete_archivdateien');
        Einzelhashdateien::delete_archivdateien(config('timings.delarchiv'));
        Log::debug('queue/delete - starting delete_responsedateien');
        Einzelresponses::delete_responsedateien(config('timings.delresponse'));
        Log::debug('queue/delete - starting delete_response');
        Responses::delete_response(config('timings.delresponsealg'));
        Log::debug('queue/delete - starting delete_sonstresponse');
        SonstigeResponses::delete_sonstresponse(config('timings.delresponsealg'));
        Log::debug('queue/delete - starting delete_pwl');
        PWLs::delete_pwl(config('timings.delpwl'));
        Log::info('queue/delete is done');
    }
}
