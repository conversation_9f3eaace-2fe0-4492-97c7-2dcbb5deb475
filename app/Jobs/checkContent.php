<?php

namespace App\Jobs;

use App\Http\Controllers\FileController;
use App\Http\Controllers\FTPController;
use App\Http\Controllers\SalesforceController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class checkContent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $content;
    private $fileName;
    private $id;
    private $activity_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($content = null, $fileName = null, $id  = null, $activity_id)
    {
        $this->content = FileController::file_verschluesseln($content);
        $this->fileName = $fileName;
        $this->id = $id;
        $this->activity_id = $activity_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('queue/checkContent started.');
        SalesforceController::checkContent($this->content, $this->fileName, $this->id, $this->activity_id );
        Log::info('queue/checkContent is done.');
    }
}
