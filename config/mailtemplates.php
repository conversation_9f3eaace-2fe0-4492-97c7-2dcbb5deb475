<?php

return  [
    'EMA<PERSON>_PROJEKTANLAGE_TGD' => env('EMAIL_PROJEKTANLAGE_TGD', '3'),
    'EMAIL_2TE_FREIGABE_TGD' => env('EMAIL_2TE_FREIGABE_TGD', '5'),
    'EMAIL_FREIGABENABSCHLUSS_TGD' => env('EMAIL_FREIGABENABSCHLUSS_TGD', '6'),
    'EMAIL_PROJEKTFREIGABE_TGD' => env('EMAIL_PROJEKTFREIGABE_TGD', '7'),
    'EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE' => env('EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE', '8'),
    'EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE_INFOTGD' => env('EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE_INFOTGD', '9'),
    'EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE' => env('EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE', '10'),
    'EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE_INFOTGD' => env('EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE_INFOTGD', '11'),
    'EMAIL_FREIGABE_ABGELEHNT' => env('EMAIL_FREIGABE_ABGELEHNT', '13'),
    'EMAIL_MC_FREIGABE_ERFOLGT' => env('EMAIL_MC_FREIGABE_ERFOLGT', '12'),
    'EMAIL_PWL_UPLOAD' => env('EMAIL_PWL_UPLOAD', '14'),
    'EMAIL_CRM_BELADUNG' => env('EMAIL_CRM_BELADUNG', '15'),
    'EMAIL_DATENUEBERGABE_AN_DRUCKER' => env('EMAIL_DATENUEBERGABE_AN_DRUCKER', '30'),
    'EMAIL_DATENUEBERGABE_AN_DRUCK_INFOKUNDE' => env('EMAIL_DATENUEBERGABE_AN_DRUCK_INFOKUNDE', '16'),
    'EMAIL_DATENUEBERGABE_VON_DRUCKER_TGD' => env('EMAIL_DATENUEBERGABE_VON_DRUCKER_TGD', '17'),
    'EMAIL_RESPONSE1_KUNDE' => env('EMAIL_RESPONSE1_KUNDE', '18'),
    'EMAIL_RESPONSE1_TGD' => env('EMAIL_RESPONSE1_TGD', '19'),
    'EMAIL_RESPONSE2_KUNDE' => env('EMAIL_RESPONSE2_KUNDE', '20'),
    'EMAIL_RESPONSE2_TGD' => env('EMAIL_RESPONSE2_TGD', '21'),
    'EMAIL_RESPONSE3_KUNDE' => env('EMAIL_RESPONSE3_KUNDE', '31'),
    'EMAIL_RESPONSE3_TGD' => env('EMAIL_RESPONSE3_TGD', '32'),
    'EMAIL_LOESCHBESTAETIGUNG' => env('EMAIL_LOESCHBESTAETIGUNG', '24'),
    'EMAIL_BENUTZER_ANLAGE' => env('EMAIL_BENUTZER_ANLAGE', '25'),
    'EMAIL_PW_AENDERUNG_OK' => env('EMAIL_PW_AENDERUNG_OK', '26'),
    'EMAIL_PW_RESET' => env('EMAIL_PW_RESET', '27'),
    'EMAIL_SYSTEMMELDUNG_TGD' => env('EMAIL_SYSTEMMELDUNG_TGD', '33'),
    'EMAIL_DRUCKFTP_FEHLER_TGD' => env('EMAIL_DRUCKFTP_FEHLER_TGD', '34'),
    'EMAIL_SFTP_DOPPELTER_DATEINAME_DDL' => env('EMAIL_SFTP_DOPPELTER_DATEINAME_DDL', '35'),
    'EMAIL_SFTP_DOPPELTER_DATEINAME_TGD' => env('EMAIL_SFTP_DOPPELTER_DATEINAME_TGD', '36'),
    'EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_DDL' => env('EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_DDL', '37'),
    'EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_TGD' => env('EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_TGD', '38'),
    'TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_DDL' => env('TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_DDL', '39'),
    'TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_TGD' => env('TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_TGD', '40'),
    'TS4SF_EMAIL_AENDERUNG_AUSLIEFERUNGSDATUM' => env('TS4SF_EMAIL_AENDERUNG_AUSLIEFERUNGSDATUM', '49'),
];
