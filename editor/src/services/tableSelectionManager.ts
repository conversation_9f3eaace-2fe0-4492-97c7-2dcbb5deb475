/**
 * TableSelectionManager - Service for managing table cell selections
 * 
 * This service handles rectangular selection expansion, ensuring that selections
 * maintain rectangular shape even when dealing with complex tables containing
 * colspan and rowspan attributes.
 */

import type {
  CellCoordinate,
  TableGrid,
  TableSelection,
  SelectionBounds,
} from '@/types/tableSelection';
import { TableGridAnalyzer } from './tableGridAnalyzer';

export class TableSelectionManager {
  private gridAnalyzer: TableGridAnalyzer;

  constructor() {
    this.gridAnalyzer = new TableGridAnalyzer();
  }

  /**
   * Creates a rectangular selection between two cell coordinates
   * Automatically expands to include all intersecting merged cells
   */
  createRectangularSelection(
    grid: TableGrid,
    startCell: CellCoordinate,
    endCell: CellCoordinate
  ): TableSelection {
    // Calculate initial bounds
    const initialBounds = this.calculateInitialBounds(startCell, endCell);
    
    // Expand bounds to maintain rectangular shape with merged cells
    const expandedBounds = this.expandToRectangle(initialBounds, grid);
    
    // Collect all cells within the expanded bounds
    const selectedCells = this.collectCellsInBounds(expandedBounds, grid);
    
    return {
      startCell,
      endCell,
      selectedCells,
      selectionBounds: expandedBounds,
      isActive: true,
    };
  }

  /**
   * Expands selection bounds to maintain rectangular shape
   * Handles merged cells and span intersections
   */
  expandToRectangle(bounds: SelectionBounds, grid: TableGrid): SelectionBounds {
    let { startRow, endRow, startCol, endCol } = bounds;
    
    // Ensure bounds are in correct order
    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);
    
    // Use the grid analyzer's expansion logic
    const expanded = this.gridAnalyzer.calculateExpandedBounds(
      grid,
      minRow,
      maxRow,
      minCol,
      maxCol
    );
    
    return {
      startRow: expanded.startRow,
      endRow: expanded.endRow,
      startCol: expanded.startCol,
      endCol: expanded.endCol,
    };
  }

  /**
   * Calculates initial bounds from start and end cell coordinates
   */
  private calculateInitialBounds(
    startCell: CellCoordinate,
    endCell: CellCoordinate
  ): SelectionBounds {
    return {
      startRow: Math.min(startCell.row, endCell.row),
      endRow: Math.max(startCell.row, endCell.row),
      startCol: Math.min(startCell.col, endCell.col),
      endCol: Math.max(startCell.col, endCell.col),
    };
  }

  /**
   * Collects all cells within the specified bounds
   */
  collectCellsInBounds(
    bounds: SelectionBounds,
    grid: TableGrid
  ): Set<CellCoordinate> {
    return this.gridAnalyzer.getCellsInBounds(
      grid,
      bounds.startRow,
      bounds.endRow,
      bounds.startCol,
      bounds.endCol
    );
  }

  /**
   * Validates that a selection bounds is within table limits
   */
  validateSelectionBounds(bounds: SelectionBounds, grid: TableGrid): boolean {
    return (
      bounds.startRow >= 0 &&
      bounds.endRow < grid.rowCount &&
      bounds.startCol >= 0 &&
      bounds.endCol < grid.colCount &&
      bounds.startRow <= bounds.endRow &&
      bounds.startCol <= bounds.endCol
    );
  }

  /**
   * Calculates the area (number of logical cells) of a selection
   */
  calculateSelectionArea(bounds: SelectionBounds): number {
    const rows = bounds.endRow - bounds.startRow + 1;
    const cols = bounds.endCol - bounds.startCol + 1;
    return rows * cols;
  }

  /**
   * Checks if two selections overlap
   */
  selectionsOverlap(bounds1: SelectionBounds, bounds2: SelectionBounds): boolean {
    return !(
      bounds1.endRow < bounds2.startRow ||
      bounds2.endRow < bounds1.startRow ||
      bounds1.endCol < bounds2.startCol ||
      bounds2.endCol < bounds1.startCol
    );
  }

  /**
   * Merges two selection bounds into a single rectangular bounds
   */
  mergeSelectionBounds(bounds1: SelectionBounds, bounds2: SelectionBounds): SelectionBounds {
    return {
      startRow: Math.min(bounds1.startRow, bounds2.startRow),
      endRow: Math.max(bounds1.endRow, bounds2.endRow),
      startCol: Math.min(bounds1.startCol, bounds2.startCol),
      endCol: Math.max(bounds1.endCol, bounds2.endCol),
    };
  }

  /**
   * Constrains selection bounds to table limits
   */
  constrainBoundsToTable(bounds: SelectionBounds, grid: TableGrid): SelectionBounds {
    return {
      startRow: Math.max(0, bounds.startRow),
      endRow: Math.min(grid.rowCount - 1, bounds.endRow),
      startCol: Math.max(0, bounds.startCol),
      endCol: Math.min(grid.colCount - 1, bounds.endCol),
    };
  }

  /**
   * Extends selection bounds in a specific direction
   * Used for keyboard selection extension
   */
  extendSelectionBounds(
    currentBounds: SelectionBounds,
    direction: 'up' | 'down' | 'left' | 'right',
    steps: number = 1,
    grid: TableGrid
  ): SelectionBounds {
    let newBounds = { ...currentBounds };

    switch (direction) {
      case 'up':
        newBounds.startRow = Math.max(0, currentBounds.startRow - steps);
        break;
      case 'down':
        newBounds.endRow = Math.min(grid.rowCount - 1, currentBounds.endRow + steps);
        break;
      case 'left':
        newBounds.startCol = Math.max(0, currentBounds.startCol - steps);
        break;
      case 'right':
        newBounds.endCol = Math.min(grid.colCount - 1, currentBounds.endCol + steps);
        break;
    }

    // Expand to maintain rectangular shape with merged cells
    return this.expandToRectangle(newBounds, grid);
  }

  /**
   * Creates a selection that encompasses the entire table
   */
  selectAll(grid: TableGrid): SelectionBounds {
    if (grid.rowCount === 0 || grid.colCount === 0) {
      return {
        startRow: 0,
        endRow: 0,
        startCol: 0,
        endCol: 0,
      };
    }

    return {
      startRow: 0,
      endRow: grid.rowCount - 1,
      startCol: 0,
      endCol: grid.colCount - 1,
    };
  }

  /**
   * Finds the next cell in a given direction from current position
   * Useful for keyboard navigation
   */
  findNextCell(
    grid: TableGrid,
    currentRow: number,
    currentCol: number,
    direction: 'up' | 'down' | 'left' | 'right'
  ): CellCoordinate | null {
    let nextRow = currentRow;
    let nextCol = currentCol;

    switch (direction) {
      case 'up':
        nextRow = currentRow - 1;
        break;
      case 'down':
        nextRow = currentRow + 1;
        break;
      case 'left':
        nextCol = currentCol - 1;
        break;
      case 'right':
        nextCol = currentCol + 1;
        break;
    }

    return this.gridAnalyzer.getCellAtPosition(grid, nextRow, nextCol);
  }

  /**
   * Updates an existing selection with new end cell
   * Maintains rectangular shape and validates constraints
   */
  updateSelection(
    currentSelection: TableSelection,
    newEndCell: CellCoordinate,
    grid: TableGrid
  ): TableSelection {
    const newSelection = this.createRectangularSelection(
      grid,
      currentSelection.startCell,
      newEndCell
    );

    // Validate the new selection
    if (!this.validateSelection(newSelection, grid)) {
      // Return constrained selection if validation fails
      const constrainedBounds = this.constrainBoundsToTable(newSelection.selectionBounds, grid);
      return {
        ...newSelection,
        selectionBounds: constrainedBounds,
        selectedCells: this.collectCellsInBounds(constrainedBounds, grid),
      };
    }

    return newSelection;
  }

  /**
   * Validates that a selection is valid within the grid constraints
   */
  validateSelection(selection: TableSelection, grid: TableGrid): boolean {
    // Check bounds validity
    if (!this.validateSelectionBounds(selection.selectionBounds, grid)) {
      return false;
    }

    // Check that all selected cells are within the bounds
    for (const cell of selection.selectedCells) {
      if (
        cell.row < selection.selectionBounds.startRow ||
        cell.row > selection.selectionBounds.endRow ||
        cell.col < selection.selectionBounds.startCol ||
        cell.col > selection.selectionBounds.endCol
      ) {
        return false;
      }
    }

    // Check that selection maintains rectangular shape
    const expectedArea = this.calculateSelectionArea(selection.selectionBounds);
    const actualCells = this.collectCellsInBounds(selection.selectionBounds, grid);
    
    // For rectangular selection, we should have cells covering the entire bounds area
    // (though some positions might reference the same merged cell)
    return actualCells.size > 0 && actualCells.size <= expectedArea;
  }

  /**
   * Clears the current selection
   */
  clearSelection(): TableSelection {
    // Create an empty selection with dummy coordinates
    const emptyCell: CellCoordinate = {
      row: 0,
      col: 0,
      rowSpan: 1,
      colSpan: 1,
      element: document.createElement('td') as HTMLTableCellElement,
      id: 'empty',
    };

    return {
      startCell: emptyCell,
      endCell: emptyCell,
      selectedCells: new Set(),
      selectionBounds: {
        startRow: 0,
        endRow: 0,
        startCol: 0,
        endCol: 0,
      },
      isActive: false,
    };
  }

  /**
   * Checks if a cell is within the current selection
   */
  isCellSelected(cell: CellCoordinate, selection: TableSelection): boolean {
    if (!selection.isActive) {
      return false;
    }

    return selection.selectedCells.has(cell);
  }

  /**
   * Checks if a cell at specific coordinates is within selection bounds
   */
  isCellInSelectionBounds(
    row: number,
    col: number,
    selection: TableSelection
  ): boolean {
    if (!selection.isActive) {
      return false;
    }

    const bounds = selection.selectionBounds;
    return (
      row >= bounds.startRow &&
      row <= bounds.endRow &&
      col >= bounds.startCol &&
      col <= bounds.endCol
    );
  }

  /**
   * Gets all unique DOM elements in the current selection
   */
  getSelectedElements(selection: TableSelection): Set<HTMLTableCellElement> {
    const elements = new Set<HTMLTableCellElement>();
    
    for (const cell of selection.selectedCells) {
      elements.add(cell.element);
    }
    
    return elements;
  }

  /**
   * Calculates selection statistics
   */
  getSelectionStats(selection: TableSelection): {
    cellCount: number;
    uniqueElementCount: number;
    area: number;
    bounds: SelectionBounds;
  } {
    return {
      cellCount: selection.selectedCells.size,
      uniqueElementCount: this.getSelectedElements(selection).size,
      area: this.calculateSelectionArea(selection.selectionBounds),
      bounds: selection.selectionBounds,
    };
  }

  /**
   * Creates a selection from a single cell
   */
  createSingleCellSelection(cell: CellCoordinate): TableSelection {
    return {
      startCell: cell,
      endCell: cell,
      selectedCells: new Set([cell]),
      selectionBounds: {
        startRow: cell.row,
        endRow: cell.row,
        startCol: cell.col,
        endCol: cell.col,
      },
      isActive: true,
    };
  }

  /**
   * Extends current selection to include a new cell
   * Maintains rectangular shape
   */
  extendSelectionToCell(
    currentSelection: TableSelection,
    targetCell: CellCoordinate,
    grid: TableGrid
  ): TableSelection {
    if (!currentSelection.isActive) {
      return this.createSingleCellSelection(targetCell);
    }

    return this.createRectangularSelection(
      grid,
      currentSelection.startCell,
      targetCell
    );
  }

  /**
   * Checks if selection can be extended in a given direction
   */
  canExtendSelection(
    selection: TableSelection,
    direction: 'up' | 'down' | 'left' | 'right',
    grid: TableGrid
  ): boolean {
    if (!selection.isActive) {
      return false;
    }

    const bounds = selection.selectionBounds;
    
    switch (direction) {
      case 'up':
        return bounds.startRow > 0;
      case 'down':
        return bounds.endRow < grid.rowCount - 1;
      case 'left':
        return bounds.startCol > 0;
      case 'right':
        return bounds.endCol < grid.colCount - 1;
      default:
        return false;
    }
  }

  // ===== COORDINATE MAPPING UTILITIES =====

  /**
   * Converts DOM mouse coordinates to logical grid position
   * Returns the cell coordinate at the given screen position
   */
  getGridPositionFromDOMCoordinates(
    grid: TableGrid,
    x: number,
    y: number,
    tableElement: HTMLTableElement
  ): CellCoordinate | null {
    // Get the element at the specified coordinates
    const elementAtPoint = document.elementFromPoint(x, y);
    
    if (!elementAtPoint) {
      return null;
    }

    // Find the closest table cell
    const cellElement = elementAtPoint.closest('td, th') as HTMLTableCellElement;
    
    if (!cellElement || !tableElement.contains(cellElement)) {
      return null;
    }

    // Look up the cell coordinate from the grid
    return this.gridAnalyzer.getCellCoordinate(grid, cellElement);
  }

  /**
   * Converts logical grid coordinates to DOM bounding rectangle
   * Returns the visual bounds of a cell at the given grid position
   */
  getDOMBoundsFromGridPosition(
    grid: TableGrid,
    row: number,
    col: number
  ): DOMRect | null {
    const cell = this.gridAnalyzer.getCellAtPosition(grid, row, col);
    
    if (!cell) {
      return null;
    }

    return cell.element.getBoundingClientRect();
  }

  /**
   * Finds the cell at specific logical grid coordinates
   * This is a convenience wrapper around the grid analyzer
   */
  getCellAtGridPosition(
    grid: TableGrid,
    row: number,
    col: number
  ): CellCoordinate | null {
    return this.gridAnalyzer.getCellAtPosition(grid, row, col);
  }

  /**
   * Finds the cell coordinate for a given DOM element
   * Returns null if the element is not part of the table
   */
  getCellCoordinateFromElement(
    grid: TableGrid,
    element: HTMLTableCellElement
  ): CellCoordinate | null {
    return this.gridAnalyzer.getCellCoordinate(grid, element);
  }

  /**
   * Calculates the visual boundaries of a selection in DOM coordinates
   * Returns the bounding rectangle that encompasses all selected cells
   */
  getSelectionDOMBounds(selection: TableSelection): DOMRect | null {
    if (!selection.isActive || selection.selectedCells.size === 0) {
      return null;
    }

    let minLeft = Number.POSITIVE_INFINITY;
    let minTop = Number.POSITIVE_INFINITY;
    let maxRight = Number.NEGATIVE_INFINITY;
    let maxBottom = Number.NEGATIVE_INFINITY;

    // Calculate bounds from all selected cell elements
    for (const cell of selection.selectedCells) {
      const rect = cell.element.getBoundingClientRect();
      
      minLeft = Math.min(minLeft, rect.left);
      minTop = Math.min(minTop, rect.top);
      maxRight = Math.max(maxRight, rect.right);
      maxBottom = Math.max(maxBottom, rect.bottom);
    }

    // Create a DOMRect-like object
    return new DOMRect(
      minLeft,
      minTop,
      maxRight - minLeft,
      maxBottom - minTop
    );
  }

  /**
   * Calculates the logical grid boundaries of a cell including its spans
   * Returns the start and end coordinates that the cell occupies
   */
  getCellLogicalBounds(cell: CellCoordinate): {
    startRow: number;
    endRow: number;
    startCol: number;
    endCol: number;
  } {
    return {
      startRow: cell.row,
      endRow: cell.row + cell.rowSpan - 1,
      startCol: cell.col,
      endCol: cell.col + cell.colSpan - 1,
    };
  }

  /**
   * Checks if a point (in DOM coordinates) is within a cell's visual bounds
   */
  isPointInCell(
    cell: CellCoordinate,
    x: number,
    y: number
  ): boolean {
    const rect = cell.element.getBoundingClientRect();
    
    return (
      x >= rect.left &&
      x <= rect.right &&
      y >= rect.top &&
      y <= rect.bottom
    );
  }

  /**
   * Finds all cells that intersect with a given DOM rectangle
   * Useful for drag selection operations
   */
  getCellsInDOMRect(
    grid: TableGrid,
    rect: DOMRect
  ): Set<CellCoordinate> {
    const intersectingCells = new Set<CellCoordinate>();

    // Check each cell in the grid
    for (const cell of grid.elementMap.values()) {
      const cellRect = cell.element.getBoundingClientRect();
      
      // Check if rectangles intersect
      if (this.rectanglesIntersect(rect, cellRect)) {
        intersectingCells.add(cell);
      }
    }

    return intersectingCells;
  }

  /**
   * Helper method to check if two rectangles intersect
   */
  private rectanglesIntersect(rect1: DOMRect, rect2: DOMRect): boolean {
    return !(
      rect1.right < rect2.left ||
      rect2.right < rect1.left ||
      rect1.bottom < rect2.top ||
      rect2.bottom < rect1.top
    );
  }

  /**
   * Converts a selection bounds to the corresponding DOM rectangle
   * This gives the visual representation of the logical selection
   */
  getSelectionBoundsDOMRect(
    bounds: SelectionBounds,
    grid: TableGrid
  ): DOMRect | null {
    // Get the top-left and bottom-right cells
    const topLeftCell = this.gridAnalyzer.getCellAtPosition(grid, bounds.startRow, bounds.startCol);
    const bottomRightCell = this.gridAnalyzer.getCellAtPosition(grid, bounds.endRow, bounds.endCol);

    if (!topLeftCell || !bottomRightCell) {
      return null;
    }

    const topLeftRect = topLeftCell.element.getBoundingClientRect();
    const bottomRightRect = bottomRightCell.element.getBoundingClientRect();

    return new DOMRect(
      topLeftRect.left,
      topLeftRect.top,
      bottomRightRect.right - topLeftRect.left,
      bottomRightRect.bottom - topLeftRect.top
    );
  }

  /**
   * Finds the closest cell to a given DOM coordinate point
   * Useful when the exact point doesn't hit a cell directly
   */
  getClosestCellToDOMPoint(
    grid: TableGrid,
    x: number,
    y: number
  ): CellCoordinate | null {
    let closestCell: CellCoordinate | null = null;
    let minDistance = Number.POSITIVE_INFINITY;

    for (const cell of grid.elementMap.values()) {
      const rect = cell.element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const distance = Math.sqrt(
        Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        closestCell = cell;
      }
    }

    return closestCell;
  }

  /**
   * Calculates the offset of a point within a cell
   * Returns normalized coordinates (0-1) within the cell bounds
   */
  getPointOffsetInCell(
    cell: CellCoordinate,
    x: number,
    y: number
  ): { offsetX: number; offsetY: number } | null {
    const rect = cell.element.getBoundingClientRect();
    
    if (!this.isPointInCell(cell, x, y)) {
      return null;
    }

    return {
      offsetX: (x - rect.left) / rect.width,
      offsetY: (y - rect.top) / rect.height,
    };
  }

  /**
   * Snaps a DOM coordinate to the nearest cell boundary
   * Useful for precise selection operations
   */
  snapToNearestCellBoundary(
    grid: TableGrid,
    x: number,
    y: number,
    snapThreshold: number = 10
  ): { x: number; y: number; cell: CellCoordinate | null } {
    const closestCell = this.getClosestCellToDOMPoint(grid, x, y);
    
    if (!closestCell) {
      return { x, y, cell: null };
    }

    const rect = closestCell.element.getBoundingClientRect();
    let snappedX = x;
    let snappedY = y;

    // Snap to horizontal boundaries
    if (Math.abs(x - rect.left) <= snapThreshold) {
      snappedX = rect.left;
    } else if (Math.abs(x - rect.right) <= snapThreshold) {
      snappedX = rect.right;
    }

    // Snap to vertical boundaries
    if (Math.abs(y - rect.top) <= snapThreshold) {
      snappedY = rect.top;
    } else if (Math.abs(y - rect.bottom) <= snapThreshold) {
      snappedY = rect.bottom;
    }

    return { x: snappedX, y: snappedY, cell: closestCell };
  }
}