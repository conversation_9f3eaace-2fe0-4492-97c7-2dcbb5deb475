/**
 * TableGridAnalyzer - Service for analyzing table structure and creating logical coordinate system
 * 
 * This service handles complex table structures with colspan and rowspan attributes,
 * creating a normalized grid representation that enables rectangular selection algorithms.
 */

import type {
  CellCoordinate,
  TableGrid,
  TableGridAnalysisOptions,
} from '@/types/tableSelection';

export class TableGridAnalyzer {
  private options: Required<TableGridAnalysisOptions>;

  constructor(options: TableGridAnalysisOptions = {}) {
    this.options = {
      handleMalformed: true,
      maxSize: 10000, // 100x100 table max
      ...options,
    };
  }

  /**
   * Analyzes a table element and creates a logical grid representation
   */
  analyzeTable(tableElement: HTMLTableElement): TableGrid {
    if (!tableElement) {
      throw new Error('Table element is required');
    }

    // Get all rows from tbody, thead, and tfoot
    const rows = this.getAllTableRows(tableElement);
    
    if (rows.length === 0) {
      return this.createEmptyGrid();
    }

    // First pass: determine grid dimensions
    const { rowCount, colCount } = this.calculateGridDimensions(rows);
    
    // Validate size constraints
    if (rowCount * colCount > this.options.maxSize) {
      throw new Error(`Table too large: ${rowCount}x${colCount} exceeds maximum size of ${this.options.maxSize} cells`);
    }

    // Create empty grid structure
    const grid = this.createEmptyGrid(rowCount, colCount);
    
    // Second pass: map cells to grid positions
    this.mapCellsToGrid(rows, grid);
    
    // Build lookup maps
    this.buildLookupMaps(grid);
    
    return grid;
  }

  /**
   * Gets all table rows from tbody, thead, and tfoot sections
   */
  private getAllTableRows(tableElement: HTMLTableElement): HTMLTableRowElement[] {
    const rows: HTMLTableRowElement[] = [];
    
    // Get rows from tbody (most common)
    const tbody = tableElement.querySelector('tbody');
    if (tbody) {
      rows.push(...Array.from(tbody.querySelectorAll('tr')));
    }
    
    // Get rows from thead
    const thead = tableElement.querySelector('thead');
    if (thead) {
      rows.unshift(...Array.from(thead.querySelectorAll('tr')));
    }
    
    // Get rows from tfoot
    const tfoot = tableElement.querySelector('tfoot');
    if (tfoot) {
      rows.push(...Array.from(tfoot.querySelectorAll('tr')));
    }
    
    // Fallback: get direct tr children if no tbody/thead/tfoot
    if (rows.length === 0) {
      rows.push(...Array.from(tableElement.querySelectorAll('tr')));
    }
    
    return rows;
  }

  /**
   * Calculates the logical grid dimensions by analyzing all cells and their spans
   */
  private calculateGridDimensions(rows: HTMLTableRowElement[]): { rowCount: number; colCount: number } {
    let maxColCount = 0;
    
    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      const cells = Array.from(rows[rowIndex].querySelectorAll('td, th'));
      let colCursor = 0;
      
      for (const cell of cells) {
        const colSpan = this.getCellSpan(cell, 'colspan');
        colCursor += colSpan;
      }
      
      maxColCount = Math.max(maxColCount, colCursor);
    }
    
    return {
      rowCount: rows.length,
      colCount: maxColCount,
    };
  }

  /**
   * Creates an empty grid structure
   */
  private createEmptyGrid(rowCount = 0, colCount = 0): TableGrid {
    const cells: (CellCoordinate | null)[][] = [];
    
    for (let row = 0; row < rowCount; row++) {
      cells[row] = new Array(colCount).fill(null);
    }
    
    return {
      cells,
      rowCount,
      colCount,
      spanMap: new Map(),
      elementMap: new Map(),
    };
  }

  /**
   * Maps actual table cells to their logical grid positions
   */
  private mapCellsToGrid(rows: HTMLTableRowElement[], grid: TableGrid): void {
    // Track which grid positions are occupied by rowspan cells
    const occupiedPositions = new Set<string>();
    
    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      const cells = Array.from(rows[rowIndex].querySelectorAll('td, th'));
      let colCursor = 0;
      
      for (const cellElement of cells) {
        // Find the next available column position
        while (occupiedPositions.has(`${rowIndex},${colCursor}`)) {
          colCursor++;
        }
        
        const colSpan = this.getCellSpan(cellElement, 'colspan');
        const rowSpan = this.getCellSpan(cellElement, 'rowspan');
        
        // Create cell coordinate
        const cellCoord: CellCoordinate = {
          row: rowIndex,
          col: colCursor,
          rowSpan,
          colSpan,
          element: cellElement as HTMLTableCellElement,
          id: `${rowIndex}-${colCursor}`,
        };
        
        // Fill the grid positions occupied by this cell
        for (let r = rowIndex; r < rowIndex + rowSpan && r < grid.rowCount; r++) {
          for (let c = colCursor; c < colCursor + colSpan && c < grid.colCount; c++) {
            if (grid.cells[r] && grid.cells[r][c] === null) {
              grid.cells[r][c] = cellCoord;
            }
            
            // Mark positions as occupied for future rows if this cell has rowspan > 1
            if (r > rowIndex) {
              occupiedPositions.add(`${r},${c}`);
            }
          }
        }
        
        colCursor += colSpan;
      }
    }
  }

  /**
   * Builds lookup maps for efficient cell coordinate retrieval
   */
  private buildLookupMaps(grid: TableGrid): void {
    grid.spanMap.clear();
    grid.elementMap.clear();
    
    for (let row = 0; row < grid.rowCount; row++) {
      for (let col = 0; col < grid.colCount; col++) {
        const cell = grid.cells[row][col];
        if (cell) {
          const key = `${row},${col}`;
          grid.spanMap.set(key, cell);
          grid.elementMap.set(cell.element, cell);
        }
      }
    }
  }

  /**
   * Gets the span value (colspan or rowspan) from a cell element
   */
  private getCellSpan(cell: Element, spanType: 'colspan' | 'rowspan'): number {
    const spanValue = cell.getAttribute(spanType);
    
    if (!spanValue) {
      return 1;
    }
    
    const parsed = parseInt(spanValue, 10);
    
    // Handle malformed span values
    if (this.options.handleMalformed) {
      if (isNaN(parsed) || parsed < 1) {
        return 1;
      }
      // Reasonable maximum to prevent performance issues
      return Math.min(parsed, 100);
    }
    
    if (isNaN(parsed) || parsed < 1) {
      throw new Error(`Invalid ${spanType} value: ${spanValue}`);
    }
    
    return parsed;
  }

  /**
   * Finds the cell coordinate at a specific grid position
   */
  getCellAtPosition(grid: TableGrid, row: number, col: number): CellCoordinate | null {
    if (row < 0 || row >= grid.rowCount || col < 0 || col >= grid.colCount) {
      return null;
    }
    
    return grid.cells[row][col];
  }

  /**
   * Gets the cell coordinate for a DOM element
   */
  getCellCoordinate(grid: TableGrid, element: HTMLTableCellElement): CellCoordinate | null {
    return grid.elementMap.get(element) || null;
  }

  /**
   * Finds all cells that intersect with a given rectangular bounds
   */
  getCellsInBounds(
    grid: TableGrid,
    startRow: number,
    endRow: number,
    startCol: number,
    endCol: number
  ): Set<CellCoordinate> {
    const cells = new Set<CellCoordinate>();
    
    const minRow = Math.max(0, Math.min(startRow, endRow));
    const maxRow = Math.min(grid.rowCount - 1, Math.max(startRow, endRow));
    const minCol = Math.max(0, Math.min(startCol, endCol));
    const maxCol = Math.min(grid.colCount - 1, Math.max(startCol, endCol));
    
    for (let row = minRow; row <= maxRow; row++) {
      for (let col = minCol; col <= maxCol; col++) {
        const cell = grid.cells[row][col];
        if (cell) {
          cells.add(cell);
        }
      }
    }
    
    return cells;
  }

  /**
   * Calculates the expanded bounds needed to maintain rectangular selection
   * when dealing with merged cells
   */
  calculateExpandedBounds(
    grid: TableGrid,
    startRow: number,
    endRow: number,
    startCol: number,
    endCol: number
  ): { startRow: number; endRow: number; startCol: number; endCol: number } {
    let minRow = Math.min(startRow, endRow);
    let maxRow = Math.max(startRow, endRow);
    let minCol = Math.min(startCol, endCol);
    let maxCol = Math.max(startCol, endCol);
    
    let changed = true;
    
    // Iteratively expand bounds until no more expansion is needed
    while (changed) {
      changed = false;
      
      // Check all cells in current bounds and expand if needed
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const cell = grid.cells[row][col];
          if (cell) {
            const cellEndRow = cell.row + cell.rowSpan - 1;
            const cellEndCol = cell.col + cell.colSpan - 1;
            
            // Expand bounds if this cell extends beyond current bounds
            if (cell.row < minRow) {
              minRow = cell.row;
              changed = true;
            }
            if (cellEndRow > maxRow) {
              maxRow = cellEndRow;
              changed = true;
            }
            if (cell.col < minCol) {
              minCol = cell.col;
              changed = true;
            }
            if (cellEndCol > maxCol) {
              maxCol = cellEndCol;
              changed = true;
            }
          }
        }
      }
    }
    
    return {
      startRow: minRow,
      endRow: maxRow,
      startCol: minCol,
      endCol: maxCol,
    };
  }

  /**
   * Validates that the grid structure is consistent
   */
  validateGrid(grid: TableGrid): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check that all non-null cells have valid coordinates
    for (let row = 0; row < grid.rowCount; row++) {
      for (let col = 0; col < grid.colCount; col++) {
        const cell = grid.cells[row][col];
        if (cell) {
          // Validate cell spans don't exceed grid bounds
          if (cell.row + cell.rowSpan > grid.rowCount) {
            errors.push(`Cell at (${cell.row}, ${cell.col}) rowspan extends beyond grid`);
          }
          if (cell.col + cell.colSpan > grid.colCount) {
            errors.push(`Cell at (${cell.row}, ${cell.col}) colspan extends beyond grid`);
          }
          
          // Validate that all spanned positions reference the same cell
          for (let r = cell.row; r < cell.row + cell.rowSpan; r++) {
            for (let c = cell.col; c < cell.col + cell.colSpan; c++) {
              if (r < grid.rowCount && c < grid.colCount) {
                if (grid.cells[r][c] !== cell) {
                  errors.push(`Inconsistent cell reference at (${r}, ${c})`);
                }
              }
            }
          }
        }
      }
    }
    
    // Check lookup maps consistency
    // spanMap should have entries for all non-null grid positions
    // elementMap should have one entry per unique cell
    const expectedSpanMapSize = grid.cells.flat().filter(cell => cell !== null).length;
    if (grid.spanMap.size !== expectedSpanMapSize) {
      errors.push(`SpanMap size mismatch: expected ${expectedSpanMapSize}, got ${grid.spanMap.size}`);
    }
    
    // Count unique cells
    const uniqueCells = new Set(grid.cells.flat().filter(cell => cell !== null));
    if (grid.elementMap.size !== uniqueCells.size) {
      errors.push(`ElementMap size mismatch: expected ${uniqueCells.size}, got ${grid.elementMap.size}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}