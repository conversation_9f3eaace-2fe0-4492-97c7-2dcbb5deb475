/**
 * Tests for TableSelectionManager - rectangular selection expansion algorithm
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TableSelectionManager } from '../tableSelectionManager';
import { TableGridAnalyzer } from '../tableGridAnalyzer';
import type { CellCoordinate, TableGrid, SelectionBounds } from '@/types/tableSelection';

describe('TableSelectionManager', () => {
  let selectionManager: TableSelectionManager;
  let gridAnalyzer: TableGridAnalyzer;

  beforeEach(() => {
    selectionManager = new TableSelectionManager();
    gridAnalyzer = new TableGridAnalyzer();
  });

  // Helper function to create a mock table element
  const createMockTable = (html: string): HTMLTableElement => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.querySelector('table') as HTMLTableElement;
  };

  // Helper function to create a simple grid for testing
  const createSimpleGrid = (): { grid: TableGrid; cells: CellCoordinate[] } => {
    const tableHtml = `
      <table>
        <tr>
          <td>A1</td>
          <td>B1</td>
          <td>C1</td>
        </tr>
        <tr>
          <td>A2</td>
          <td>B2</td>
          <td>C2</td>
        </tr>
        <tr>
          <td>A3</td>
          <td>B3</td>
          <td>C3</td>
        </tr>
      </table>
    `;
    
    const table = createMockTable(tableHtml);
    const grid = gridAnalyzer.analyzeTable(table);
    const cells = Array.from(grid.elementMap.values());
    
    return { grid, cells };
  };

  // Helper function to create a complex grid with merged cells
  const createComplexGrid = (): { grid: TableGrid; cells: CellCoordinate[] } => {
    const tableHtml = `
      <table>
        <tr>
          <td colspan="2">A1-B1</td>
          <td>C1</td>
        </tr>
        <tr>
          <td>A2</td>
          <td rowspan="2">B2-B3</td>
          <td>C2</td>
        </tr>
        <tr>
          <td>A3</td>
          <td>C3</td>
        </tr>
      </table>
    `;
    
    const table = createMockTable(tableHtml);
    const grid = gridAnalyzer.analyzeTable(table);
    const cells = Array.from(grid.elementMap.values());
    
    return { grid, cells };
  };

  describe('createRectangularSelection', () => {
    it('should create a simple rectangular selection', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0)!; // A1
      const endCell = cells.find(c => c.row === 1 && c.col === 1)!; // B2

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);

      expect(selection.startCell).toBe(startCell);
      expect(selection.endCell).toBe(endCell);
      expect(selection.isActive).toBe(true);
      expect(selection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      });
      expect(selection.selectedCells.size).toBe(4); // A1, B1, A2, B2
    });

    it('should handle reverse selection (end before start)', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 1 && c.col === 1)!; // B2
      const endCell = cells.find(c => c.row === 0 && c.col === 0)!; // A1

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);

      expect(selection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      });
      expect(selection.selectedCells.size).toBe(4);
    });

    it('should expand selection to include merged cells', () => {
      const { grid, cells } = createComplexGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0)!; // A1-B1 (colspan=2)
      const endCell = cells.find(c => c.row === 1 && c.col === 2)!; // C2

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);

      // Should expand to include the entire merged cell area
      // The selection includes the rowspan cell B2-B3 which extends to row 2
      expect(selection.selectionBounds.startRow).toBe(0);
      expect(selection.selectionBounds.endRow).toBe(2); // Expanded to include rowspan
      expect(selection.selectionBounds.startCol).toBe(0);
      expect(selection.selectionBounds.endCol).toBe(2);
    });

    it('should handle single cell selection', () => {
      const { grid, cells } = createSimpleGrid();
      const cell = cells.find(c => c.row === 1 && c.col === 1)!; // B2

      const selection = selectionManager.createRectangularSelection(grid, cell, cell);

      expect(selection.selectionBounds).toEqual({
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      });
      expect(selection.selectedCells.size).toBe(1);
    });
  });

  describe('expandToRectangle', () => {
    it('should not change bounds for simple rectangular selection', () => {
      const { grid } = createSimpleGrid();
      const bounds: SelectionBounds = {
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      };

      const expanded = selectionManager.expandToRectangle(bounds, grid);

      expect(expanded).toEqual(bounds);
    });

    it('should expand bounds to include merged cells', () => {
      const { grid } = createComplexGrid();
      const bounds: SelectionBounds = {
        startRow: 0,
        endRow: 0,
        startCol: 1,
        endCol: 1,
      };

      const expanded = selectionManager.expandToRectangle(bounds, grid);

      // Should expand to include the colspan=2 cell that starts at col 0
      expect(expanded.startCol).toBe(0);
      expect(expanded.endCol).toBe(1);
    });

    it('should handle rowspan expansion', () => {
      const { grid } = createComplexGrid();
      const bounds: SelectionBounds = {
        startRow: 2,
        endRow: 2,
        startCol: 1,
        endCol: 1,
      };

      const expanded = selectionManager.expandToRectangle(bounds, grid);

      // Should expand to include the rowspan=2 cell that starts at row 1
      expect(expanded.startRow).toBe(1);
      expect(expanded.endRow).toBe(2);
    });
  });

  describe('validateSelectionBounds', () => {
    it('should validate correct bounds', () => {
      const { grid } = createSimpleGrid();
      const bounds: SelectionBounds = {
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      };

      expect(selectionManager.validateSelectionBounds(bounds, grid)).toBe(true);
    });

    it('should reject bounds outside table', () => {
      const { grid } = createSimpleGrid();
      const bounds: SelectionBounds = {
        startRow: 0,
        endRow: 5, // Beyond table
        startCol: 0,
        endCol: 2,
      };

      expect(selectionManager.validateSelectionBounds(bounds, grid)).toBe(false);
    });

    it('should reject inverted bounds', () => {
      const { grid } = createSimpleGrid();
      const bounds: SelectionBounds = {
        startRow: 2,
        endRow: 0, // End before start
        startCol: 0,
        endCol: 2,
      };

      expect(selectionManager.validateSelectionBounds(bounds, grid)).toBe(false);
    });
  });

  describe('calculateSelectionArea', () => {
    it('should calculate area correctly', () => {
      const bounds: SelectionBounds = {
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 1,
      };

      const area = selectionManager.calculateSelectionArea(bounds);
      expect(area).toBe(6); // 3 rows × 2 cols
    });

    it('should handle single cell area', () => {
      const bounds: SelectionBounds = {
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      };

      const area = selectionManager.calculateSelectionArea(bounds);
      expect(area).toBe(1);
    });
  });

  describe('selectionsOverlap', () => {
    it('should detect overlapping selections', () => {
      const bounds1: SelectionBounds = {
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      };
      const bounds2: SelectionBounds = {
        startRow: 1,
        endRow: 2,
        startCol: 1,
        endCol: 2,
      };

      expect(selectionManager.selectionsOverlap(bounds1, bounds2)).toBe(true);
    });

    it('should detect non-overlapping selections', () => {
      const bounds1: SelectionBounds = {
        startRow: 0,
        endRow: 0,
        startCol: 0,
        endCol: 0,
      };
      const bounds2: SelectionBounds = {
        startRow: 2,
        endRow: 2,
        startCol: 2,
        endCol: 2,
      };

      expect(selectionManager.selectionsOverlap(bounds1, bounds2)).toBe(false);
    });
  });

  describe('mergeSelectionBounds', () => {
    it('should merge two bounds correctly', () => {
      const bounds1: SelectionBounds = {
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      };
      const bounds2: SelectionBounds = {
        startRow: 1,
        endRow: 2,
        startCol: 1,
        endCol: 2,
      };

      const merged = selectionManager.mergeSelectionBounds(bounds1, bounds2);

      expect(merged).toEqual({
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      });
    });
  });

  describe('constrainBoundsToTable', () => {
    it('should constrain bounds to table limits', () => {
      const { grid } = createSimpleGrid(); // 3x3 grid
      const bounds: SelectionBounds = {
        startRow: -1,
        endRow: 5,
        startCol: -1,
        endCol: 5,
      };

      const constrained = selectionManager.constrainBoundsToTable(bounds, grid);

      expect(constrained).toEqual({
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      });
    });
  });

  describe('extendSelectionBounds', () => {
    it('should extend selection up', () => {
      const { grid } = createSimpleGrid();
      const currentBounds: SelectionBounds = {
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      };

      const extended = selectionManager.extendSelectionBounds(
        currentBounds,
        'up',
        1,
        grid
      );

      expect(extended.startRow).toBe(0);
      expect(extended.endRow).toBe(1);
    });

    it('should extend selection down', () => {
      const { grid } = createSimpleGrid();
      const currentBounds: SelectionBounds = {
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      };

      const extended = selectionManager.extendSelectionBounds(
        currentBounds,
        'down',
        1,
        grid
      );

      expect(extended.startRow).toBe(1);
      expect(extended.endRow).toBe(2);
    });

    it('should extend selection left', () => {
      const { grid } = createSimpleGrid();
      const currentBounds: SelectionBounds = {
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      };

      const extended = selectionManager.extendSelectionBounds(
        currentBounds,
        'left',
        1,
        grid
      );

      expect(extended.startCol).toBe(0);
      expect(extended.endCol).toBe(1);
    });

    it('should extend selection right', () => {
      const { grid } = createSimpleGrid();
      const currentBounds: SelectionBounds = {
        startRow: 1,
        endRow: 1,
        startCol: 1,
        endCol: 1,
      };

      const extended = selectionManager.extendSelectionBounds(
        currentBounds,
        'right',
        1,
        grid
      );

      expect(extended.startCol).toBe(1);
      expect(extended.endCol).toBe(2);
    });

    it('should not extend beyond table bounds', () => {
      const { grid } = createSimpleGrid();
      const currentBounds: SelectionBounds = {
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      };

      const extended = selectionManager.extendSelectionBounds(
        currentBounds,
        'up',
        5,
        grid
      );

      expect(extended.startRow).toBe(0); // Should not go below 0
    });
  });

  describe('selectAll', () => {
    it('should select entire table', () => {
      const { grid } = createSimpleGrid();

      const bounds = selectionManager.selectAll(grid);

      expect(bounds).toEqual({
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      });
    });

    it('should handle empty table', () => {
      const emptyGrid: TableGrid = {
        cells: [],
        rowCount: 0,
        colCount: 0,
        spanMap: new Map(),
        elementMap: new Map(),
      };

      const bounds = selectionManager.selectAll(emptyGrid);

      expect(bounds).toEqual({
        startRow: 0,
        endRow: 0,
        startCol: 0,
        endCol: 0,
      });
    });
  });

  describe('findNextCell', () => {
    it('should find next cell in each direction', () => {
      const { grid } = createSimpleGrid();

      // From center cell (1,1)
      const up = selectionManager.findNextCell(grid, 1, 1, 'up');
      const down = selectionManager.findNextCell(grid, 1, 1, 'down');
      const left = selectionManager.findNextCell(grid, 1, 1, 'left');
      const right = selectionManager.findNextCell(grid, 1, 1, 'right');

      expect(up?.row).toBe(0);
      expect(up?.col).toBe(1);
      expect(down?.row).toBe(2);
      expect(down?.col).toBe(1);
      expect(left?.row).toBe(1);
      expect(left?.col).toBe(0);
      expect(right?.row).toBe(1);
      expect(right?.col).toBe(2);
    });

    it('should return null when moving beyond table bounds', () => {
      const { grid } = createSimpleGrid();

      const up = selectionManager.findNextCell(grid, 0, 0, 'up');
      const left = selectionManager.findNextCell(grid, 0, 0, 'left');

      expect(up).toBeNull();
      expect(left).toBeNull();
    });
  });

  describe('updateSelection', () => {
    it('should update selection with new end cell', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const initialEndCell = cells.find(c => c.row === 0 && c.col === 1) as CellCoordinate;
      const newEndCell = cells.find(c => c.row === 1 && c.col === 2) as CellCoordinate;

      const initialSelection = selectionManager.createRectangularSelection(grid, startCell, initialEndCell);
      const updatedSelection = selectionManager.updateSelection(initialSelection, newEndCell, grid);

      expect(updatedSelection.endCell).toBe(newEndCell);
      expect(updatedSelection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 2,
      });
    });

    it('should handle selection validation correctly', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 2 && c.col === 2) as CellCoordinate; // Valid end cell

      const initialSelection = selectionManager.createSingleCellSelection(startCell);
      const updatedSelection = selectionManager.updateSelection(initialSelection, endCell, grid);

      // Should create valid selection spanning the entire grid
      expect(updatedSelection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      });
      expect(selectionManager.validateSelection(updatedSelection, grid)).toBe(true);
    });
  });

  describe('validateSelection', () => {
    it('should validate correct selection', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      expect(selectionManager.validateSelection(selection, grid)).toBe(true);
    });

    it('should reject selection with invalid bounds', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      // Manually corrupt the bounds
      selection.selectionBounds.endRow = 10;

      expect(selectionManager.validateSelection(selection, grid)).toBe(false);
    });
  });

  describe('clearSelection', () => {
    it('should create an inactive empty selection', () => {
      const clearedSelection = selectionManager.clearSelection();

      expect(clearedSelection.isActive).toBe(false);
      expect(clearedSelection.selectedCells.size).toBe(0);
      expect(clearedSelection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 0,
        startCol: 0,
        endCol: 0,
      });
    });
  });

  describe('isCellSelected', () => {
    it('should return true for selected cells', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      
      expect(selectionManager.isCellSelected(startCell, selection)).toBe(true);
      expect(selectionManager.isCellSelected(endCell, selection)).toBe(true);
    });

    it('should return false for unselected cells', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 0 && c.col === 1) as CellCoordinate;
      const unselectedCell = cells.find(c => c.row === 2 && c.col === 2) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      
      expect(selectionManager.isCellSelected(unselectedCell, selection)).toBe(false);
    });

    it('should return false for inactive selection', () => {
      const { cells } = createSimpleGrid();
      const cell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const inactiveSelection = selectionManager.clearSelection();

      expect(selectionManager.isCellSelected(cell, inactiveSelection)).toBe(false);
    });
  });

  describe('isCellInSelectionBounds', () => {
    it('should return true for coordinates within bounds', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      
      expect(selectionManager.isCellInSelectionBounds(0, 0, selection)).toBe(true);
      expect(selectionManager.isCellInSelectionBounds(1, 1, selection)).toBe(true);
      expect(selectionManager.isCellInSelectionBounds(0, 1, selection)).toBe(true);
    });

    it('should return false for coordinates outside bounds', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      
      expect(selectionManager.isCellInSelectionBounds(2, 2, selection)).toBe(false);
      expect(selectionManager.isCellInSelectionBounds(-1, 0, selection)).toBe(false);
    });
  });

  describe('getSelectedElements', () => {
    it('should return unique DOM elements', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      const elements = selectionManager.getSelectedElements(selection);

      expect(elements.size).toBe(4); // 4 unique cells selected
      expect(elements.has(startCell.element)).toBe(true);
      expect(elements.has(endCell.element)).toBe(true);
    });

    it('should handle merged cells correctly', () => {
      const { grid, cells } = createComplexGrid();
      const mergedCell = cells.find(c => c.colSpan === 2) as CellCoordinate; // A1-B1 (colspan=2)
      
      const selection = selectionManager.createSingleCellSelection(mergedCell);
      const elements = selectionManager.getSelectedElements(selection);

      expect(elements.size).toBe(1); // Only one DOM element for merged cell
      expect(elements.has(mergedCell.element)).toBe(true);
    });
  });

  describe('getSelectionStats', () => {
    it('should return correct statistics', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
      const stats = selectionManager.getSelectionStats(selection);

      expect(stats.cellCount).toBe(4);
      expect(stats.uniqueElementCount).toBe(4);
      expect(stats.area).toBe(4);
      expect(stats.bounds).toEqual({
        startRow: 0,
        endRow: 1,
        startCol: 0,
        endCol: 1,
      });
    });
  });

  describe('createSingleCellSelection', () => {
    it('should create selection for single cell', () => {
      const { cells } = createSimpleGrid();
      const cell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const selection = selectionManager.createSingleCellSelection(cell);

      expect(selection.startCell).toBe(cell);
      expect(selection.endCell).toBe(cell);
      expect(selection.selectedCells.has(cell)).toBe(true);
      expect(selection.selectedCells.size).toBe(1);
      expect(selection.isActive).toBe(true);
    });
  });

  describe('extendSelectionToCell', () => {
    it('should extend existing selection to new cell', () => {
      const { grid, cells } = createSimpleGrid();
      const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const targetCell = cells.find(c => c.row === 2 && c.col === 2) as CellCoordinate;

      const initialSelection = selectionManager.createSingleCellSelection(startCell);
      const extendedSelection = selectionManager.extendSelectionToCell(initialSelection, targetCell, grid);

      expect(extendedSelection.selectionBounds).toEqual({
        startRow: 0,
        endRow: 2,
        startCol: 0,
        endCol: 2,
      });
      expect(extendedSelection.selectedCells.size).toBe(9); // 3x3 selection
    });

    it('should create new selection if current is inactive', () => {
      const { grid, cells } = createSimpleGrid();
      const targetCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

      const inactiveSelection = selectionManager.clearSelection();
      const newSelection = selectionManager.extendSelectionToCell(inactiveSelection, targetCell, grid);

      expect(newSelection.startCell).toBe(targetCell);
      expect(newSelection.endCell).toBe(targetCell);
      expect(newSelection.selectedCells.size).toBe(1);
    });
  });

  describe('canExtendSelection', () => {
    it('should return true when extension is possible', () => {
      const { grid, cells } = createSimpleGrid();
      const cell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate; // Center cell

      const selection = selectionManager.createSingleCellSelection(cell);

      expect(selectionManager.canExtendSelection(selection, 'up', grid)).toBe(true);
      expect(selectionManager.canExtendSelection(selection, 'down', grid)).toBe(true);
      expect(selectionManager.canExtendSelection(selection, 'left', grid)).toBe(true);
      expect(selectionManager.canExtendSelection(selection, 'right', grid)).toBe(true);
    });

    it('should return false when extension hits boundaries', () => {
      const { grid, cells } = createSimpleGrid();
      const topLeftCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
      const bottomRightCell = cells.find(c => c.row === 2 && c.col === 2) as CellCoordinate;

      const topLeftSelection = selectionManager.createSingleCellSelection(topLeftCell);
      const bottomRightSelection = selectionManager.createSingleCellSelection(bottomRightCell);

      expect(selectionManager.canExtendSelection(topLeftSelection, 'up', grid)).toBe(false);
      expect(selectionManager.canExtendSelection(topLeftSelection, 'left', grid)).toBe(false);
      expect(selectionManager.canExtendSelection(bottomRightSelection, 'down', grid)).toBe(false);
      expect(selectionManager.canExtendSelection(bottomRightSelection, 'right', grid)).toBe(false);
    });

    it('should return false for inactive selection', () => {
      const { grid } = createSimpleGrid();
      const inactiveSelection = selectionManager.clearSelection();

      expect(selectionManager.canExtendSelection(inactiveSelection, 'up', grid)).toBe(false);
    });
  });

  describe('Coordinate Mapping Utilities', () => {
    describe('getCellAtGridPosition', () => {
      it('should return cell at valid grid position', () => {
        const { grid } = createSimpleGrid();
        
        const cell = selectionManager.getCellAtGridPosition(grid, 1, 1);
        
        expect(cell).not.toBeNull();
        expect(cell?.row).toBe(1);
        expect(cell?.col).toBe(1);
      });

      it('should return null for invalid grid position', () => {
        const { grid } = createSimpleGrid();
        
        const cell = selectionManager.getCellAtGridPosition(grid, 10, 10);
        
        expect(cell).toBeNull();
      });
    });

    describe('getCellCoordinateFromElement', () => {
      it('should return coordinate for valid element', () => {
        const { grid, cells } = createSimpleGrid();
        const testCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;
        
        const coordinate = selectionManager.getCellCoordinateFromElement(grid, testCell.element);
        
        expect(coordinate).toBe(testCell);
      });

      it('should return null for element not in grid', () => {
        const { grid } = createSimpleGrid();
        const foreignElement = document.createElement('td') as HTMLTableCellElement;
        
        const coordinate = selectionManager.getCellCoordinateFromElement(grid, foreignElement);
        
        expect(coordinate).toBeNull();
      });
    });

    describe('getCellLogicalBounds', () => {
      it('should return correct bounds for simple cell', () => {
        const { cells } = createSimpleGrid();
        const cell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;
        
        const bounds = selectionManager.getCellLogicalBounds(cell);
        
        expect(bounds).toEqual({
          startRow: 1,
          endRow: 1,
          startCol: 1,
          endCol: 1,
        });
      });

      it('should return correct bounds for merged cell', () => {
        const { cells } = createComplexGrid();
        const mergedCell = cells.find(c => c.colSpan === 2) as CellCoordinate; // A1-B1 (colspan=2)
        
        const bounds = selectionManager.getCellLogicalBounds(mergedCell);
        
        expect(bounds).toEqual({
          startRow: mergedCell.row,
          endRow: mergedCell.row,
          startCol: mergedCell.col,
          endCol: mergedCell.col + 1, // colSpan - 1
        });
      });

      it('should return correct bounds for rowspan cell', () => {
        const { cells } = createComplexGrid();
        const rowspanCell = cells.find(c => c.rowSpan === 2) as CellCoordinate; // B2-B3 (rowspan=2)
        
        const bounds = selectionManager.getCellLogicalBounds(rowspanCell);
        
        expect(bounds).toEqual({
          startRow: rowspanCell.row,
          endRow: rowspanCell.row + 1, // rowSpan - 1
          startCol: rowspanCell.col,
          endCol: rowspanCell.col,
        });
      });
    });

    describe('getSelectionDOMBounds', () => {
      it('should return null for inactive selection', () => {
        const inactiveSelection = selectionManager.clearSelection();
        
        const bounds = selectionManager.getSelectionDOMBounds(inactiveSelection);
        
        expect(bounds).toBeNull();
      });

      it('should return bounds for active selection', () => {
        const { grid, cells } = createSimpleGrid();
        const startCell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
        const endCell = cells.find(c => c.row === 1 && c.col === 1) as CellCoordinate;

        // Mock getBoundingClientRect for test cells
        const mockRect = { left: 0, top: 0, right: 100, bottom: 50, width: 100, height: 50 };
        startCell.element.getBoundingClientRect = vi.fn().mockReturnValue(mockRect);
        endCell.element.getBoundingClientRect = vi.fn().mockReturnValue(mockRect);
        
        // Mock other cells in selection
        for (const cell of cells) {
          if (cell !== startCell && cell !== endCell) {
            cell.element.getBoundingClientRect = vi.fn().mockReturnValue(mockRect);
          }
        }

        const selection = selectionManager.createRectangularSelection(grid, startCell, endCell);
        const bounds = selectionManager.getSelectionDOMBounds(selection);
        
        expect(bounds).not.toBeNull();
        expect(bounds?.width).toBeGreaterThan(0);
        expect(bounds?.height).toBeGreaterThan(0);
      });
    });

    describe('isPointInCell', () => {
      it('should return true for point inside cell', () => {
        const { cells } = createSimpleGrid();
        const cell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
        
        // Mock getBoundingClientRect
        cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
          left: 0,
          top: 0,
          right: 100,
          bottom: 50,
          width: 100,
          height: 50,
        });

        const isInside = selectionManager.isPointInCell(cell, 50, 25);
        
        expect(isInside).toBe(true);
      });

      it('should return false for point outside cell', () => {
        const { cells } = createSimpleGrid();
        const cell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
        
        // Mock getBoundingClientRect
        cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
          left: 0,
          top: 0,
          right: 100,
          bottom: 50,
          width: 100,
          height: 50,
        });

        const isInside = selectionManager.isPointInCell(cell, 150, 75);
        
        expect(isInside).toBe(false);
      });
    });

    describe('getCellsInDOMRect', () => {
      it('should find cells that intersect with DOM rectangle', () => {
        const { grid, cells } = createSimpleGrid();
        
        // Mock getBoundingClientRect for cells
        cells.forEach((cell, index) => {
          cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
            left: (cell.col * 100),
            top: (cell.row * 50),
            right: (cell.col * 100) + 100,
            bottom: (cell.row * 50) + 50,
            width: 100,
            height: 50,
          });
        });

        const searchRect = new DOMRect(50, 25, 100, 50); // Should intersect with multiple cells
        const intersectingCells = selectionManager.getCellsInDOMRect(grid, searchRect);
        
        expect(intersectingCells.size).toBeGreaterThan(0);
      });
    });

    describe('getClosestCellToDOMPoint', () => {
      it('should find the closest cell to a point', () => {
        const { grid, cells } = createSimpleGrid();
        
        // Mock getBoundingClientRect for cells
        cells.forEach((cell) => {
          cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
            left: (cell.col * 100),
            top: (cell.row * 50),
            right: (cell.col * 100) + 100,
            bottom: (cell.row * 50) + 50,
            width: 100,
            height: 50,
          });
        });

        const closestCell = selectionManager.getClosestCellToDOMPoint(grid, 50, 25);
        
        expect(closestCell).not.toBeNull();
        expect(closestCell?.row).toBe(0);
        expect(closestCell?.col).toBe(0);
      });

      it('should return null for empty grid', () => {
        const emptyGrid: TableGrid = {
          cells: [],
          rowCount: 0,
          colCount: 0,
          spanMap: new Map(),
          elementMap: new Map(),
        };

        const closestCell = selectionManager.getClosestCellToDOMPoint(emptyGrid, 50, 25);
        
        expect(closestCell).toBeNull();
      });
    });

    describe('getPointOffsetInCell', () => {
      it('should return normalized offset for point inside cell', () => {
        const { cells } = createSimpleGrid();
        const cell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
        
        // Mock getBoundingClientRect
        cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
          left: 0,
          top: 0,
          right: 100,
          bottom: 50,
          width: 100,
          height: 50,
        });

        const offset = selectionManager.getPointOffsetInCell(cell, 25, 12.5);
        
        expect(offset).not.toBeNull();
        expect(offset?.offsetX).toBe(0.25); // 25/100
        expect(offset?.offsetY).toBe(0.25); // 12.5/50
      });

      it('should return null for point outside cell', () => {
        const { cells } = createSimpleGrid();
        const cell = cells.find(c => c.row === 0 && c.col === 0) as CellCoordinate;
        
        // Mock getBoundingClientRect
        cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
          left: 0,
          top: 0,
          right: 100,
          bottom: 50,
          width: 100,
          height: 50,
        });

        const offset = selectionManager.getPointOffsetInCell(cell, 150, 75);
        
        expect(offset).toBeNull();
      });
    });

    describe('snapToNearestCellBoundary', () => {
      it('should snap to cell boundary when within threshold', () => {
        const { grid, cells } = createSimpleGrid();
        
        // Mock getBoundingClientRect for cells
        cells.forEach((cell) => {
          cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
            left: (cell.col * 100),
            top: (cell.row * 50),
            right: (cell.col * 100) + 100,
            bottom: (cell.row * 50) + 50,
            width: 100,
            height: 50,
          });
        });

        const snapped = selectionManager.snapToNearestCellBoundary(grid, 95, 45, 10);
        
        expect(snapped.x).toBe(100); // Snapped to right edge of first cell
        expect(snapped.y).toBe(50);  // Snapped to bottom edge of first cell
        expect(snapped.cell).not.toBeNull();
      });

      it('should not snap when outside threshold', () => {
        const { grid, cells } = createSimpleGrid();
        
        // Mock getBoundingClientRect for cells
        cells.forEach((cell) => {
          cell.element.getBoundingClientRect = vi.fn().mockReturnValue({
            left: (cell.col * 100),
            top: (cell.row * 50),
            right: (cell.col * 100) + 100,
            bottom: (cell.row * 50) + 50,
            width: 100,
            height: 50,
          });
        });

        const snapped = selectionManager.snapToNearestCellBoundary(grid, 80, 30, 5);
        
        expect(snapped.x).toBe(80); // Not snapped
        expect(snapped.y).toBe(30); // Not snapped
        expect(snapped.cell).not.toBeNull();
      });
    });
  });
});