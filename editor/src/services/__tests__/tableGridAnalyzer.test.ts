/**
 * Unit tests for TableGridAnalyzer service
 */

import { describe, it, expect, beforeEach } from "vitest";
import { TableGridAnalyzer } from "../tableGridAnalyzer";
import type { TableGrid } from "@/types/tableSelection";

describe("TableGridAnalyzer", () => {
	let analyzer: TableGridAnalyzer;

	beforeEach(() => {
		analyzer = new TableGridAnalyzer();
	});

	// Helper function to create a table element from HTML string
	const createTableFromHTML = (html: string): HTMLTableElement => {
		const container = document.createElement("div");
		container.innerHTML = html;
		const table = container.querySelector("table");
		if (!table) {
			throw new Error("No table found in HTML");
		}
		return table;
	};

	describe("Basic table analysis", () => {
		it("should analyze a simple 2x2 table correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(2);
			expect(grid.cells).toHaveLength(2);
			expect(grid.cells[0]).toHaveLength(2);
			expect(grid.cells[1]).toHaveLength(2);

			// Check that all positions are filled
			expect(grid.cells[0][0]).not.toBeNull();
			expect(grid.cells[0][1]).not.toBeNull();
			expect(grid.cells[1][0]).not.toBeNull();
			expect(grid.cells[1][1]).not.toBeNull();

			// Check cell coordinates
			expect(grid.cells[0][0]?.row).toBe(0);
			expect(grid.cells[0][0]?.col).toBe(0);
			expect(grid.cells[0][1]?.row).toBe(0);
			expect(grid.cells[0][1]?.col).toBe(1);
		});

		it("should handle empty table gracefully", () => {
			const html = "<table><tbody></tbody></table>";
			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(0);
			expect(grid.colCount).toBe(0);
			expect(grid.cells).toHaveLength(0);
		});

		it("should handle table without tbody", () => {
			const html = `
        <table>
          <tr>
            <td>A1</td>
            <td>B1</td>
          </tr>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(1);
			expect(grid.colCount).toBe(2);
		});
	});

	describe("Colspan handling", () => {
		it("should handle simple colspan correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2">Merged</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(2);

			// First row should have the same cell in both positions
			expect(grid.cells[0][0]).toBe(grid.cells[0][1]);
			expect(grid.cells[0][0]?.colSpan).toBe(2);

			// Second row should have different cells
			expect(grid.cells[1][0]).not.toBe(grid.cells[1][1]);
			expect(grid.cells[1][0]?.colSpan).toBe(1);
			expect(grid.cells[1][1]?.colSpan).toBe(1);
		});

		it("should handle complex colspan pattern from example table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td>D1</td>
              <td>E1</td>
            </tr>
            <tr>
              <td colspan="3">Merged ABC</td>
              <td>D2</td>
              <td>E2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td colspan="2">Merged CD</td>
              <td>E3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(5);

			// Row 2: colspan="3" should occupy positions 0, 1, 2
			expect(grid.cells[1][0]).toBe(grid.cells[1][1]);
			expect(grid.cells[1][1]).toBe(grid.cells[1][2]);
			expect(grid.cells[1][0]?.colSpan).toBe(3);

			// Row 3: colspan="2" should occupy positions 2, 3
			expect(grid.cells[2][2]).toBe(grid.cells[2][3]);
			expect(grid.cells[2][2]?.colSpan).toBe(2);
		});
	});

	describe("Rowspan handling", () => {
		it("should handle simple rowspan correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td rowspan="2">Merged</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(2);

			// First column should have the same cell in both rows
			expect(grid.cells[0][0]).toBe(grid.cells[1][0]);
			expect(grid.cells[0][0]?.rowSpan).toBe(2);

			// Second column should have different cells
			expect(grid.cells[0][1]).not.toBe(grid.cells[1][1]);
		});

		it("should handle rowspan from example table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td rowspan="2">Merged D</td>
              <td>E1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>C2</td>
              <td>E2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(5);

			// Column 3 should have the same cell in both rows
			expect(grid.cells[0][3]).toBe(grid.cells[1][3]);
			expect(grid.cells[0][3]?.rowSpan).toBe(2);

			// Other columns should have different cells
			expect(grid.cells[0][0]).not.toBe(grid.cells[1][0]);
			expect(grid.cells[0][1]).not.toBe(grid.cells[1][1]);
			expect(grid.cells[0][2]).not.toBe(grid.cells[1][2]);
			expect(grid.cells[0][4]).not.toBe(grid.cells[1][4]);
		});
	});

	describe("Complex span combinations", () => {
		it("should handle both colspan and rowspan in same table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td colspan="2" rowspan="2">Merged BC</td>
              <td>D1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>D2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(4);

			// The merged cell should occupy positions (0,1), (0,2), (1,1), (1,2)
			const mergedCell = grid.cells[0][1];
			expect(mergedCell).toBe(grid.cells[0][2]);
			expect(mergedCell).toBe(grid.cells[1][1]);
			expect(mergedCell).toBe(grid.cells[1][2]);
			expect(mergedCell?.colSpan).toBe(2);
			expect(mergedCell?.rowSpan).toBe(2);
		});
	});

	describe("Lookup maps", () => {
		it("should build spanMap correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.spanMap.size).toBe(4);
			expect(grid.spanMap.has("0,0")).toBe(true);
			expect(grid.spanMap.has("0,1")).toBe(true);
			expect(grid.spanMap.has("1,0")).toBe(true);
			expect(grid.spanMap.has("1,1")).toBe(true);

			const cell00 = grid.spanMap.get("0,0");
			expect(cell00?.row).toBe(0);
			expect(cell00?.col).toBe(0);
		});

		it("should build elementMap correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.elementMap.size).toBe(2);

			const cells = table.querySelectorAll("td");
			expect(grid.elementMap.has(cells[0] as HTMLTableCellElement)).toBe(true);
			expect(grid.elementMap.has(cells[1] as HTMLTableCellElement)).toBe(true);
		});
	});

	describe("Utility methods", () => {
		let grid: TableGrid;

		beforeEach(() => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td colspan="2">B1-C1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>C2</td>
            </tr>
          </tbody>
        </table>
      `;
			const table = createTableFromHTML(html);
			grid = analyzer.analyzeTable(table);
		});

		describe("getCellAtPosition", () => {
			it("should return correct cell for valid positions", () => {
				const cell = analyzer.getCellAtPosition(grid, 0, 0);
				expect(cell).not.toBeNull();
				expect(cell?.row).toBe(0);
				expect(cell?.col).toBe(0);
			});

			it("should return null for invalid positions", () => {
				expect(analyzer.getCellAtPosition(grid, -1, 0)).toBeNull();
				expect(analyzer.getCellAtPosition(grid, 0, -1)).toBeNull();
				expect(analyzer.getCellAtPosition(grid, 10, 0)).toBeNull();
				expect(analyzer.getCellAtPosition(grid, 0, 10)).toBeNull();
			});
		});

		describe("getCellsInBounds", () => {
			it("should return all cells in rectangular bounds", () => {
				const cells = analyzer.getCellsInBounds(grid, 0, 1, 0, 2);
				expect(cells.size).toBe(5); // 5 unique cells in the 2x3 bounds

				// Convert to array to check contents
				const cellArray = Array.from(cells);
				expect(cellArray).toHaveLength(5);
			});

			it("should handle bounds that extend beyond grid", () => {
				const cells = analyzer.getCellsInBounds(grid, 0, 10, 0, 10);
				expect(cells.size).toBe(5); // Should only include cells that exist
			});
		});

		describe("calculateExpandedBounds", () => {
			it("should expand bounds to include full merged cells", () => {
				// Select just position (0,1) which is part of a colspan=2 cell
				const expanded = analyzer.calculateExpandedBounds(grid, 0, 0, 1, 1);

				// Should expand to include the full merged cell (0,1) and (0,2)
				expect(expanded.startRow).toBe(0);
				expect(expanded.endRow).toBe(0);
				expect(expanded.startCol).toBe(1);
				expect(expanded.endCol).toBe(2);
			});

			it("should not expand if bounds already include full cells", () => {
				const expanded = analyzer.calculateExpandedBounds(grid, 0, 1, 0, 2);

				expect(expanded.startRow).toBe(0);
				expect(expanded.endRow).toBe(1);
				expect(expanded.startCol).toBe(0);
				expect(expanded.endCol).toBe(2);
			});
		});
	});

	describe("Error handling", () => {
		it("should throw error for null table element", () => {
			expect(() => analyzer.analyzeTable(null as any)).toThrow(
				"Table element is required",
			);
		});

		it("should handle malformed colspan values gracefully", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="invalid">A1</td>
              <td colspan="-1">B1</td>
              <td colspan="0">C1</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// All invalid colspans should default to 1
			expect(grid.colCount).toBe(3);
			expect(grid.cells[0][0]?.colSpan).toBe(1);
			expect(grid.cells[0][1]?.colSpan).toBe(1);
			expect(grid.cells[0][2]?.colSpan).toBe(1);
		});

		it("should handle malformed rowspan values gracefully", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td rowspan="invalid">A1</td>
              <td rowspan="-1">B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// All invalid rowspans should default to 1
			expect(grid.cells[0][0]?.rowSpan).toBe(1);
			expect(grid.cells[0][1]?.rowSpan).toBe(1);
		});

		it("should throw error for tables exceeding size limit", () => {
			const smallAnalyzer = new TableGridAnalyzer({ maxSize: 4 });

			const html = `
        <table>
          <tbody>
            <tr><td>1</td><td>2</td><td>3</td></tr>
            <tr><td>4</td><td>5</td><td>6</td></tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			expect(() => smallAnalyzer.analyzeTable(table)).toThrow(
				"Table too large",
			);
		});
	});

	describe("Grid validation", () => {
		it("should validate correct grid structure", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);
			const validation = analyzer.validateGrid(grid);

			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);
		});
	});

	describe("Complex example table from table.example.html", () => {
		it("should correctly analyze the provided complex table structure", () => {
			// This is a simplified version of the complex table from table.example.html
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td>D1</td>
              <td>E1</td>
            </tr>
            <tr>
              <td colspan="3">ABC2</td>
              <td>D2</td>
              <td>E2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td colspan="2">CD3</td>
              <td>E3</td>
            </tr>
            <tr>
              <td>A4</td>
              <td>B4</td>
              <td>C4</td>
              <td rowspan="2">D4-D5</td>
              <td>E4</td>
            </tr>
            <tr>
              <td>A5</td>
              <td>B5</td>
              <td>C5</td>
              <td>E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Verify basic dimensions
			expect(grid.rowCount).toBe(5);
			expect(grid.colCount).toBe(5);

			// Verify row 2 colspan="3" (positions 0,1,2 should be same cell)
			expect(grid.cells[1][0]).toBe(grid.cells[1][1]);
			expect(grid.cells[1][1]).toBe(grid.cells[1][2]);
			expect(grid.cells[1][0]?.colSpan).toBe(3);

			// Verify row 3 colspan="2" (positions 2,3 should be same cell)
			expect(grid.cells[2][2]).toBe(grid.cells[2][3]);
			expect(grid.cells[2][2]?.colSpan).toBe(2);

			// Verify row 4-5 rowspan="2" (positions [3][3] and [4][3] should be same cell)
			expect(grid.cells[3][3]).toBe(grid.cells[4][3]);
			expect(grid.cells[3][3]?.rowSpan).toBe(2);

			// Verify grid validation passes
			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);

			// Test expanded bounds calculation with the complex structure
			// Select just the merged cell in row 2 - should expand to include full colspan
			const expandedRow2 = analyzer.calculateExpandedBounds(grid, 1, 1, 1, 1);
			expect(expandedRow2.startCol).toBe(0);
			expect(expandedRow2.endCol).toBe(2);

			// Select just the merged cell in row 4 - should expand to include full rowspan
			const expandedRow4 = analyzer.calculateExpandedBounds(grid, 3, 3, 3, 3);
			expect(expandedRow4.startRow).toBe(3);
			expect(expandedRow4.endRow).toBe(4);
		});

		it("should correctly analyze the actual complex table from table.example.html", () => {
			// This represents the actual structure from table.example.html
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="1" rowspan="1">Cell 1</td>
              <td colspan="1" rowspan="1">Cell 2</td>
              <td colspan="1" rowspan="1">Cell 3</td>
              <td colspan="1" rowspan="1">Cell 4</td>
              <td colspan="1" rowspan="1">Cell 5</td>
            </tr>
            <tr>
              <td colspan="3" rowspan="1">Merged ABC</td>
              <td colspan="1" rowspan="1">Cell D2</td>
              <td colspan="1" rowspan="1">Cell E2</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A3</td>
              <td colspan="1" rowspan="1">Cell B3</td>
              <td colspan="2" rowspan="1">Merged CD</td>
              <td colspan="1" rowspan="1">Cell E3</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A4</td>
              <td colspan="1" rowspan="1">Cell B4</td>
              <td colspan="1" rowspan="1">Cell C4</td>
              <td colspan="1" rowspan="2">Merged D4-D5</td>
              <td colspan="1" rowspan="1">Cell E4</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A5</td>
              <td colspan="1" rowspan="1">Cell B5</td>
              <td colspan="1" rowspan="1">Cell C5</td>
              <td colspan="1" rowspan="1">Cell E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Verify dimensions match the complex example
			expect(grid.rowCount).toBe(5);
			expect(grid.colCount).toBe(5);

			// Verify all cells are properly mapped
			expect(grid.spanMap.size).toBe(25); // 5x5 grid positions
			expect(grid.elementMap.size).toBe(21); // 21 unique cells

			// Test specific merged cell behaviors
			const row2MergedCell = grid.cells[1][0];
			expect(row2MergedCell).toBe(grid.cells[1][1]);
			expect(row2MergedCell).toBe(grid.cells[1][2]);
			expect(row2MergedCell?.colSpan).toBe(3);

			const row3MergedCell = grid.cells[2][2];
			expect(row3MergedCell).toBe(grid.cells[2][3]);
			expect(row3MergedCell?.colSpan).toBe(2);

			const row4MergedCell = grid.cells[3][3];
			expect(row4MergedCell).toBe(grid.cells[4][3]);
			expect(row4MergedCell?.rowSpan).toBe(2);

			// Verify grid validation
			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should handle selection bounds that cross multiple merged cells", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2" rowspan="2">A1-B1-A2-B2</td>
              <td>C1</td>
            </tr>
            <tr>
              <td>C2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(3);

			// The merged cell should occupy positions (0,0), (0,1), (1,0), (1,1)
			const mergedCell = grid.cells[0][0];
			expect(mergedCell).toBe(grid.cells[0][1]);
			expect(mergedCell).toBe(grid.cells[1][0]);
			expect(mergedCell).toBe(grid.cells[1][1]);
			expect(mergedCell?.colSpan).toBe(2);
			expect(mergedCell?.rowSpan).toBe(2);

			// Test selection that partially overlaps the merged cell
			const expanded = analyzer.calculateExpandedBounds(grid, 0, 2, 1, 2);
			// Should expand to include the full merged cell
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(2);
		});

		it("should handle complex overlapping spans correctly", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="3" rowspan="2">Large merged cell</td>
              <td>D1</td>
              <td rowspan="3">E1-E2-E3</td>
            </tr>
            <tr>
              <td>D2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td colspan="2">B3-C3</td>
              <td>D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(5);

			// Verify the large merged cell (3x2)
			const largeMerged = grid.cells[0][0];
			expect(largeMerged?.colSpan).toBe(3);
			expect(largeMerged?.rowSpan).toBe(2);
			
			// Check all positions occupied by large merged cell
			for (let r = 0; r < 2; r++) {
				for (let c = 0; c < 3; c++) {
					expect(grid.cells[r][c]).toBe(largeMerged);
				}
			}

			// Verify the vertical merged cell (1x3)
			const verticalMerged = grid.cells[0][4];
			expect(verticalMerged?.rowSpan).toBe(3);
			expect(verticalMerged).toBe(grid.cells[1][4]);
			expect(verticalMerged).toBe(grid.cells[2][4]);

			// Verify the horizontal merged cell in row 3
			const horizontalMerged = grid.cells[2][1];
			expect(horizontalMerged?.colSpan).toBe(2);
			expect(horizontalMerged).toBe(grid.cells[2][2]);

			// Test bounds expansion with overlapping spans
			const expanded = analyzer.calculateExpandedBounds(grid, 0, 2, 0, 4);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(4);
		});
	});

	describe("Advanced edge cases and malformed tables", () => {
		it("should handle tables with missing cells gracefully", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
            </tr>
            <tr>
              <td>A2</td>
              <!-- Missing B2 cell -->
              <td>C2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			// The analyzer maps cells sequentially, so missing cell doesn't create null
			// Instead, C2 gets mapped to position [1][1]
			expect(grid.colCount).toBe(3);

			// Row 2 should have A2 at [1][0] and C2 at [1][1]
			expect(grid.cells[1][0]).not.toBeNull();
			expect(grid.cells[1][1]).not.toBeNull();
			expect(grid.cells[1][1]?.element.textContent?.trim()).toBe("C2");
			
			// Position [1][2] should be null since there are only 2 cells in row 2
			expect(grid.cells[1][2]).toBeNull();
		});

		it("should handle extremely large span values", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="1000">Huge span</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Should be capped at reasonable maximum (100)
			expect(grid.cells[0][0]?.colSpan).toBe(100);
			expect(grid.colCount).toBe(100);
		});

		it("should handle zero and negative span values", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="0">Zero span</td>
              <td rowspan="-1">Negative span</td>
              <td>Normal</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// All invalid spans should default to 1
			expect(grid.cells[0][0]?.colSpan).toBe(1);
			expect(grid.cells[0][1]?.rowSpan).toBe(1);
			expect(grid.colCount).toBe(3);
		});

		it("should handle tables with thead and tfoot sections", () => {
			const html = `
        <table>
          <thead>
            <tr>
              <th>Header 1</th>
              <th colspan="2">Header 2-3</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Body 1</td>
              <td>Body 2</td>
              <td>Body 3</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3">Footer</td>
            </tr>
          </tfoot>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3); // thead + tbody + tfoot
			expect(grid.colCount).toBe(3);

			// Header row should have merged cell
			expect(grid.cells[0][1]).toBe(grid.cells[0][2]);
			expect(grid.cells[0][1]?.colSpan).toBe(2);

			// Footer row should have merged cell
			expect(grid.cells[2][0]).toBe(grid.cells[2][1]);
			expect(grid.cells[2][0]).toBe(grid.cells[2][2]);
			expect(grid.cells[2][0]?.colSpan).toBe(3);
		});

		it("should handle nested table structures", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>
                B1
                <table>
                  <tr><td>Nested</td></tr>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// The analyzer uses querySelectorAll('tr') which might pick up nested table rows
			// Let's test what it actually returns
			expect(grid.rowCount).toBeGreaterThanOrEqual(1);
			expect(grid.colCount).toBeGreaterThanOrEqual(2);

			// Should have at least the outer table cells
			expect(grid.cells[0][0]).not.toBeNull();
			expect(grid.cells[0][1]).not.toBeNull();
			
			// Verify the outer table cells have correct content
			expect(grid.cells[0][0]?.element.textContent?.trim()).toBe("A1");
		});

		it("should validate grid consistency with complex spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2" rowspan="2">A1-B1-A2-B2</td>
              <td>C1</td>
              <td rowspan="3">D1-D2-D3</td>
            </tr>
            <tr>
              <td>C2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);

			// Verify specific cell references are consistent
			const cell00 = grid.cells[0][0];
			expect(cell00).toBe(grid.cells[0][1]);
			expect(cell00).toBe(grid.cells[1][0]);
			expect(cell00).toBe(grid.cells[1][1]);

			const cell03 = grid.cells[0][3];
			expect(cell03).toBe(grid.cells[1][3]);
			expect(cell03).toBe(grid.cells[2][3]);
		});

		it("should handle performance with large tables", () => {
			// Create a 50x50 table to test performance
			let html = '<table><tbody>';
			for (let row = 0; row < 50; row++) {
				html += '<tr>';
				for (let col = 0; col < 50; col++) {
					html += `<td>Cell ${row}-${col}</td>`;
				}
				html += '</tr>';
			}
			html += '</tbody></table>';

			const table = createTableFromHTML(html);
			const startTime = performance.now();
			const grid = analyzer.analyzeTable(table);
			const endTime = performance.now();

			expect(grid.rowCount).toBe(50);
			expect(grid.colCount).toBe(50);
			expect(grid.spanMap.size).toBe(2500);
			expect(grid.elementMap.size).toBe(2500);

			// Should complete within reasonable time (less than 100ms)
			expect(endTime - startTime).toBeLessThan(100);

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should handle coordinate mapping accuracy with complex spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td colspan="3">B1-C1-D1</td>
              <td>E1</td>
            </tr>
            <tr>
              <td rowspan="2">A2-A3</td>
              <td>B2</td>
              <td>C2</td>
              <td>D2</td>
              <td rowspan="2">E2-E3</td>
            </tr>
            <tr>
              <td>B3</td>
              <td colspan="2">C3-D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Test coordinate mapping for each cell
			const cells = table.querySelectorAll('td');
			
			// First cell (A1) should be at (0,0)
			const a1Coord = analyzer.getCellCoordinate(grid, cells[0] as HTMLTableCellElement);
			expect(a1Coord?.row).toBe(0);
			expect(a1Coord?.col).toBe(0);
			expect(a1Coord?.colSpan).toBe(1);
			expect(a1Coord?.rowSpan).toBe(1);

			// Second cell (B1-C1-D1) should be at (0,1) with colspan=3
			const b1Coord = analyzer.getCellCoordinate(grid, cells[1] as HTMLTableCellElement);
			expect(b1Coord?.row).toBe(0);
			expect(b1Coord?.col).toBe(1);
			expect(b1Coord?.colSpan).toBe(3);
			expect(b1Coord?.rowSpan).toBe(1);

			// Third cell (E1) should be at (0,4)
			const e1Coord = analyzer.getCellCoordinate(grid, cells[2] as HTMLTableCellElement);
			expect(e1Coord?.row).toBe(0);
			expect(e1Coord?.col).toBe(4);

			// Fourth cell (A2-A3) should be at (1,0) with rowspan=2
			const a2Coord = analyzer.getCellCoordinate(grid, cells[3] as HTMLTableCellElement);
			expect(a2Coord?.row).toBe(1);
			expect(a2Coord?.col).toBe(0);
			expect(a2Coord?.rowSpan).toBe(2);

			// Test getCellAtPosition for various positions
			expect(analyzer.getCellAtPosition(grid, 0, 0)?.element.textContent?.trim()).toBe("A1");
			expect(analyzer.getCellAtPosition(grid, 0, 1)?.element.textContent?.trim()).toBe("B1-C1-D1");
			expect(analyzer.getCellAtPosition(grid, 1, 0)?.element.textContent?.trim()).toBe("A2-A3");
		});
	});

	describe("Real-world complex table from table.example.html", () => {
		it("should correctly analyze the actual table.example.html structure", () => {
			// This represents the actual structure from table.example.html
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="1" rowspan="1">Cell 1</td>
              <td colspan="1" rowspan="1">Cell 2</td>
              <td colspan="1" rowspan="1">Cell 3</td>
              <td colspan="1" rowspan="1">Cell 4</td>
              <td colspan="1" rowspan="1">Cell 5</td>
            </tr>
            <tr>
              <td colspan="3" rowspan="1">Merged ABC</td>
              <td colspan="1" rowspan="1">Cell D2</td>
              <td colspan="1" rowspan="1">Cell E2</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A3</td>
              <td colspan="1" rowspan="1">Cell B3</td>
              <td colspan="2" rowspan="1">Merged CD</td>
              <td colspan="1" rowspan="1">Cell E3</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A4</td>
              <td colspan="1" rowspan="1">Cell B4</td>
              <td colspan="1" rowspan="1">Cell C4</td>
              <td colspan="1" rowspan="2">Merged D4-D5</td>
              <td colspan="1" rowspan="1">Cell E4</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A5</td>
              <td colspan="1" rowspan="1">Cell B5</td>
              <td colspan="1" rowspan="1">Cell C5</td>
              <td colspan="1" rowspan="1">Cell E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Verify dimensions match the complex example
			expect(grid.rowCount).toBe(5);
			expect(grid.colCount).toBe(5);

			// Verify all cells are properly mapped
			expect(grid.spanMap.size).toBe(25); // 5x5 grid positions
			expect(grid.elementMap.size).toBe(21); // 21 unique cells

			// Test specific merged cell behaviors
			const row2MergedCell = grid.cells[1][0];
			expect(row2MergedCell).toBe(grid.cells[1][1]);
			expect(row2MergedCell).toBe(grid.cells[1][2]);
			expect(row2MergedCell?.colSpan).toBe(3);
			expect(row2MergedCell?.element.textContent?.trim()).toBe("Merged ABC");

			const row3MergedCell = grid.cells[2][2];
			expect(row3MergedCell).toBe(grid.cells[2][3]);
			expect(row3MergedCell?.colSpan).toBe(2);
			expect(row3MergedCell?.element.textContent?.trim()).toBe("Merged CD");

			const row4MergedCell = grid.cells[3][3];
			expect(row4MergedCell).toBe(grid.cells[4][3]);
			expect(row4MergedCell?.rowSpan).toBe(2);
			expect(row4MergedCell?.element.textContent?.trim()).toBe("Merged D4-D5");

			// Verify grid validation
			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);

			// Test bounds expansion with the complex structure
			const expandedRow2 = analyzer.calculateExpandedBounds(grid, 1, 1, 1, 1);
			expect(expandedRow2.startCol).toBe(0);
			expect(expandedRow2.endCol).toBe(2);

			const expandedRow4 = analyzer.calculateExpandedBounds(grid, 3, 3, 3, 3);
			expect(expandedRow4.startRow).toBe(3);
			expect(expandedRow4.endRow).toBe(4);
		});

		it("should handle selection scenarios from the complex table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td>D1</td>
              <td>E1</td>
            </tr>
            <tr>
              <td colspan="3">ABC2</td>
              <td>D2</td>
              <td>E2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td colspan="2">CD3</td>
              <td>E3</td>
            </tr>
            <tr>
              <td>A4</td>
              <td>B4</td>
              <td>C4</td>
              <td rowspan="2">D4-D5</td>
              <td>E4</td>
            </tr>
            <tr>
              <td>A5</td>
              <td>B5</td>
              <td>C5</td>
              <td>E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Test selection that crosses multiple merged cells
			const cells = analyzer.getCellsInBounds(grid, 1, 3, 0, 4);
			expect(cells.size).toBe(13); // Should include all unique cells in the bounds

			// Test selection that partially overlaps merged cells
			const expandedBounds = analyzer.calculateExpandedBounds(grid, 1, 2, 1, 3);
			// Should expand to include full merged cells
			expect(expandedBounds.startRow).toBe(1);
			expect(expandedBounds.endRow).toBe(2);
			expect(expandedBounds.startCol).toBe(0);
			expect(expandedBounds.endCol).toBe(3);

			// Test selection that includes the rowspan cell
			const rowspanBounds = analyzer.calculateExpandedBounds(grid, 3, 3, 3, 3);
			expect(rowspanBounds.startRow).toBe(3);
			expect(rowspanBounds.endRow).toBe(4);
			expect(rowspanBounds.startCol).toBe(3);
			expect(rowspanBounds.endCol).toBe(3);
		});
	});

	describe("Advanced malformed table handling", () => {
		it("should handle tables with inconsistent row lengths", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td>D1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(4); // Based on the longest row

			// Row 2 should have null cells for missing positions
			expect(grid.cells[1][0]).not.toBeNull();
			expect(grid.cells[1][1]).not.toBeNull();
			expect(grid.cells[1][2]).toBeNull();
			expect(grid.cells[1][3]).toBeNull();

			// Row 3 should have null cell for missing position
			expect(grid.cells[2][0]).not.toBeNull();
			expect(grid.cells[2][1]).not.toBeNull();
			expect(grid.cells[2][2]).not.toBeNull();
			expect(grid.cells[2][3]).toBeNull();
		});

		it("should handle overlapping spans gracefully", () => {
			// This creates a scenario where spans might theoretically overlap
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="3">Wide cell</td>
              <td>D1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td colspan="2" rowspan="2">Overlapping</td>
              <td>D2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(4);

			// The analyzer should handle this by mapping cells sequentially
			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should handle extremely malformed span attributes", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="abc">Invalid colspan</td>
              <td rowspan="xyz">Invalid rowspan</td>
              <td colspan="">Empty colspan</td>
              <td rowspan="0">Zero rowspan</td>
              <td colspan="-5">Negative colspan</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// All invalid spans should default to 1
			expect(grid.cells[0][0]?.colSpan).toBe(1);
			expect(grid.cells[0][1]?.rowSpan).toBe(1);
			expect(grid.cells[0][2]?.colSpan).toBe(1);
			expect(grid.cells[0][3]?.rowSpan).toBe(1);
			expect(grid.cells[0][4]?.colSpan).toBe(1);

			expect(grid.colCount).toBe(5);
			expect(grid.rowCount).toBe(1);
		});

		it("should handle missing table sections gracefully", () => {
			const html = `
        <table>
          <tr>
            <td>Direct TR 1</td>
            <td>Direct TR 2</td>
          </tr>
          <tr>
            <td>Direct TR 3</td>
            <td>Direct TR 4</td>
          </tr>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2);
			expect(grid.colCount).toBe(2);
			expect(grid.cells[0][0]?.element.textContent?.trim()).toBe("Direct TR 1");
			expect(grid.cells[1][1]?.element.textContent?.trim()).toBe("Direct TR 4");
		});
	});

	describe("Performance and scalability tests", () => {
		it("should handle moderately large tables efficiently", () => {
			// Create a 20x20 table
			let html = '<table><tbody>';
			for (let row = 0; row < 20; row++) {
				html += '<tr>';
				for (let col = 0; col < 20; col++) {
					html += `<td>R${row}C${col}</td>`;
				}
				html += '</tr>';
			}
			html += '</tbody></table>';

			const table = createTableFromHTML(html);
			const startTime = performance.now();
			const grid = analyzer.analyzeTable(table);
			const endTime = performance.now();

			expect(grid.rowCount).toBe(20);
			expect(grid.colCount).toBe(20);
			expect(grid.spanMap.size).toBe(400);
			expect(grid.elementMap.size).toBe(400);

			// Should complete within reasonable time (less than 50ms)
			expect(endTime - startTime).toBeLessThan(50);

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should handle tables with many merged cells efficiently", () => {
			// Create a table with alternating merged cells
			let html = '<table><tbody>';
			for (let row = 0; row < 10; row++) {
				html += '<tr>';
				if (row % 2 === 0) {
					// Even rows have colspan=2 cells
					for (let col = 0; col < 5; col++) {
						html += `<td colspan="2">Merged R${row}C${col * 2}</td>`;
					}
				} else {
					// Odd rows have normal cells
					for (let col = 0; col < 10; col++) {
						html += `<td>R${row}C${col}</td>`;
					}
				}
				html += '</tr>';
			}
			html += '</tbody></table>';

			const table = createTableFromHTML(html);
			const startTime = performance.now();
			const grid = analyzer.analyzeTable(table);
			const endTime = performance.now();

			expect(grid.rowCount).toBe(10);
			expect(grid.colCount).toBe(10);

			// Should complete within reasonable time
			expect(endTime - startTime).toBeLessThan(50);

			// Verify merged cells are handled correctly
			expect(grid.cells[0][0]).toBe(grid.cells[0][1]);
			expect(grid.cells[0][0]?.colSpan).toBe(2);

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should respect size limits", () => {
			const smallAnalyzer = new TableGridAnalyzer({ maxSize: 10 });

			// Create a 4x4 table (16 cells > 10 limit)
			let html = '<table><tbody>';
			for (let row = 0; row < 4; row++) {
				html += '<tr>';
				for (let col = 0; col < 4; col++) {
					html += `<td>R${row}C${col}</td>`;
				}
				html += '</tr>';
			}
			html += '</tbody></table>';

			const table = createTableFromHTML(html);
			expect(() => smallAnalyzer.analyzeTable(table)).toThrow("Table too large");
		});
	});

	describe("Grid validation edge cases", () => {
		it("should detect inconsistent cell references", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2">Merged</td>
              <td>C1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>C2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Manually corrupt the grid to test validation
			const originalCell = grid.cells[0][1];
			grid.cells[0][1] = grid.cells[1][1]; // Create inconsistency

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(false);
			expect(validation.errors.length).toBeGreaterThan(0);

			// Restore for cleanup
			grid.cells[0][1] = originalCell;
		});

		it("should validate lookup map consistency", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Manually corrupt the spanMap to test validation
			grid.spanMap.clear();

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(false);
			expect(validation.errors.some(error => error.includes("SpanMap size mismatch"))).toBe(true);
		});
	});

	describe("Coordinate mapping accuracy", () => {
		it("should provide accurate coordinate mapping for complex structures", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td colspan="2" rowspan="2">B1-C1-B2-C2</td>
              <td>D1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>D2</td>
            </tr>
            <tr>
              <td colspan="4">Full width</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);
			const cells = table.querySelectorAll('td');

			// Test coordinate mapping for each cell
			const a1Coord = analyzer.getCellCoordinate(grid, cells[0] as HTMLTableCellElement);
			expect(a1Coord?.row).toBe(0);
			expect(a1Coord?.col).toBe(0);
			expect(a1Coord?.colSpan).toBe(1);
			expect(a1Coord?.rowSpan).toBe(1);

			const mergedCoord = analyzer.getCellCoordinate(grid, cells[1] as HTMLTableCellElement);
			expect(mergedCoord?.row).toBe(0);
			expect(mergedCoord?.col).toBe(1);
			expect(mergedCoord?.colSpan).toBe(2);
			expect(mergedCoord?.rowSpan).toBe(2);

			const fullWidthCoord = analyzer.getCellCoordinate(grid, cells[4] as HTMLTableCellElement);
			expect(fullWidthCoord?.row).toBe(2);
			expect(fullWidthCoord?.col).toBe(0);
			expect(fullWidthCoord?.colSpan).toBe(4);
			expect(fullWidthCoord?.rowSpan).toBe(1);

			// Test getCellAtPosition for various positions
			expect(analyzer.getCellAtPosition(grid, 0, 1)).toBe(mergedCoord);
			expect(analyzer.getCellAtPosition(grid, 0, 2)).toBe(mergedCoord);
			expect(analyzer.getCellAtPosition(grid, 1, 1)).toBe(mergedCoord);
			expect(analyzer.getCellAtPosition(grid, 1, 2)).toBe(mergedCoord);

			expect(analyzer.getCellAtPosition(grid, 2, 0)).toBe(fullWidthCoord);
			expect(analyzer.getCellAtPosition(grid, 2, 3)).toBe(fullWidthCoord);
		});

		it("should handle coordinate queries at grid boundaries", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Test boundary conditions
			expect(analyzer.getCellAtPosition(grid, -1, 0)).toBeNull();
			expect(analyzer.getCellAtPosition(grid, 0, -1)).toBeNull();
			expect(analyzer.getCellAtPosition(grid, 2, 0)).toBeNull();
			expect(analyzer.getCellAtPosition(grid, 0, 2)).toBeNull();
			expect(analyzer.getCellAtPosition(grid, 100, 100)).toBeNull();

			// Test valid boundary positions
			expect(analyzer.getCellAtPosition(grid, 0, 0)).not.toBeNull();
			expect(analyzer.getCellAtPosition(grid, 1, 1)).not.toBeNull();
		});
	});

	describe("Bounds calculation with complex scenarios", () => {
		it("should handle bounds calculation with multiple overlapping spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2" rowspan="2">A1-B1-A2-B2</td>
              <td>C1</td>
              <td rowspan="3">D1-D2-D3</td>
            </tr>
            <tr>
              <td>C2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td colspan="2">B3-C3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Test bounds that intersect multiple merged cells
			const bounds1 = analyzer.calculateExpandedBounds(grid, 0, 1, 0, 2);
			expect(bounds1.startRow).toBe(0);
			expect(bounds1.endRow).toBe(2);
			expect(bounds1.startCol).toBe(0);
			expect(bounds1.endCol).toBe(3);

			// Test bounds that start in middle of merged cell
			const bounds2 = analyzer.calculateExpandedBounds(grid, 0, 0, 1, 1);
			expect(bounds2.startRow).toBe(0);
			expect(bounds2.endRow).toBe(1);
			expect(bounds2.startCol).toBe(0);
			expect(bounds2.endCol).toBe(1);

			// Test bounds that include the vertical span
			const bounds3 = analyzer.calculateExpandedBounds(grid, 1, 1, 3, 3);
			expect(bounds3.startRow).toBe(0);
			expect(bounds3.endRow).toBe(2);
			expect(bounds3.startCol).toBe(3);
			expect(bounds3.endCol).toBe(3);
		});

		it("should handle getCellsInBounds with complex spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td colspan="2">B1-C1</td>
              <td>D1</td>
            </tr>
            <tr>
              <td rowspan="2">A2-A3</td>
              <td>B2</td>
              <td>C2</td>
              <td>D2</td>
            </tr>
            <tr>
              <td>B3</td>
              <td>C3</td>
              <td>D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Get cells in a specific rectangular region
			const cells = analyzer.getCellsInBounds(grid, 0, 1, 1, 2);
			expect(cells.size).toBe(4); // Should include 4 unique cells

			// Convert to array to verify contents
			const cellArray = Array.from(cells);
			const cellTexts = cellArray.map(cell => cell.element.textContent?.trim()).sort();
			expect(cellTexts).toEqual(["A2-A3", "B1-C1", "B2", "C2"]);

			// Test bounds that extend beyond grid
			const cellsExtended = analyzer.getCellsInBounds(grid, 0, 10, 0, 10);
			expect(cellsExtended.size).toBe(7); // Should only include existing cells
			// Test getCellAtPosition for various positions
			expect(analyzer.getCellAtPosition(grid, 0, 0)?.element.textContent?.trim()).toBe("A1");
			expect(analyzer.getCellAtPosition(grid, 0, 1)?.element.textContent?.trim()).toBe("B1-C1-D1");
			expect(analyzer.getCellAtPosition(grid, 1, 0)?.element.textContent?.trim()).toBe("A2-A3");
		});
	});

	describe("Real-world complex table from table.example.html", () => {
		it("should correctly analyze the actual table.example.html structure", () => {
			// This represents the exact structure from table.example.html
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="1" rowspan="1">Cell 1</td>
              <td colspan="1" rowspan="1">Cell 2</td>
              <td colspan="1" rowspan="1">Cell 3</td>
              <td colspan="1" rowspan="1">Cell 4</td>
              <td colspan="1" rowspan="1">Cell 5</td>
            </tr>
            <tr>
              <td colspan="3" rowspan="1">Merged ABC</td>
              <td colspan="1" rowspan="1">Cell D2</td>
              <td colspan="1" rowspan="1">Cell E2</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A3</td>
              <td colspan="1" rowspan="1">Cell B3</td>
              <td colspan="2" rowspan="1">Merged CD</td>
              <td colspan="1" rowspan="1">Cell E3</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A4</td>
              <td colspan="1" rowspan="1">Cell B4</td>
              <td colspan="1" rowspan="1">Cell C4</td>
              <td colspan="1" rowspan="2">Merged D4-D5</td>
              <td colspan="1" rowspan="1">Cell E4</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A5</td>
              <td colspan="1" rowspan="1">Cell B5</td>
              <td colspan="1" rowspan="1">Cell C5</td>
              <td colspan="1" rowspan="1">Cell E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Verify basic structure
			expect(grid.rowCount).toBe(5);
			expect(grid.colCount).toBe(5);

			// Test specific merged cells from the example
			// Row 2: colspan="3" (positions 0,1,2 should be same cell)
			const row2MergedCell = grid.cells[1][0];
			expect(row2MergedCell).toBe(grid.cells[1][1]);
			expect(row2MergedCell).toBe(grid.cells[1][2]);
			expect(row2MergedCell?.colSpan).toBe(3);
			expect(row2MergedCell?.element.textContent?.trim()).toBe("Merged ABC");

			// Row 3: colspan="2" (positions 2,3 should be same cell)
			const row3MergedCell = grid.cells[2][2];
			expect(row3MergedCell).toBe(grid.cells[2][3]);
			expect(row3MergedCell?.colSpan).toBe(2);
			expect(row3MergedCell?.element.textContent?.trim()).toBe("Merged CD");

			// Row 4-5: rowspan="2" (positions [3][3] and [4][3] should be same cell)
			const row4MergedCell = grid.cells[3][3];
			expect(row4MergedCell).toBe(grid.cells[4][3]);
			expect(row4MergedCell?.rowSpan).toBe(2);
			expect(row4MergedCell?.element.textContent?.trim()).toBe("Merged D4-D5");

			// Test bounds expansion with this specific structure
			// Selecting just the merged cell in row 2 should expand to full colspan
			const expandedRow2 = analyzer.calculateExpandedBounds(grid, 1, 1, 1, 1);
			expect(expandedRow2.startRow).toBe(1);
			expect(expandedRow2.endRow).toBe(1);
			expect(expandedRow2.startCol).toBe(0);
			expect(expandedRow2.endCol).toBe(2);

			// Selecting just the merged cell in row 4 should expand to full rowspan
			const expandedRow4 = analyzer.calculateExpandedBounds(grid, 3, 3, 3, 3);
			expect(expandedRow4.startRow).toBe(3);
			expect(expandedRow4.endRow).toBe(4);
			expect(expandedRow4.startCol).toBe(3);
			expect(expandedRow4.endCol).toBe(3);

			// Test selection that crosses multiple merged cells
			const crossSelection = analyzer.calculateExpandedBounds(grid, 1, 3, 1, 3);
			expect(crossSelection.startRow).toBe(1);
			expect(crossSelection.endRow).toBe(4);
			expect(crossSelection.startCol).toBe(0);
			expect(crossSelection.endCol).toBe(3);

			// Verify grid validation passes
			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
			expect(validation.errors).toHaveLength(0);
		});

		it("should handle performance with the complex example table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="1" rowspan="1">Cell 1</td>
              <td colspan="1" rowspan="1">Cell 2</td>
              <td colspan="1" rowspan="1">Cell 3</td>
              <td colspan="1" rowspan="1">Cell 4</td>
              <td colspan="1" rowspan="1">Cell 5</td>
            </tr>
            <tr>
              <td colspan="3" rowspan="1">Merged ABC</td>
              <td colspan="1" rowspan="1">Cell D2</td>
              <td colspan="1" rowspan="1">Cell E2</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A3</td>
              <td colspan="1" rowspan="1">Cell B3</td>
              <td colspan="2" rowspan="1">Merged CD</td>
              <td colspan="1" rowspan="1">Cell E3</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A4</td>
              <td colspan="1" rowspan="1">Cell B4</td>
              <td colspan="1" rowspan="1">Cell C4</td>
              <td colspan="1" rowspan="2">Merged D4-D5</td>
              <td colspan="1" rowspan="1">Cell E4</td>
            </tr>
            <tr>
              <td colspan="1" rowspan="1">Cell A5</td>
              <td colspan="1" rowspan="1">Cell B5</td>
              <td colspan="1" rowspan="1">Cell C5</td>
              <td colspan="1" rowspan="1">Cell E5</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			
			// Measure performance
			const startTime = performance.now();
			const grid = analyzer.analyzeTable(table);
			const endTime = performance.now();

			// Should complete quickly (less than 10ms for this small table)
			expect(endTime - startTime).toBeLessThan(10);

			// Verify correctness wasn't sacrificed for performance
			expect(grid.rowCount).toBe(5);
			expect(grid.colCount).toBe(5);
			expect(grid.spanMap.size).toBe(25); // 5x5 positions
			expect(grid.elementMap.size).toBe(21); // 21 unique cells

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});
	});

	describe("Advanced edge cases and stress testing", () => {
		it("should handle extremely complex nested spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="4" rowspan="2">Large 4x2</td>
              <td rowspan="4">Tall 1x4</td>
            </tr>
            <tr>
              <!-- Large cell continues -->
            </tr>
            <tr>
              <td>A3</td>
              <td colspan="3" rowspan="2">Wide 3x2</td>
            </tr>
            <tr>
              <td>A4</td>
              <!-- Wide cell continues -->
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(4);
			expect(grid.colCount).toBe(5);

			// Verify the large 4x2 cell
			const largeCell = grid.cells[0][0];
			expect(largeCell?.colSpan).toBe(4);
			expect(largeCell?.rowSpan).toBe(2);
			
			// Check all positions occupied by large cell
			for (let r = 0; r < 2; r++) {
				for (let c = 0; c < 4; c++) {
					expect(grid.cells[r][c]).toBe(largeCell);
				}
			}

			// Verify the tall 1x4 cell
			const tallCell = grid.cells[0][4];
			expect(tallCell?.rowSpan).toBe(4);
			for (let r = 0; r < 4; r++) {
				expect(grid.cells[r][4]).toBe(tallCell);
			}

			// Verify the wide 3x2 cell
			const wideCell = grid.cells[2][1];
			expect(wideCell?.colSpan).toBe(3);
			expect(wideCell?.rowSpan).toBe(2);
			for (let r = 2; r < 4; r++) {
				for (let c = 1; c < 4; c++) {
					expect(grid.cells[r][c]).toBe(wideCell);
				}
			}

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});

		it("should handle tables with irregular row lengths", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
              <td>C1</td>
              <td>D1</td>
              <td>E1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>C2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
              <td>D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(5); // Based on the longest row

			// Row 1 should have all 5 cells
			expect(grid.cells[0][0]).not.toBeNull();
			expect(grid.cells[0][1]).not.toBeNull();
			expect(grid.cells[0][2]).not.toBeNull();
			expect(grid.cells[0][3]).not.toBeNull();
			expect(grid.cells[0][4]).not.toBeNull();

			// Row 2 should have only 3 cells, rest should be null
			expect(grid.cells[1][0]).not.toBeNull();
			expect(grid.cells[1][1]).not.toBeNull();
			expect(grid.cells[1][2]).not.toBeNull();
			expect(grid.cells[1][3]).toBeNull();
			expect(grid.cells[1][4]).toBeNull();

			// Row 3 should have 4 cells
			expect(grid.cells[2][0]).not.toBeNull();
			expect(grid.cells[2][1]).not.toBeNull();
			expect(grid.cells[2][2]).not.toBeNull();
			expect(grid.cells[2][3]).not.toBeNull();
			expect(grid.cells[2][4]).toBeNull();
		});

		it("should handle span values that exceed table boundaries", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="10">Too wide</td>
            </tr>
            <tr>
              <td rowspan="10">Too tall</td>
              <td>Normal</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// The analyzer should cap spans at reasonable values
			expect(grid.cells[0][0]?.colSpan).toBe(10); // Allowed but capped at grid size
			expect(grid.cells[1][0]?.rowSpan).toBe(10); // Allowed but capped at grid size

			// Grid should be sized appropriately
			expect(grid.colCount).toBe(10);
			expect(grid.rowCount).toBe(2);

			// Validation should handle this gracefully
			const validation = analyzer.validateGrid(grid);
			// This might have errors due to spans exceeding actual grid bounds
			if (!validation.isValid) {
				expect(validation.errors.length).toBeGreaterThan(0);
				expect(validation.errors.some(error => error.includes('extends beyond grid'))).toBe(true);
			}
		});

		it("should handle tables with only header or footer sections", () => {
			const html = `
        <table>
          <thead>
            <tr>
              <th>Header 1</th>
              <th colspan="2">Header 2-3</th>
            </tr>
          </thead>
          <tfoot>
            <tr>
              <td colspan="3">Footer</td>
            </tr>
          </tfoot>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(2); // thead + tfoot
			expect(grid.colCount).toBe(3);

			// Header row
			expect(grid.cells[0][1]).toBe(grid.cells[0][2]);
			expect(grid.cells[0][1]?.colSpan).toBe(2);

			// Footer row
			expect(grid.cells[1][0]).toBe(grid.cells[1][1]);
			expect(grid.cells[1][0]).toBe(grid.cells[1][2]);
			expect(grid.cells[1][0]?.colSpan).toBe(3);
		});

		it("should handle mixed th and td elements", () => {
			const html = `
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Age</th>
              <th>City</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>John</td>
              <td>25</td>
              <td>NYC</td>
            </tr>
            <tr>
              <th>Average</th>
              <td colspan="2">25 years, NYC</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(3);
			expect(grid.colCount).toBe(3);

			// Mixed th/td should work the same way
			expect(grid.cells[2][1]).toBe(grid.cells[2][2]);
			expect(grid.cells[2][1]?.colSpan).toBe(2);

			// Verify element types are preserved
			expect(grid.cells[0][0]?.element.tagName.toLowerCase()).toBe('th');
			expect(grid.cells[1][0]?.element.tagName.toLowerCase()).toBe('td');
			expect(grid.cells[2][0]?.element.tagName.toLowerCase()).toBe('th');
		});
	});

	describe("Comprehensive bounds calculation testing", () => {
		it("should correctly expand bounds for overlapping merged cells", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2">A1-B1</td>
              <td rowspan="2">C1-C2</td>
              <td>D1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>D2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td colspan="3">B3-C3-D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Test various selection scenarios
			
			// Select just A2 - should not expand
			let expanded = analyzer.calculateExpandedBounds(grid, 1, 1, 0, 0);
			expect(expanded.startRow).toBe(1);
			expect(expanded.endRow).toBe(1);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(0);

			// Select A1 (part of merged cell) - should expand to include B1
			expanded = analyzer.calculateExpandedBounds(grid, 0, 0, 0, 0);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(0);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(1);

			// Select C1 (part of rowspan) - should expand to include C2
			expanded = analyzer.calculateExpandedBounds(grid, 0, 0, 2, 2);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(1);
			expect(expanded.startCol).toBe(2);
			expect(expanded.endCol).toBe(2);

			// Select B3 (part of large merged cell) - should expand to include C3-D3
			expanded = analyzer.calculateExpandedBounds(grid, 2, 2, 1, 1);
			expect(expanded.startRow).toBe(2);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(1);
			expect(expanded.endCol).toBe(3);

			// Select area that crosses multiple merged cells
			expanded = analyzer.calculateExpandedBounds(grid, 0, 2, 1, 2);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(3);
		});

		it("should handle bounds calculation with complex intersecting spans", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="3" rowspan="2">Large</td>
              <td>D1</td>
              <td rowspan="3">E1-E2-E3</td>
            </tr>
            <tr>
              <td>D2</td>
            </tr>
            <tr>
              <td>A3</td>
              <td>B3</td>
              <td>C3</td>
              <td>D3</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Select middle of large cell - should expand to full cell
			let expanded = analyzer.calculateExpandedBounds(grid, 0, 0, 1, 1);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(1);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(2);

			// Select area that intersects with vertical span
			expanded = analyzer.calculateExpandedBounds(grid, 1, 2, 4, 4);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(4);
			expect(expanded.endCol).toBe(4);

			// Select area that intersects multiple complex spans
			expanded = analyzer.calculateExpandedBounds(grid, 0, 2, 2, 4);
			expect(expanded.startRow).toBe(0);
			expect(expanded.endRow).toBe(2);
			expect(expanded.startCol).toBe(0);
			expect(expanded.endCol).toBe(4);
		});
	});

	describe("Grid validation comprehensive testing", () => {
		it("should detect inconsistent cell references", () => {
			// Create a grid manually with inconsistent references
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2">Merged</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Manually corrupt the grid to test validation
			const originalCell = grid.cells[0][0];
			const fakeCell = { ...originalCell!, id: 'fake' } as CellCoordinate;
			grid.cells[0][1] = fakeCell; // This should cause validation to fail

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(false);
			expect(validation.errors.length).toBeGreaterThan(0);
		});

		it("should validate lookup map consistency", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Manually corrupt the spanMap to test validation
			grid.spanMap.clear();

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(false);
			expect(validation.errors.some(error => error.includes('SpanMap size mismatch'))).toBe(true);
		});

		it("should validate element map consistency", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>A1</td>
              <td>B1</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			// Manually corrupt the elementMap to test validation
			grid.elementMap.clear();

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(false);
			expect(validation.errors.some(error => error.includes('ElementMap size mismatch'))).toBe(true);
		});
	});

	describe("Memory and performance stress testing", () => {
		it("should handle repeated analysis without memory leaks", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td colspan="2">A1-B1</td>
              <td>C1</td>
            </tr>
            <tr>
              <td>A2</td>
              <td>B2</td>
              <td>C2</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);

			// Analyze the same table multiple times
			const results = [];
			for (let i = 0; i < 100; i++) {
				const grid = analyzer.analyzeTable(table);
				results.push(grid);
			}

			// All results should be consistent
			for (const grid of results) {
				expect(grid.rowCount).toBe(2);
				expect(grid.colCount).toBe(3);
				expect(grid.spanMap.size).toBe(6);
				expect(grid.elementMap.size).toBe(5);
			}
		});

		it("should handle maximum size constraints", () => {
			const smallAnalyzer = new TableGridAnalyzer({ maxSize: 9 }); // 3x3 max

			const html = `
        <table>
          <tbody>
            <tr><td>1</td><td>2</td><td>3</td><td>4</td></tr>
            <tr><td>5</td><td>6</td><td>7</td><td>8</td></tr>
            <tr><td>9</td><td>10</td><td>11</td><td>12</td></tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			expect(() => smallAnalyzer.analyzeTable(table)).toThrow('Table too large');
		});

		it("should handle edge case of single cell table", () => {
			const html = `
        <table>
          <tbody>
            <tr>
              <td>Single cell</td>
            </tr>
          </tbody>
        </table>
      `;

			const table = createTableFromHTML(html);
			const grid = analyzer.analyzeTable(table);

			expect(grid.rowCount).toBe(1);
			expect(grid.colCount).toBe(1);
			expect(grid.cells[0][0]?.element.textContent?.trim()).toBe("Single cell");

			const validation = analyzer.validateGrid(grid);
			expect(validation.isValid).toBe(true);
		});
	});
});
