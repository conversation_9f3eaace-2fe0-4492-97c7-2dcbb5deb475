import type { TableProperties } from "@/types/table";
import { pxToMm } from "./unitConversion";

export function getTableBorderContributions(tableProperties?: TableProperties) {
	if (!tableProperties) {
		return { h: 0, v: 0, left: 0, top: 0, right: 0, bottom: 0 };
	}
	const { cells, rows, columns } = tableProperties;

	let maxLeftBorderPx = 0;
	if (cells) {
		cells.forEach((row) => {
			if (row?.[0]) {
				const cell = row[0];
				const borderWidth =
					cell.borderSettings?.left?.width ?? cell.borderWidths?.left ?? 0;
				if (borderWidth > maxLeftBorderPx) maxLeftBorderPx = borderWidth;
			}
		});
	}

	let maxRightBorderPx = 0;
	if (cells && columns > 0) {
		cells.forEach((row) => {
			let logicalCol = 0;
			for (const cell of row) {
				if (!cell) continue;
				logicalCol += cell.colspan;
				if (logicalCol >= columns) {
					const borderWidth =
						cell.borderSettings?.right?.width ?? cell.borderWidths?.right ?? 0;
					if (borderWidth > maxRightBorderPx) maxRightBorderPx = borderWidth;
					break;
				}
			}
		});
	}

	let maxTopBorderPx = 0;
	if (cells?.[0]) {
		cells[0].forEach((cell) => {
			if (cell) {
				const borderWidth =
					cell.borderSettings?.top?.width ?? cell.borderWidths?.top ?? 0;
				if (borderWidth > maxTopBorderPx) maxTopBorderPx = borderWidth;
			}
		});
	}

	let maxBottomBorderPx = 0;
	if (cells && rows > 0) {
		cells.forEach((row, rowIndex) => {
			row.forEach((cell) => {
				if (cell && rowIndex + cell.rowspan >= rows) {
					const borderWidth =
						cell.borderSettings?.bottom?.width ??
						cell.borderWidths?.bottom ??
						0;
					if (borderWidth > maxBottomBorderPx) maxBottomBorderPx = borderWidth;
				}
			});
		});
	}

	// With border-collapse, half of the border's width extends outwards.
	const horizontalContributionMm = pxToMm(
		(maxLeftBorderPx + maxRightBorderPx) / 2,
	);
	const verticalContributionMm = pxToMm(
		(maxTopBorderPx + maxBottomBorderPx) / 2,
	);
	const leftShiftMm = pxToMm(maxLeftBorderPx / 2);
	const topShiftMm = pxToMm(maxTopBorderPx / 2);

	return {
		h: horizontalContributionMm,
		v: verticalContributionMm,
		left: leftShiftMm,
		top: topShiftMm,
		right: pxToMm(maxRightBorderPx / 2),
		bottom: pxToMm(maxBottomBorderPx / 2),
	};
}

// Calculates scaled column widths and row heights for a table so that it fits into
// the provided container dimensions while preserving the original aspect ratio.
//
// initialColumnWidths / initialRowHeights – intrinsic dimensions in mm stored on the element.
// containerWidthMm / containerHeightMm    – the available space in mm. If omitted the
//                                            intrinsic total size is used which results in
//                                            a scale factor of 1.
//
// The function returns the scaled widths / heights as well as a flag that indicates whether
// the scaling is actually based on *valid* container dimensions (both container and intrinsic
// dimensions > 0).
export function calculateScaledDimensions(
	initialColumnWidths: number[] | undefined,
	initialRowHeights: number[] | undefined,
	containerWidthMm?: number,
	containerHeightMm?: number,
) {
	const intrinsicWidthMm =
		initialColumnWidths?.reduce((sum, w) => sum + w, 0) || 0;
	const intrinsicHeightMm =
		initialRowHeights?.reduce((sum, h) => sum + h, 0) || 0;

	const currentContainerWidthMm = containerWidthMm ?? intrinsicWidthMm;
	const currentContainerHeightMm = containerHeightMm ?? intrinsicHeightMm;

	// Both container and intrinsic dimensions have to be > 0 – otherwise no scaling is applied.
	const hasValidDimensions =
		currentContainerWidthMm > 0 &&
		currentContainerHeightMm > 0 &&
		intrinsicWidthMm > 0 &&
		intrinsicHeightMm > 0;

	// If we do not have valid data fall back to a scale factor of 1.
	const scaleX = hasValidDimensions
		? currentContainerWidthMm / intrinsicWidthMm
		: 1;
	const scaleY = hasValidDimensions
		? currentContainerHeightMm / intrinsicHeightMm
		: 1;

	const scaledWidths = initialColumnWidths
		? initialColumnWidths.map((w) => w * scaleX)
		: [];
	const scaledHeights = initialRowHeights
		? initialRowHeights.map((h) => h * scaleY)
		: [];

	return {
		scaledWidths,
		scaledHeights,
		hasValidDimensions,
		intrinsicWidthMm,
		intrinsicHeightMm,
	} as const;
}
