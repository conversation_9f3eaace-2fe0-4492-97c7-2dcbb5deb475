/**
 * Enhanced table selection types for handling complex table structures
 * with colspan and rowspan attributes
 */

/**
 * Represents a cell's coordinate and span information in the logical grid
 */
export interface CellCoordinate {
  /** Logical row index in the grid */
  row: number;
  /** Logical column index in the grid */
  col: number;
  /** Number of rows this cell spans */
  rowSpan: number;
  /** Number of columns this cell spans */
  colSpan: number;
  /** Reference to the actual DOM element */
  element: HTMLTableCellElement;
  /** Unique identifier for this cell */
  id: string;
}

/**
 * Represents the logical grid structure of a table
 */
export interface TableGrid {
  /** 2D array representing the logical grid structure */
  cells: (CellCoordinate | null)[][];
  /** Total number of logical rows */
  rowCount: number;
  /** Total number of logical columns */
  colCount: number;
  /** Maps "row,col" coordinates to cell information */
  spanMap: Map<string, CellCoordinate>;
  /** Maps DOM element to its coordinate information */
  elementMap: Map<HTMLTableCellElement, CellCoordinate>;
}

/**
 * Represents selection bounds in the logical grid
 */
export interface SelectionBounds {
  /** Starting row (inclusive) */
  startRow: number;
  /** Ending row (inclusive) */
  endRow: number;
  /** Starting column (inclusive) */
  startCol: number;
  /** Ending column (inclusive) */
  endCol: number;
}

/**
 * Represents the current table selection state
 */
export interface TableSelection {
  /** Starting cell coordinate */
  startCell: CellCoordinate;
  /** Ending cell coordinate */
  endCell: CellCoordinate;
  /** Set of all selected cell coordinates */
  selectedCells: Set<CellCoordinate>;
  /** Rectangular bounds of the selection */
  selectionBounds: SelectionBounds;
  /** Whether selection is currently active */
  isActive: boolean;
}

/**
 * Configuration options for table grid analysis
 */
export interface TableGridAnalysisOptions {
  /** Whether to handle malformed table structures gracefully */
  handleMalformed?: boolean;
  /** Maximum table size to analyze (for performance) */
  maxSize?: number;
}