import { Plus, Trash2 } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { type Editor, RichTextEditor } from "@/components/RichTextEditor";
import { But<PERSON> } from "@/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import type { TableCell, TableProperties } from "@/types/table";
import type { TestDataRecord } from "@/utils/apiService";
import { processContentWithVariables } from "@/utils/contentProcessing";
import { calculateScaledDimensions } from "@/utils/tableUtils";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import { TableBodyContent } from "./Table";
import { TableGridAnalyzer } from "@/services/tableGridAnalyzer";
import { TableSelectionManager } from "@/services/tableSelectionManager";
import { 
	TableSelectionOverlay, 
	TableCellSelectionIndicator 
} from "./TableSelectionOverlay";
import type { 
	CellCoordinate, 
	TableGrid, 
	TableSelection 
} from "@/types/tableSelection";

interface TableEditorProps {
	tableProperties: TableProperties;
	onChange: (properties: TableProperties) => void;
	setActiveEditor: (editor: Editor | null) => void; // Add this prop
	containerWidthMm?: number;
	containerHeightMm?: number;
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
	setIsTextEditorFocused?: (focused: boolean) => void;
}

// Helper to compare selection objects by value
const areSelectionsEqual = (
	selA: TableProperties["selection"],
	selB: TableProperties["selection"],
): boolean => {
	if (selA === selB) return true; // Handles if both are null or same reference
	if (!selA || !selB) return false; // One is null, the other isn't
	return (
		selA.start.row === selB.start.row &&
		selA.start.col === selB.start.col &&
		selA.end.row === selB.end.row &&
		selA.end.col === selB.end.col
	);
};

export function TableEditor({
	tableProperties,
	onChange,
	setActiveEditor,
	containerWidthMm,
	containerHeightMm,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
	setIsTextEditorFocused,
}: TableEditorProps) {
	const [resizingCol, setResizingCol] = useState<number | null>(null);
	const [resizingRow, setResizingRow] = useState<number | null>(null);
	const [selectedCells, setSelectedCells] =
		useState<TableProperties["selection"]>(null);
	const [isShiftPressed, setIsShiftPressed] = useState(false);
	const [isDragging, setIsDragging] = useState(false);
	const [columnWidths, setColumnWidths] = useState<number[]>([]);
	const [rowHeights, setRowHeights] = useState<number[]>([]);
	const [isResizing, setIsResizing] = useState(false);
	const [editingCell, setEditingCell] = useState<{
		row: number;
		col: number;
	} | null>(null);
	const [intrinsicColumnWidths, setIntrinsicColumnWidths] = useState<number[]>(
		[],
	);
	const [intrinsicRowHeights, setIntrinsicRowHeights] = useState<number[]>([]);
	
	// Enhanced selection state
	const [tableGrid, setTableGrid] = useState<TableGrid | null>(null);
	const [enhancedSelection, setEnhancedSelection] = useState<TableSelection | null>(null);
	const [isEnhancedDragging, setIsEnhancedDragging] = useState(false);
	const [dragStartCell, setDragStartCell] = useState<CellCoordinate | null>(null);
	const [previewSelection, setPreviewSelection] = useState<TableSelection | null>(null);
	const [hoveredCell, setHoveredCell] = useState<{ row: number; col: number } | null>(null);
	
	// Services
	const gridAnalyzerRef = useRef(new TableGridAnalyzer());
	const selectionManagerRef = useRef(new TableSelectionManager());
	
	const startPosRef = useRef({ x: 0, y: 0 });
	const tableRef = useRef<HTMLTableElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const tableWidthRef = useRef<number>(0);
	const resizeIndicatorRef = useRef<HTMLDivElement | null>(null);
	const initialSizeRef = useRef<{ width?: number; height?: number }>({});

	// Minimum dimensions in mm
	const minWidthMm = 2;
	const minHeightMm = 2;

	const handleResizeStart = (
		e: React.MouseEvent,
		index: number,
		type: "col" | "row",
	) => {
		e.preventDefault();
		e.stopPropagation(); // Prevent selection handling
		setIsResizing(true);

		// Store initial size
		if (type === "col") {
			setResizingCol(index);
			initialSizeRef.current.width = intrinsicColumnWidths[index] || 15;
			// Highlight entire column
			const rows = tableRef.current?.getElementsByTagName("tr");
			if (rows) {
				for (let i = 0; i < rows.length; i++) {
					const cell = rows[i].getElementsByTagName("td")[index];
					if (cell) cell.classList.add("bg-blue-50");
				}
			}

			// Create visual resize indicator
			if (!resizeIndicatorRef.current && containerRef.current) {
				const indicator = document.createElement("div");
				indicator.className = "absolute bg-blue-500 pointer-events-none z-50";
				indicator.style.width = "2px";
				indicator.style.top = "0";
				indicator.style.bottom = "0";
				containerRef.current.appendChild(indicator);
				resizeIndicatorRef.current = indicator;
			}

			// Position the indicator at the exact mouse location on mousedown
			if (resizeIndicatorRef.current && containerRef.current) {
				const containerRect = containerRef.current.getBoundingClientRect();
				resizeIndicatorRef.current.style.left = `${e.clientX - containerRect.left}px`;
			}
		} else {
			setResizingRow(index);
			initialSizeRef.current.height = intrinsicRowHeights[index] || 10;
			// Highlight entire row
			const row = tableRef.current?.getElementsByTagName("tr")[index];
			if (row) {
				const cells = row.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.add("bg-blue-50");
				}
			}

			// Create visual resize indicator
			if (!resizeIndicatorRef.current && containerRef.current) {
				const indicator = document.createElement("div");
				indicator.className = "absolute bg-blue-500 pointer-events-none z-50";
				indicator.style.height = "2px";
				indicator.style.left = "0";
				indicator.style.right = "0";
				containerRef.current.appendChild(indicator);
				resizeIndicatorRef.current = indicator;
			}

			// Position the indicator at the exact mouse location on mousedown
			if (resizeIndicatorRef.current && containerRef.current) {
				const containerRect = containerRef.current.getBoundingClientRect();
				resizeIndicatorRef.current.style.top = `${e.clientY - containerRect.top}px`;
			}
		}
		startPosRef.current = { x: e.clientX, y: e.clientY };
	};

	useEffect(() => {
		if (tableRef.current) {
			tableWidthRef.current = tableRef.current.offsetWidth;
		}
		// Always sync state with props when they change
		// Use provided defaults if not present (15mm width, 10mm height)
		const initialColWidths =
			tableProperties.columnWidths || Array(tableProperties.columns).fill(15);
		const initialRowHeights =
			tableProperties.rowHeights || Array(tableProperties.rows).fill(10);

		// Use shared helper to compute scaled dimensions based on available container size
		const { scaledWidths, scaledHeights } = calculateScaledDimensions(
			initialColWidths,
			initialRowHeights,
			containerWidthMm,
			containerHeightMm,
		);

		setColumnWidths(scaledWidths);
		setRowHeights(scaledHeights);
		setIntrinsicColumnWidths(initialColWidths);
		setIntrinsicRowHeights(initialRowHeights);
	}, [
		tableProperties.columnWidths,
		tableProperties.rowHeights,
		tableProperties.columns,
		tableProperties.rows,
		containerWidthMm,
		containerHeightMm,
	]); // Update when dimensions change

	// Analyze table grid when table structure changes
	useEffect(() => {
		if (tableRef.current) {
			try {
				const grid = gridAnalyzerRef.current.analyzeTable(tableRef.current);
				setTableGrid(grid);
				
				// Validate existing selections against new grid
				if (enhancedSelection && grid) {
					const isValid = selectionManagerRef.current.validateSelection(enhancedSelection, grid);
					if (!isValid) {
						// Clear invalid selection
						setEnhancedSelection(null);
						setPreviewSelection(null);
						setSelectedCells(null);
					}
				}
			} catch (error) {
				console.warn('Failed to analyze table grid:', error);
				setTableGrid(null);
				// Clear selections if grid analysis fails
				setEnhancedSelection(null);
				setPreviewSelection(null);
			}
		}
	}, [
		tableProperties.cells,
		tableProperties.columns,
		tableProperties.rows,
		columnWidths,
		rowHeights,
		enhancedSelection,
	]);

	// Effect to apply selectedCellsBackgroundColor
	useEffect(() => {
		if (tableProperties.selectedCellsBackgroundColor && selectedCells) {
			const newCells = tableProperties.cells.map((row, rIdx) =>
				row.map((cell, cIdx) => {
					const isCellSelected =
						rIdx >= Math.min(selectedCells.start.row, selectedCells.end.row) &&
						rIdx <= Math.max(selectedCells.start.row, selectedCells.end.row) &&
						cIdx >= Math.min(selectedCells.start.col, selectedCells.end.col) &&
						cIdx <= Math.max(selectedCells.start.col, selectedCells.end.col);

					if (isCellSelected) {
						return {
							...cell,
							backgroundColor:
								tableProperties.selectedCellsBackgroundColor || null,
						};
					}
					return cell;
				}),
			);

			onChange({
				...tableProperties,
				cells: newCells,
				selectedCellsBackgroundColor: undefined, // Clear the color after applying
			});
		}
	}, [tableProperties, selectedCells, onChange]);

	// This useEffect is responsible for calling onChange when selectedCells (internal state) changes
	// and is different from what is currently in tableProperties.selection.
	useEffect(() => {
		const currentReportedSelection = tableProperties.selection;

		if (!areSelectionsEqual(selectedCells, currentReportedSelection)) {
			onChange({
				...tableProperties,
				selection: selectedCells,
			});
		}
	}, [selectedCells, onChange, tableProperties]); // tableProperties is a dep because we spread it and read .selection

	useEffect(() => {
		// Capture the table ref value to use in cleanup
		const table = tableRef.current;

		const handleMouseMove = (e: MouseEvent) => {
			if (
				resizingCol !== null &&
				tableRef.current &&
				resizeIndicatorRef.current
			) {
				// Directly position the guide at the cursor (we'll enforce min-width later on mouseup)
				const containerRect = containerRef.current?.getBoundingClientRect();
				if (containerRect) {
					const indicatorPos = e.clientX - containerRect.left;
					resizeIndicatorRef.current.style.left = `${indicatorPos}px`;
				}
			}
			if (
				resizingRow !== null &&
				tableRef.current &&
				resizeIndicatorRef.current
			) {
				// Directly position the guide at the cursor (we'll enforce min-height later on mouseup)
				const containerRect = containerRef.current?.getBoundingClientRect();
				if (containerRect) {
					const indicatorPos = e.clientY - containerRect.top;
					resizeIndicatorRef.current.style.top = `${indicatorPos}px`;
				}
			}
		};

		const handleMouseUp = (e: MouseEvent) => {
			if ((resizingCol !== null || resizingRow !== null) && tableRef.current) {
				// Remove highlight from all cells
				const cells = tableRef.current.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.remove("bg-blue-50");
				}

				// Calculate final sizes and update state
				if (resizingCol !== null) {
					const diffPx = e.clientX - startPosRef.current.x;
					const diffMm = pxToMm(diffPx);
					const initialWidth = initialSizeRef.current.width || 15;
					const newCurrentWidthMm = roundToTwoDecimals(
						Math.max(minWidthMm, initialWidth + diffMm),
					);

					// Update intrinsic (unscaled) dimensions in tableProperties
					const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
					newIntrinsicColumnWidths[resizingCol] = newCurrentWidthMm;

					onChange({
						...tableProperties,
						columnWidths: newIntrinsicColumnWidths,
						rowHeights: intrinsicRowHeights,
					});
				}

				if (resizingRow !== null) {
					const diffPx = e.clientY - startPosRef.current.y;
					const diffMm = pxToMm(diffPx);
					const initialHeight = initialSizeRef.current.height || 10;
					const newHeightMm = roundToTwoDecimals(
						Math.max(minHeightMm, initialHeight + diffMm),
					);

					// Update intrinsic (unscaled) dimensions in tableProperties
					const newIntrinsicRowHeights = [...intrinsicRowHeights];
					newIntrinsicRowHeights[resizingRow] = newHeightMm;

					onChange({
						...tableProperties,
						columnWidths: intrinsicColumnWidths,
						rowHeights: newIntrinsicRowHeights,
					});
				}

				// Remove resize indicator
				if (resizeIndicatorRef.current) {
					resizeIndicatorRef.current.remove();
					resizeIndicatorRef.current = null;
				}
			}
			setResizingCol(null);
			setResizingRow(null);
			setIsResizing(false);
			initialSizeRef.current = {};
		};

		if (resizingCol !== null || resizingRow !== null) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);
		}

		return () => {
			// Remove highlights when unmounting or cleaning up
			if (table) {
				const cells = table.getElementsByTagName("td");
				for (let i = 0; i < cells.length; i++) {
					cells[i].classList.remove("bg-blue-50");
				}
			}
			document.removeEventListener("mousemove", handleMouseMove);
			document.removeEventListener("mouseup", handleMouseUp);
		};
	}, [
		resizingCol,
		resizingRow,
		intrinsicColumnWidths,
		intrinsicRowHeights,
		onChange,
		tableProperties,
	]);

	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key === "Shift") setIsShiftPressed(true);
			
			// Handle all keyboard selection operations (including Ctrl+A and Escape)
			if (tableGrid) {
				handleKeyboardSelection(e);
			}
		};

		const handleKeyUp = (e: KeyboardEvent) => {
			if (e.key === "Shift") setIsShiftPressed(false);
		};

		window.addEventListener("keydown", handleKeyDown);
		window.addEventListener("keyup", handleKeyUp);

		return () => {
			window.removeEventListener("keydown", handleKeyDown);
			window.removeEventListener("keyup", handleKeyUp);
		};
	}, [tableGrid, enhancedSelection]);

	const handleCellSelection = (rowIndex: number, colIndex: number) => {
		if (!isShiftPressed || !selectedCells) {
			// Single cell selection
			setSelectedCells({
				start: { row: rowIndex, col: colIndex },
				end: { row: rowIndex, col: colIndex },
			});
		} else {
			// Extend selection when shift is pressed
			setSelectedCells({
				start: selectedCells.start,
				end: { row: rowIndex, col: colIndex },
			});
		}
	};

	// Keyboard selection handler for Shift+Arrow keys and Ctrl+A
	const handleKeyboardSelection = useCallback((e: KeyboardEvent) => {
		if (!tableGrid) {
			return;
		}

		// Handle Ctrl+A (Cmd+A on Mac) for select all
		if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
			e.preventDefault();
			e.stopPropagation();

			// Create select-all bounds
			const selectAllBounds = selectionManagerRef.current.selectAll(tableGrid);
			
			// Get the start and end cells for the selection
			const startCell = selectionManagerRef.current.getCellAtGridPosition(
				tableGrid,
				selectAllBounds.startRow,
				selectAllBounds.startCol
			);
			const endCell = selectionManagerRef.current.getCellAtGridPosition(
				tableGrid,
				selectAllBounds.endRow,
				selectAllBounds.endCol
			);

			if (startCell && endCell) {
				// Create the select-all selection
				const selectAllSelection: TableSelection = {
					startCell,
					endCell,
					selectionBounds: selectAllBounds,
					selectedCells: selectionManagerRef.current.collectCellsInBounds(selectAllBounds, tableGrid),
					isActive: true,
				};

				// Update enhanced selection
				setEnhancedSelection(selectAllSelection);

				// Update legacy selection for compatibility
				setSelectedCells({
					start: { row: selectAllBounds.startRow, col: selectAllBounds.startCol },
					end: { row: selectAllBounds.endRow, col: selectAllBounds.endCol },
				});
			}
			return;
		}

		// Handle Escape key to clear selection
		if (e.key === 'Escape') {
			e.preventDefault();
			e.stopPropagation();
			
			// Clear all selections
			setEnhancedSelection(null);
			setPreviewSelection(null);
			setSelectedCells(null);
			setEditingCell(null);
			return;
		}

		// Only handle Shift+Arrow keys for selection extension if we have an active selection
		if (!enhancedSelection || !enhancedSelection.isActive || !e.shiftKey) {
			return;
		}

		let direction: 'up' | 'down' | 'left' | 'right' | null = null;
		
		switch (e.key) {
			case 'ArrowUp':
				direction = 'up';
				break;
			case 'ArrowDown':
				direction = 'down';
				break;
			case 'ArrowLeft':
				direction = 'left';
				break;
			case 'ArrowRight':
				direction = 'right';
				break;
			default:
				return; // Not an arrow key, ignore
		}

		// Prevent default browser behavior
		e.preventDefault();
		e.stopPropagation();

		// Check if we can extend in this direction
		if (!selectionManagerRef.current.canExtendSelection(enhancedSelection, direction, tableGrid)) {
			return; // Can't extend further in this direction
		}

		// Extend the selection bounds
		const extendedBounds = selectionManagerRef.current.extendSelectionBounds(
			enhancedSelection.selectionBounds,
			direction,
			1, // Extend by 1 step
			tableGrid
		);

		// Create new selection with extended bounds
		const newSelection: TableSelection = {
			...enhancedSelection,
			selectionBounds: extendedBounds,
			selectedCells: selectionManagerRef.current.collectCellsInBounds(extendedBounds, tableGrid),
		};

		// Update enhanced selection
		setEnhancedSelection(newSelection);

		// Update legacy selection for compatibility
		setSelectedCells({
			start: { row: extendedBounds.startRow, col: extendedBounds.startCol },
			end: { row: extendedBounds.endRow, col: extendedBounds.endCol },
		});
	}, [tableGrid, enhancedSelection]);

	// Enhanced mouse event handlers for drag selection
	const handleEnhancedMouseDown = (
		e: React.MouseEvent,
		rowIndex: number,
		colIndex: number,
	) => {
		if (e.detail === 2) {
			handleDoubleClick(e, rowIndex, colIndex);
			return;
		}

		if (isResizing) return; // Don't start selection if we're resizing

		// If editing this cell, don't prevent default to allow text cursor positioning
		if (editingCell?.row === rowIndex && editingCell?.col === colIndex) {
			return;
		}

		// Prevent default text selection behavior
		e.preventDefault();

		// Clear editing cell if clicking a different cell
		if (
			editingCell &&
			(editingCell.row !== rowIndex || editingCell.col !== colIndex)
		) {
			setEditingCell(null);
		}

		// Use enhanced selection if table grid is available
		if (tableGrid) {
			// Ensure coordinates are within table bounds
			const constrainedRow = Math.max(0, Math.min(tableGrid.rowCount - 1, rowIndex));
			const constrainedCol = Math.max(0, Math.min(tableGrid.colCount - 1, colIndex));
			
			const cellCoord = selectionManagerRef.current.getCellAtGridPosition(
				tableGrid,
				constrainedRow,
				constrainedCol
			);

			if (cellCoord) {
				if (!isShiftPressed || !enhancedSelection) {
					// Single cell selection or start new selection
					setIsEnhancedDragging(true);
					setDragStartCell(cellCoord);

					const selection = selectionManagerRef.current.createSingleCellSelection(cellCoord);
					setEnhancedSelection(selection);
					setPreviewSelection(null);

					// Also update legacy selection for compatibility
					setSelectedCells({
						start: { row: rowIndex, col: colIndex },
						end: { row: rowIndex, col: colIndex },
					});
				} else {
					// Shift+click: extend existing selection with boundary constraints
					// Don't start dragging for Shift+click - it's a single action
					let extendedSelection = selectionManagerRef.current.createRectangularSelection(
						tableGrid,
						enhancedSelection.startCell,
						cellCoord
					);

					// Ensure selection bounds are within table limits
					const constrainedBounds = selectionManagerRef.current.constrainBoundsToTable(
						extendedSelection.selectionBounds,
						tableGrid
					);

					// Update selection with constrained bounds if needed
					if (
						constrainedBounds.startRow !== extendedSelection.selectionBounds.startRow ||
						constrainedBounds.endRow !== extendedSelection.selectionBounds.endRow ||
						constrainedBounds.startCol !== extendedSelection.selectionBounds.startCol ||
						constrainedBounds.endCol !== extendedSelection.selectionBounds.endCol
					) {
						extendedSelection = {
							...extendedSelection,
							selectionBounds: constrainedBounds,
							selectedCells: selectionManagerRef.current.collectCellsInBounds(
								constrainedBounds,
								tableGrid
							),
						};
					}
					
					// Update the end cell to reflect the actual clicked cell
					extendedSelection.endCell = cellCoord;
					
					setEnhancedSelection(extendedSelection);
					
					// Update legacy selection for compatibility
					const bounds = extendedSelection.selectionBounds;
					setSelectedCells({
						start: { row: bounds.startRow, col: bounds.startCol },
						end: { row: bounds.endRow, col: bounds.endCol },
					});
				}
			}
		} else {
			// Fallback to legacy selection
			setIsDragging(true);
			handleCellSelection(rowIndex, colIndex);
		}
	};

	const handleEnhancedMouseEnter = (rowIndex: number, colIndex: number) => {
		// Update hover state for visual feedback
		setHoveredCell({ row: rowIndex, col: colIndex });

		if (isEnhancedDragging && dragStartCell && tableGrid) {
			// Constrain coordinates to table boundaries
			const constrainedRow = Math.max(0, Math.min(tableGrid.rowCount - 1, rowIndex));
			const constrainedCol = Math.max(0, Math.min(tableGrid.colCount - 1, colIndex));

			const targetCell = selectionManagerRef.current.getCellAtGridPosition(
				tableGrid,
				constrainedRow,
				constrainedCol
			);

			if (targetCell) {
				// Create rectangular selection from start to current cell
				let selection = selectionManagerRef.current.createRectangularSelection(
					tableGrid,
					dragStartCell,
					targetCell
				);

				// Ensure selection bounds are within table limits
				const constrainedBounds = selectionManagerRef.current.constrainBoundsToTable(
					selection.selectionBounds,
					tableGrid
				);

				// Update selection with constrained bounds if needed
				if (
					constrainedBounds.startRow !== selection.selectionBounds.startRow ||
					constrainedBounds.endRow !== selection.selectionBounds.endRow ||
					constrainedBounds.startCol !== selection.selectionBounds.startCol ||
					constrainedBounds.endCol !== selection.selectionBounds.endCol
				) {
					// Recreate selection with constrained bounds
					const constrainedCells = selectionManagerRef.current.collectCellsInBounds(
						constrainedBounds,
						tableGrid
					);
					
					selection = {
						...selection,
						selectionBounds: constrainedBounds,
						selectedCells: constrainedCells,
					};
				}

				// Set as preview during drag
				setPreviewSelection(selection);

				// Update legacy selection for compatibility
				const bounds = selection.selectionBounds;
				setSelectedCells({
					start: { row: bounds.startRow, col: bounds.startCol },
					end: { row: bounds.endRow, col: bounds.endCol },
				});
			}
		} else if (isDragging && selectedCells) {
			// Fallback to legacy behavior with boundary constraints
			const constrainedRow = Math.max(0, Math.min(tableProperties.rows - 1, rowIndex));
			const constrainedCol = Math.max(0, Math.min(tableProperties.columns - 1, colIndex));
			
			setSelectedCells({
				start: selectedCells.start,
				end: { row: constrainedRow, col: constrainedCol },
			});
		}
	};

	const handleEnhancedMouseLeave = () => {
		// Clear hover state when leaving cell
		if (!isEnhancedDragging && !isDragging) {
			setHoveredCell(null);
		}
	};

	// Handle smooth boundary transitions
	const handleBoundaryTransition = useCallback((
		currentSelection: TableSelection,
		targetRow: number,
		targetCol: number
	): TableSelection => {
		if (!tableGrid) {
			return currentSelection;
		}

		// Ensure target coordinates are within bounds
		const constrainedRow = Math.max(0, Math.min(tableGrid.rowCount - 1, targetRow));
		const constrainedCol = Math.max(0, Math.min(tableGrid.colCount - 1, targetCol));

		// Get the target cell
		const targetCell = selectionManagerRef.current.getCellAtGridPosition(
			tableGrid,
			constrainedRow,
			constrainedCol
		);

		if (!targetCell) {
			return currentSelection;
		}

		// Create new selection with smooth transition
		const newSelection = selectionManagerRef.current.createRectangularSelection(
			tableGrid,
			currentSelection.startCell,
			targetCell
		);

		// Validate and constrain the selection
		const constrainedBounds = selectionManagerRef.current.constrainBoundsToTable(
			newSelection.selectionBounds,
			tableGrid
		);

		// Return selection with constrained bounds
		return {
			...newSelection,
			selectionBounds: constrainedBounds,
			selectedCells: selectionManagerRef.current.collectCellsInBounds(
				constrainedBounds,
				tableGrid
			),
		};
	}, [tableGrid]);

	const handleEnhancedMouseUp = useCallback(() => {
		if (isEnhancedDragging) {
			// Finalize the selection
			if (previewSelection) {
				setEnhancedSelection(previewSelection);
			}
			setPreviewSelection(null);
			setIsEnhancedDragging(false);
			setDragStartCell(null);
		}
		
		// Legacy cleanup
		setIsDragging(false);
	}, [isEnhancedDragging, previewSelection]);

	// Legacy handlers for backward compatibility
	const handleMouseDown = (
		e: React.MouseEvent,
		rowIndex: number,
		colIndex: number,
	) => {
		handleEnhancedMouseDown(e, rowIndex, colIndex);
	};

	const handleMouseEnter = (rowIndex: number, colIndex: number) => {
		handleEnhancedMouseEnter(rowIndex, colIndex);
	};

	const handleMouseUp = useCallback(() => {
		handleEnhancedMouseUp();
	}, [handleEnhancedMouseUp]);

	// Global mouse move handler for drag operations outside table
	const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
		if (!isEnhancedDragging || !dragStartCell || !tableGrid || !tableRef.current) {
			return;
		}

		// Get the table bounds
		const tableRect = tableRef.current.getBoundingClientRect();
		
		// Check if mouse is outside table bounds
		const isOutsideTable = (
			e.clientX < tableRect.left ||
			e.clientX > tableRect.right ||
			e.clientY < tableRect.top ||
			e.clientY > tableRect.bottom
		);

		if (isOutsideTable) {
			// Find the closest edge cell
			let targetRow: number;
			let targetCol: number;

			// Constrain to table boundaries
			if (e.clientY < tableRect.top) {
				targetRow = 0;
			} else if (e.clientY > tableRect.bottom) {
				targetRow = tableGrid.rowCount - 1;
			} else {
				// Find row based on Y position
				const relativeY = e.clientY - tableRect.top;
				const rowHeight = tableRect.height / tableGrid.rowCount;
				targetRow = Math.floor(relativeY / rowHeight);
				targetRow = Math.max(0, Math.min(tableGrid.rowCount - 1, targetRow));
			}

			if (e.clientX < tableRect.left) {
				targetCol = 0;
			} else if (e.clientX > tableRect.right) {
				targetCol = tableGrid.colCount - 1;
			} else {
				// Find column based on X position
				const relativeX = e.clientX - tableRect.left;
				const colWidth = tableRect.width / tableGrid.colCount;
				targetCol = Math.floor(relativeX / colWidth);
				targetCol = Math.max(0, Math.min(tableGrid.colCount - 1, targetCol));
			}

			// Update selection to the edge cell
			const targetCell = selectionManagerRef.current.getCellAtGridPosition(
				tableGrid,
				targetRow,
				targetCol
			);

			if (targetCell) {
				const selection = selectionManagerRef.current.createRectangularSelection(
					tableGrid,
					dragStartCell,
					targetCell
				);

				setPreviewSelection(selection);

				// Update legacy selection for compatibility
				const bounds = selection.selectionBounds;
				setSelectedCells({
					start: { row: bounds.startRow, col: bounds.startCol },
					end: { row: bounds.endRow, col: bounds.endCol },
				});
			}
		}
	}, [isEnhancedDragging, dragStartCell, tableGrid]);

	useEffect(() => {
		if (isDragging || isEnhancedDragging) {
			window.addEventListener("mouseup", handleMouseUp);
			
			// Add global mouse move handler for enhanced dragging
			if (isEnhancedDragging) {
				window.addEventListener("mousemove", handleGlobalMouseMove);
			}
			
			return () => {
				window.removeEventListener("mouseup", handleMouseUp);
				window.removeEventListener("mousemove", handleGlobalMouseMove);
			};
		}
	}, [isDragging, isEnhancedDragging, handleMouseUp, handleGlobalMouseMove]);

	const isSingleCellSelected =
		selectedCells &&
		selectedCells.start.row === selectedCells.end.row &&
		selectedCells.start.col === selectedCells.end.col;

	const addColumn = () => {
		if (!tableProperties.cells || tableProperties.cells.length === 0) return;

		const targetIndex = selectedCells
			? selectedCells.end.col
			: tableProperties.columns - 1; // logical index

		const defaultCell = {
			content: "",
			colspan: 1,
			rowspan: 1,
			borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			backgroundColor: "transparent",
			verticalAlign: "top" as const,
		} as TableCell;

		const updatedCells = tableProperties.cells.map((row) => {
			let colCursor = 0;
			let inserted = false;
			const newRow: TableCell[] = [];

			for (const cell of row) {
				const start = colCursor;
				const end = colCursor + cell.colspan - 1;

				if (!inserted && targetIndex < start) {
					// Insert before current block (should not really happen)
					newRow.push({ ...defaultCell });
					inserted = true;
				}

				if (targetIndex >= start && targetIndex <= end) {
					// Insertion point lies within this cell's span
					newRow.push({ ...cell, colspan: cell.colspan + 1 });
					inserted = true;
				} else {
					newRow.push(cell);
					// If insertion is right after this cell's end, place new cell
					if (!inserted && end === targetIndex) {
						newRow.push({ ...defaultCell });
						inserted = true;
					}
				}
				colCursor += cell.colspan;
			}

			if (!inserted) {
				newRow.push({ ...defaultCell }); // append at the end
			}

			return newRow;
		});

		// Update intrinsic column widths (15mm default for new column)
		const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
		newIntrinsicColumnWidths.splice(targetIndex + 1, 0, 15);

		onChange({
			...tableProperties,
			cells: updatedCells,
			columnWidths: newIntrinsicColumnWidths,
			columns: tableProperties.columns + 1,
		});
	};

	const addRow = () => {
		if (!tableProperties.cells || tableProperties.cells.length === 0) return;

		const targetIndex = selectedCells
			? selectedCells.end.row
			: tableProperties.rows - 1;

		const newRowIdx = targetIndex + 1;
		const columns = tableProperties.columns;

		const defaultCell = {
			content: "",
			colspan: 1,
			rowspan: 1,
			borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			backgroundColor: "transparent",
			verticalAlign: "top" as const,
		} as TableCell;

		// Deep copy cells
		const newCells = JSON.parse(
			JSON.stringify(tableProperties.cells),
		) as TableCell[][];

		// Track columns covered by existing rowspans that reach newRowIdx
		const coveredColumns = new Set<number>();

		for (let r = 0; r < newRowIdx; r++) {
			let colCursor = 0;
			for (const cell of newCells[r]) {
				if (r + cell.rowspan - 1 >= newRowIdx) {
					for (let c = colCursor; c < colCursor + cell.colspan; c++) {
						coveredColumns.add(c);
					}
					cell.rowspan += 1; // extend the cell downward
				}
				colCursor += cell.colspan;
			}
		}

		// Build the new row taking into account covered columns
		const newRow: TableCell[] = [];
		for (let c = 0; c < columns; c++) {
			if (coveredColumns.has(c)) continue; // no cell because covered by rowspan
			newRow.push({ ...defaultCell });
		}

		newCells.splice(newRowIdx, 0, newRow);

		const newIntrinsicRowHeights = [...intrinsicRowHeights];
		newIntrinsicRowHeights.splice(newRowIdx, 0, 10); // default 10mm height

		onChange({
			...tableProperties,
			cells: newCells,
			rowHeights: newIntrinsicRowHeights,
			rows: tableProperties.rows + 1,
		});
	};

	const deleteColumn = () => {
		if (!isSingleCellSelected || !tableProperties.cells) return;
		if (tableProperties.columns <= 1) return;
		const targetIndex = selectedCells?.end.col;
		if (targetIndex === undefined) return;

		const newIntrinsicColumnWidths = [...intrinsicColumnWidths];
		newIntrinsicColumnWidths.splice(targetIndex, 1);

		const updatedCells = tableProperties.cells.map((row) => {
			let colCursor = 0;
			const newRow: TableCell[] = [];
			for (const cell of row) {
				const start = colCursor;
				const end = colCursor + cell.colspan - 1;

				if (targetIndex >= start && targetIndex <= end) {
					// Column falls within this cell's span
					if (cell.colspan > 1) {
						newRow.push({ ...cell, colspan: cell.colspan - 1 });
					}
					// If colspan ==1, effectively remove the cell
				} else {
					newRow.push(cell);
				}
				colCursor += cell.colspan;
			}
			return newRow;
		});

		onChange({
			...tableProperties,
			cells: updatedCells,
			columnWidths: newIntrinsicColumnWidths,
			columns: tableProperties.columns - 1,
		});

		setSelectedCells(null);
	};

	const deleteRow = () => {
		if (!isSingleCellSelected || !tableProperties.cells) return;
		if (tableProperties.rows <= 1) return;
		const targetRowIdx = selectedCells?.end.row;
		if (targetRowIdx === undefined) return;

		const newCells = JSON.parse(
			JSON.stringify(tableProperties.cells),
		) as TableCell[][];

		// Adjust rowspan for cells above the deleted row
		for (let r = 0; r < targetRowIdx; r++) {
			for (const cell of newCells[r]) {
				if (r + cell.rowspan - 1 >= targetRowIdx) {
					cell.rowspan -= 1;
				}
			}
		}

		// Remove the row itself
		newCells.splice(targetRowIdx, 1);

		// Build new row heights
		const newIntrinsicRowHeights = [...intrinsicRowHeights];
		newIntrinsicRowHeights.splice(targetRowIdx, 1);

		onChange({
			...tableProperties,
			cells: newCells,
			rowHeights: newIntrinsicRowHeights,
			rows: tableProperties.rows - 1,
		});

		setSelectedCells(null);
	};

	const handleDoubleClick = (
		e: React.MouseEvent,
		rowIndex: number,
		colIndex: number,
	) => {
		e.stopPropagation();
		if (isResizing) return;

		// Clear any existing editing cell before setting new one
		if (
			editingCell &&
			(editingCell.row !== rowIndex || editingCell.col !== colIndex)
		) {
			setEditingCell(null);
		}

		setEditingCell({ row: rowIndex, col: colIndex });

		// Focus the RichTextEditor directly
		// If we start editing, treat it as a single cell selection for properties tab
		const newSelection = {
			start: { row: rowIndex, col: colIndex },
			end: { row: rowIndex, col: colIndex },
		};
		setSelectedCells(newSelection);
		// onChange will be triggered by useEffect listening to selectedCells

		setTimeout(() => {
			const editorElement = document.querySelector(".ProseMirror");
			if (editorElement) {
				(editorElement as HTMLElement).focus();
			}
		}, 0);
	};

	const handleCellContentChange = (content: string) => {
		if (!editingCell) return;

		const newCells = [...tableProperties.cells];

		// Extract vertical alignment from content if present
		let verticalAlign: "top" | "middle" | "bottom" | undefined;
		const styleMatch = content.match(/vertical-align:\s*(top|middle|bottom)/);
		const classMatch = content.match(/vertical-align-(top|middle|bottom)/);

		if (styleMatch) {
			verticalAlign = styleMatch[1] as "top" | "middle" | "bottom";
		} else if (classMatch) {
			verticalAlign = classMatch[1] as "top" | "middle" | "bottom";
		}

		newCells[editingCell.row][editingCell.col] = {
			...newCells[editingCell.row][editingCell.col],
			content,
			verticalAlign:
				verticalAlign ||
				newCells[editingCell.row][editingCell.col].verticalAlign ||
				"top",
		};

		onChange({
			...tableProperties,
			cells: newCells,
		});
	};

	// Define the cell rendering function for edit mode
	const renderEditableCell = (
		cell: TableCell,
		rowIndex: number,
		colIndex: number,
	): React.ReactNode => {
		// Check enhanced selection first, then fall back to legacy selection
		let isSelected = false;
		let isPreview = false;
		let isHover = false;

		if (enhancedSelection && tableGrid) {
			isSelected = selectionManagerRef.current.isCellInSelectionBounds(
				rowIndex,
				colIndex,
				enhancedSelection
			);
		}

		if (previewSelection && tableGrid) {
			isPreview = selectionManagerRef.current.isCellInSelectionBounds(
				rowIndex,
				colIndex,
				previewSelection
			);
		}

		// Check hover state
		if (hoveredCell && hoveredCell.row === rowIndex && hoveredCell.col === colIndex) {
			isHover = true;
		}

		// Fallback to legacy selection if enhanced selection is not available
		if (!enhancedSelection && !previewSelection && selectedCells) {
			isSelected =
				rowIndex >= Math.min(selectedCells.start.row, selectedCells.end.row) &&
				rowIndex <= Math.max(selectedCells.start.row, selectedCells.end.row) &&
				colIndex >= Math.min(selectedCells.start.col, selectedCells.end.col) &&
				colIndex <= Math.max(selectedCells.start.col, selectedCells.end.col);
		}

		// Check if this is a merged cell
		const isMerged = cell.colspan > 1 || cell.rowspan > 1;

		const isEditing =
			editingCell?.row === rowIndex && editingCell?.col === colIndex;

		// Determine background color:
		const cellOwnBackgroundColor = cell.backgroundColor
			? cell.backgroundColor
			: "transparent";

		// Compute logical column start index (accounts for previous colspans)
		const logicalColStart = tableProperties.cells[rowIndex]
			.slice(0, colIndex)
			.reduce((sum, c) => sum + c.colspan, 0);
		const spannedWidthMm = columnWidths
			.slice(logicalColStart, logicalColStart + cell.colspan)
			.reduce((sum, w) => sum + (w || 0), 0);
		const cellWidthStyle =
			spannedWidthMm > 0 ? `${spannedWidthMm}mm` : undefined;

		// Border styling
		const borderStyle = tableProperties.borderStyle || "solid";
		const globalTableBorderColor = tableProperties.borderColor || "black";

		// Top Border
		const topWidth = cell.borderSettings?.top?.width ?? cell.borderWidths.top;
		const topColor = cell.borderSettings?.top?.color || globalTableBorderColor;
		const borderTop = `${topWidth}px ${borderStyle} ${topColor}`;

		// Right Border
		const rightWidth =
			cell.borderSettings?.right?.width ?? cell.borderWidths.right;
		const rightColor =
			cell.borderSettings?.right?.color || globalTableBorderColor;
		const borderRight = `${rightWidth}px ${borderStyle} ${rightColor}`;

		// Bottom Border
		const bottomWidth =
			cell.borderSettings?.bottom?.width ?? cell.borderWidths.bottom;
		const bottomColor =
			cell.borderSettings?.bottom?.color || globalTableBorderColor;
		const borderBottom = `${bottomWidth}px ${borderStyle} ${bottomColor}`;

		// Left Border
		const leftWidth =
			cell.borderSettings?.left?.width ?? cell.borderWidths.left;
		const leftColor =
			cell.borderSettings?.left?.color || globalTableBorderColor;
		const borderLeft = `${leftWidth}px ${borderStyle} ${leftColor}`;

		return (
			<td
				key={`${rowIndex}-${colIndex}`}
				colSpan={cell.colspan}
				rowSpan={cell.rowspan}
				onMouseDown={(e) => {
					// Don't handle mousedown if we're editing this cell
					if (isEditing) {
						return;
					}
					handleMouseDown(e, rowIndex, colIndex);
				}}
				onMouseEnter={() => {
					// Don't handle mouse enter if we're editing
					if (editingCell) {
						return;
					}
					handleMouseEnter(rowIndex, colIndex);
				}}
				onMouseLeave={() => {
					// Don't handle mouse leave if we're editing
					if (editingCell) {
						return;
					}
					handleEnhancedMouseLeave();
				}}
				style={{
					// Replace the single 'border' style with individual border styles
					borderTop: borderTop,
					borderRight: borderRight,
					borderBottom: borderBottom,
					borderLeft: borderLeft,
					padding: `2px`,
					backgroundColor: cellOwnBackgroundColor, // Apply cell's own background color
					position: "relative",
					minWidth: "5px",
					width: cellWidthStyle || `15mm`, // Sum of spanned column widths
					// Height for cells spanning multiple rows
					height: (() => {
						const h = rowHeights
							.slice(rowIndex, rowIndex + cell.rowspan)
							.reduce((sum, v) => sum + (v || 0), 0);
						return `${h || 10}mm`;
					})(),
					// Max height for cells spanning multiple rows
					maxHeight: (() => {
						const h = rowHeights
							.slice(rowIndex, rowIndex + cell.rowspan)
							.reduce((sum, v) => sum + (v || 0), 0);
						return `${h || 10}mm`;
					})(),
					boxSizing: "border-box",
					wordBreak: "break-word",
					overflowWrap: "break-word",
					whiteSpace: "pre-wrap",
					verticalAlign: cell.verticalAlign || "top",
					overflow: "hidden",
				}}
			>
				{/* Wrapper for content to ensure it's above the selection overlay */}
				<div
					style={{
						position: "relative",
						zIndex: 2,
						height: "100%",
						maxHeight: "100%",
						overflow: "hidden",
						userSelect: isEditing ? "text" : (isDragging || isEnhancedDragging) ? "none" : "auto",
						WebkitUserSelect: isEditing ? "text" : (isDragging || isEnhancedDragging) ? "none" : "auto",
						MozUserSelect: isEditing ? "text" : (isDragging || isEnhancedDragging) ? "none" : "auto",
						msUserSelect: isEditing ? "text" : (isDragging || isEnhancedDragging) ? "none" : undefined,
						pointerEvents: isEditing ? "auto" : "none", // Allow clicks to pass through when not editing
						display: "flex",
						flexDirection: "column",
						justifyContent:
							cell.verticalAlign === "middle"
								? "center"
								: cell.verticalAlign === "bottom"
									? "flex-end"
									: "flex-start",
					}}
				>
					{isEditing ? (
						<div
							style={{
								height: "100%",
								maxHeight: "100%",
								overflow: "hidden",
								position: "relative",
								zIndex: 3, // Ensure editor is above other elements
							}}
						>
							<RichTextEditor
								content={cell.content}
								onChange={handleCellContentChange}
								setActiveEditor={setActiveEditor}
								verticalAlign={cell.verticalAlign || "top"}
								setIsTextEditorFocused={setIsTextEditorFocused}
							/>
						</div>
					) : (
						<div
							className="max-w-none w-full"
							style={{
								fontFamily: "'NeoSansforeprimo-Regular', sans-serif",
								whiteSpace: "pre-wrap",
								wordBreak: "break-word",
								overflowWrap: "break-word",
								overflow: "hidden",
								textOverflow: "ellipsis",
								lineHeight: "1.2",
								maxHeight: "100%",
								pointerEvents: "none", // Always allow clicks to pass through to the cell
								height: "100%",
								display: "flex",
								flexDirection: "column",
								justifyContent:
									cell.verticalAlign === "middle"
										? "center"
										: cell.verticalAlign === "bottom"
											? "flex-end"
											: "flex-start",
							}}
							data-element-content
						>
							<div
								// biome-ignore lint/security/noDangerouslySetInnerHtml: is needed
								dangerouslySetInnerHTML={{
									__html: processContentWithVariables({
										content: cell.content || "",
										isEditing: false,
										highlightVariables, // Disable highlighting when cell is selected
										selectedTestDataIndex,
										testData,
									}),
								}}
							/>
						</div>
					)}
				</div>

				{/* Enhanced Selection Indicator - hide when editing */}
				{!isEditing && (
					<TableCellSelectionIndicator
						isSelected={isSelected}
						isPreview={isPreview}
						isHover={isHover}
						isMerged={isMerged}
					/>
				)}

				{!isDragging && !isEnhancedDragging && !isEditing && (
					<div
						className="absolute top-0 right-0 w-1 cursor-col-resize"
						role="button"
						tabIndex={0}
						onMouseDown={(e) => handleResizeStart(e, colIndex, "col")}
						style={{
							zIndex: 2,
							height: `${tableRef.current?.offsetHeight || 0}px`,
							background: "transparent",
						}}
					/>
				)}
				{!isDragging && !isEnhancedDragging && !isEditing && (
					<div
						className="absolute bottom-0 left-0 h-1 cursor-row-resize"
						role="button"
						tabIndex={0}
						onMouseDown={(e) => handleResizeStart(e, rowIndex, "row")}
						style={{
							zIndex: 2,
							width: `${tableRef.current?.offsetWidth || 0}px`,
							background: "transparent",
						}}
					/>
				)}
			</td>
		);
	};

	const renderAddButtons = () => {
		if (isSingleCellSelected && tableRef.current) {
			// Find the actual cell element by iterating through rows
			let targetCell: HTMLTableCellElement | null = null;
			const rows = tableRef.current.getElementsByTagName("tr");
			if (rows[selectedCells?.end.row]) {
				const cells = rows[selectedCells?.end.row].getElementsByTagName("td");
				// We need to find which actual cell index corresponds to our logical column
				let logicalCol = 0;

				for (let i = 0; i < cells.length; i++) {
					if (logicalCol === selectedCells?.end.col) {
						targetCell = cells[i];
						break;
					}
					const colspan = parseInt(cells[i].getAttribute("colspan") || "1");
					logicalCol += colspan;
				}
			}

			if (!targetCell) return null;

			return (
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="absolute hover:bg-slate-100 z-10"
								style={{
									top: `${targetCell.offsetTop}px`,
									left: `${targetCell.offsetLeft + targetCell.offsetWidth}px`,
								}}
								onClick={(e) => {
									e.preventDefault();
									e.stopPropagation();
									addColumn();
								}}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Spalte hinzufügen</TooltipContent>
					</Tooltip>

					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="absolute hover:bg-slate-100 z-10"
								style={{
									top: `${targetCell.offsetTop + targetCell.offsetHeight}px`,
									left: `${targetCell.offsetLeft}px`,
								}}
								onClick={(e) => {
									e.preventDefault();
									e.stopPropagation();
									addRow();
								}}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Zeile hinzufügen</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			);
		}

		// Show buttons at the end when no cell is selected
		const lastRowIndex = tableProperties.cells.length - 1;
		const lastColIndex = tableProperties.cells[0].length - 1;

		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-slate-100"
							style={{
								top: `${tableRef.current?.rows[0]?.offsetTop || 0}px`,
								left: `${
									(tableRef.current?.rows[0]?.cells[lastColIndex]?.offsetLeft ||
										0) +
									(tableRef.current?.rows[0]?.cells[lastColIndex]
										?.offsetWidth || 0)
								}px`,
							}}
							onClick={addColumn}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Spalte hinzufügen</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-slate-100"
							style={{
								top: `${
									(tableRef.current?.rows[lastRowIndex]?.offsetTop || 0) +
									(tableRef.current?.rows[lastRowIndex]?.offsetHeight || 0)
								}px`,
								left: "0px",
							}}
							onClick={addRow}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zeile hinzufügen</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	};

	const highlightRow = (rowIndex: number, highlight: boolean) => {
		if (!tableRef.current) return;
		const row = tableRef.current.getElementsByTagName("tr")[rowIndex];
		if (row) {
			const cells = row.getElementsByTagName("td");
			for (let i = 0; i < cells.length; i++) {
				if (highlight) {
					// Store original background in a data attribute
					cells[i].setAttribute(
						"data-original-bg",
						cells[i].style.backgroundColor || "",
					);
					// Apply red overlay using a semi-transparent div
					const overlay = document.createElement("div");
					overlay.className = "delete-highlight-overlay";
					cells[i].style.position = "relative";
					cells[i].appendChild(overlay);
				} else {
					// Remove overlay
					const overlay = cells[i].querySelector(".delete-highlight-overlay");
					if (overlay) {
						overlay.remove();
					}
				}
			}
		}
	};

	const highlightColumn = (colIndex: number, highlight: boolean) => {
		if (!tableRef.current) return;
		const rows = tableRef.current.getElementsByTagName("tr");
		for (let i = 0; i < rows.length; i++) {
			const cells = rows[i].getElementsByTagName("td");
			let logicalCol = 0;
			for (let j = 0; j < cells.length; j++) {
				if (logicalCol === colIndex) {
					if (highlight) {
						// Store original background in a data attribute
						cells[j].setAttribute(
							"data-original-bg",
							cells[j].style.backgroundColor || "",
						);
						// Apply red overlay using a semi-transparent div
						const overlay = document.createElement("div");
						overlay.className = "delete-highlight-overlay";
						cells[j].style.position = "relative";
						cells[j].appendChild(overlay);
					} else {
						// Remove overlay
						const overlay = cells[j].querySelector(".delete-highlight-overlay");
						if (overlay) {
							overlay.remove();
						}
					}
					break;
				}
				const colspan = parseInt(cells[j].getAttribute("colspan") || "1");
				logicalCol += colspan;
			}
		}
	};

	const renderDeleteButtons = () => {
		if (!isSingleCellSelected || !tableRef.current) return null;

		// Find the actual cell element
		let targetCell: HTMLTableCellElement | null = null;
		const rows = tableRef.current.getElementsByTagName("tr");
		if (rows[selectedCells?.end.row]) {
			const cells = rows[selectedCells?.end.row].getElementsByTagName("td");
			let logicalCol = 0;

			for (let i = 0; i < cells.length; i++) {
				if (logicalCol === selectedCells?.end.col) {
					targetCell = cells[i];
					break;
				}
				const colspan = parseInt(cells[i].getAttribute("colspan") || "1");
				logicalCol += colspan;
			}
		}

		if (!targetCell) return null;

		return (
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-red-100 z-20"
							style={{
								top: `${targetCell.offsetTop}px`,
								left: `-45px`, // Position to the left of the table
							}}
							onMouseEnter={() => highlightRow(selectedCells?.end.row, true)}
							onMouseLeave={() => highlightRow(selectedCells?.end.row, false)}
							onMouseDown={(e) => {
								console.log("Delete row button clicked");
								e.preventDefault();
								e.stopPropagation();
							}}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								deleteRow();
							}}
						>
							<Trash2 className="h-4 w-4 text-red-500" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zeile löschen</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="outline"
							size="icon"
							className="absolute hover:bg-red-100 z-20"
							style={{
								top: `-45px`, // Position above the table
								left: `${targetCell.offsetLeft}px`,
							}}
							onMouseEnter={() => highlightColumn(selectedCells?.end.col, true)}
							onMouseLeave={() =>
								highlightColumn(selectedCells?.end.col, false)
							}
							onMouseDown={(e) => {
								console.log("Delete column button clicked");
								e.preventDefault();
								e.stopPropagation();
							}}
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								deleteColumn();
							}}
						>
							<Trash2 className="h-4 w-4 text-red-500" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Spalte löschen</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		);
	};

	// Clean up highlights when selection changes
	useEffect(() => {
		if (!selectedCells && tableRef.current) {
			// Clear all red highlights when no selection
			const cells = tableRef.current.getElementsByTagName("td");
			for (let i = 0; i < cells.length; i++) {
				cells[i].classList.remove("bg-red-50");
				// Remove any delete highlight overlays
				const overlay = cells[i].querySelector(".delete-highlight-overlay");
				if (overlay) {
					overlay.remove();
				}
			}
		}
	}, [selectedCells]);

	useEffect(() => {
		const handleClickOutsideEvent = (e: MouseEvent) => {
			const target = e.target as HTMLElement;

			// Check if click is within container OR on a delete button (which might be outside due to absolute positioning)
			const isClickOnTableEditor =
				containerRef.current?.contains(target) ||
				target.closest("button")?.closest('[style*="absolute"]');

			// Check for interactions with UI elements that should not cause deselection.
			// This includes the format toolbar itself, and Radix-based popovers/dialogs
			// (used by ShadCN/UI) which are often portalled.
			const isToolbarOrPopupInteraction =
				!!target.closest(".format-toolbar") || // Sidebar toolbar itself
				!!target.closest("[data-radix-popper-content-wrapper]") || // General Radix popper content (covers Popover, Tooltip, Select)
				!!target.closest('[role="dialog"]'); // General dialogs, often used by Radix for modal content

			// If currently editing a cell
			if (editingCell) {
				const editorWasClicked =
					isClickOnTableEditor && target.closest(".ProseMirror");
				if (!editorWasClicked && !isToolbarOrPopupInteraction) {
					setEditingCell(null); // Stop editing if click is outside editor and not on a related popup/toolbar
				}
			}

			// If click is NOT on the table editor AND NOT on the toolbar/popup, then clear selection.
			if (!isClickOnTableEditor && !isToolbarOrPopupInteraction) {
				if (selectedCells) {
					// Only clear selection if one exists
					setSelectedCells(null);
				}
			}
			// Optional: Handle clicks inside the table editor's div but not on a cell or known interactive element.
			// else if (isClickOnTableEditor &&
			//          !target.closest('td') &&
			//          !target.closest('button') && // Catches add/delete buttons
			//          !target.closest('[class*="resize"]')) { // Catches resize handles
			//     // This means a click on the table's padding area for example.
			//     // Clearing selection here might be too aggressive, depending on desired UX.
			//     // For now, we only ensure editing stops (handled above).
			// }
		};

		document.addEventListener("mousedown", handleClickOutsideEvent);
		return () =>
			document.removeEventListener("mousedown", handleClickOutsideEvent);
	}, [editingCell, selectedCells]); // Dependencies are correct

	// --- Keyboard navigation helpers ---
	// Focus the container whenever we enter the editor so that key events are captured
	useEffect(() => {
		if (containerRef.current) {
			containerRef.current.tabIndex = 0;
			// Auto-focus when editor mounts (but only once)
			containerRef.current.focus();
		}
	}, []);

	// Handle key navigation, editing and escape
	useEffect(() => {
		function handleKeyboardNavigation(e: KeyboardEvent) {
			// When a rich-text editor inside a cell is active we only react to Escape
			if (editingCell) {
				if (e.key === "Escape") {
					e.preventDefault();
					setEditingCell(null);
					// After leaving edit mode, refocus the container so that further
					// navigation keys are caught again
					setTimeout(() => {
						containerRef.current?.focus();
					}, 0);
				}
				return; // Do not process other keys while editing a cell
			}

			// If no selection yet, ignore all keys except maybe enter
			if (!selectedCells) {
				return;
			}

			const maxRow = tableProperties.rows - 1;
			const maxCol = tableProperties.columns - 1;

			const curRow = selectedCells.end.row;
			const curCol = selectedCells.end.col;

			let nextRow = curRow;
			let nextCol = curCol;

			let handled = false;

			if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
				e.preventDefault();
				handled = true;
				if (e.key === "ArrowUp") nextRow = Math.max(0, curRow - 1);
				if (e.key === "ArrowDown") nextRow = Math.min(maxRow, curRow + 1);
				if (e.key === "ArrowLeft") nextCol = Math.max(0, curCol - 1);
				if (e.key === "ArrowRight") nextCol = Math.min(maxCol, curCol + 1);

				if (e.shiftKey) {
					// Extend the selection keeping the original start
					setSelectedCells((prev) =>
						prev
							? { start: prev.start, end: { row: nextRow, col: nextCol } }
							: {
									start: { row: curRow, col: curCol },
									end: { row: nextRow, col: nextCol },
								},
					);
				} else {
					// Move selection
					setSelectedCells({
						start: { row: nextRow, col: nextCol },
						end: { row: nextRow, col: nextCol },
					});
				}
			}

			if (e.key === "Enter" && isSingleCellSelected) {
				e.preventDefault();
				handled = true;
				// Start editing the focused cell
				setEditingCell({
					row: selectedCells.start.row,
					col: selectedCells.start.col,
				});
				setTimeout(() => {
					const editorElement = document.querySelector(".ProseMirror");
					if (editorElement) {
						(editorElement as HTMLElement).focus();
					}
				}, 0);
			}

			if (!handled && e.key === "Escape") {
				e.preventDefault();
				setSelectedCells(null);
			}
		}

		window.addEventListener("keydown", handleKeyboardNavigation);
		return () =>
			window.removeEventListener("keydown", handleKeyboardNavigation);
	}, [
		editingCell,
		selectedCells,
		isSingleCellSelected,
		tableProperties.rows,
		tableProperties.columns,
	]);

	return (
		<div
			ref={containerRef}
			className={`relative w-full h-full ${(isDragging || isEnhancedDragging) ? "select-none" : ""}`}
		>
			<table
				ref={tableRef}
				className="border-collapse table-fixed w-full h-full"
			>
				<tbody>
					{/* Use the shared helper to render the table body content */}
					<TableBodyContent
						tableProperties={tableProperties}
						rowHeightsMm={rowHeights}
						columnWidthsMm={columnWidths}
						renderCell={renderEditableCell}
					/>
				</tbody>
			</table>

			{!editingCell &&
				(!selectedCells || isSingleCellSelected) &&
				renderAddButtons()}
			{!editingCell && renderDeleteButtons()}
			
			{/* Enhanced Selection Overlay */}
			<TableSelectionOverlay
				selection={enhancedSelection}
				isPreview={false}
				containerRef={containerRef}
				tableRef={tableRef}
			/>
			
			{/* Preview Selection Overlay */}
			<TableSelectionOverlay
				selection={previewSelection}
				isPreview={true}
				containerRef={containerRef}
				tableRef={tableRef}
			/>
		</div>
	);
}
