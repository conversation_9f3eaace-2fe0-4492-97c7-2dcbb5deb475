import React from 'react';
import type { TableSelection } from '@/types/tableSelection';

interface TableSelectionOverlayProps {
  selection: TableSelection | null;
  isPreview?: boolean;
  containerRef: React.RefObject<HTMLDivElement>;
  tableRef: React.RefObject<HTMLTableElement>;
}

export function TableSelectionOverlay({
  selection,
  isPreview = false,
  containerRef,
  tableRef,
}: TableSelectionOverlayProps) {
  if (!selection || !selection.isActive || selection.selectedCells.size === 0) {
    return null;
  }

  // Calculate the visual bounds of the selection
  const getSelectionBounds = (): DOMRect | null => {
    if (!containerRef.current || !tableRef.current) {
      return null;
    }

    let minLeft = Number.POSITIVE_INFINITY;
    let minTop = Number.POSITIVE_INFINITY;
    let maxRight = Number.NEGATIVE_INFINITY;
    let maxBottom = Number.NEGATIVE_INFINITY;

    // Get bounds from all selected cells
    for (const cell of selection.selectedCells) {
      const rect = cell.element.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();
      
      // Convert to container-relative coordinates
      const relativeLeft = rect.left - containerRect.left;
      const relativeTop = rect.top - containerRect.top;
      const relativeRight = rect.right - containerRect.left;
      const relativeBottom = rect.bottom - containerRect.top;

      minLeft = Math.min(minLeft, relativeLeft);
      minTop = Math.min(minTop, relativeTop);
      maxRight = Math.max(maxRight, relativeRight);
      maxBottom = Math.max(maxBottom, relativeBottom);
    }

    if (minLeft === Number.POSITIVE_INFINITY) {
      return null;
    }

    return new DOMRect(
      minLeft,
      minTop,
      maxRight - minLeft,
      maxBottom - minTop
    );
  };

  const bounds = getSelectionBounds();
  
  if (!bounds) {
    return null;
  }

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${bounds.left}px`,
    top: `${bounds.top}px`,
    width: `${bounds.width}px`,
    height: `${bounds.height}px`,
    pointerEvents: 'none',
    zIndex: 10,
    border: isPreview 
      ? '2px dashed rgba(0, 120, 212, 0.6)' 
      : '2px solid rgba(0, 120, 212, 0.8)',
    backgroundColor: isPreview 
      ? 'rgba(0, 120, 212, 0.05)' 
      : 'rgba(0, 120, 212, 0.1)',
    boxShadow: isPreview 
      ? '0 0 4px rgba(0, 120, 212, 0.3)' 
      : '0 0 8px rgba(0, 120, 212, 0.4)',
  };

  return (
    <div 
      className={`table-selection-bounds ${isPreview ? 'preview' : ''}`}
      style={overlayStyle}
    >
      {/* Selection corners for visual feedback */}
      {!isPreview && (
        <>
          <div className="table-selection-corner top-left" />
          <div className="table-selection-corner top-right" />
          <div className="table-selection-corner bottom-left" />
          <div className="table-selection-corner bottom-right" />
        </>
      )}
    </div>
  );
}

interface TableCellSelectionIndicatorProps {
  isSelected: boolean;
  isPreview: boolean;
  isHover?: boolean;
  isMerged?: boolean;
}

export function TableCellSelectionIndicator({
  isSelected,
  isPreview,
  isHover = false,
  isMerged = false,
}: TableCellSelectionIndicatorProps) {
  if (!isSelected && !isPreview && !isHover) {
    return null;
  }

  let className = 'table-selection-overlay';
  
  if (isPreview) {
    className += ' preview';
  }
  
  if (isMerged) {
    className += isSelected ? ' merged-selected' : ' merged-preview';
  }

  const style: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
    zIndex: 1,
  };

  if (isHover && !isSelected && !isPreview) {
    style.backgroundColor = 'rgba(0, 120, 212, 0.1)';
    style.border = '1px solid rgba(0, 120, 212, 0.3)';
  } else if (isPreview) {
    style.backgroundColor = 'rgba(0, 120, 212, 0.15)';
    style.border = '2px dashed rgba(0, 120, 212, 0.6)';
    style.boxShadow = 'inset 0 0 0 1px rgba(0, 120, 212, 0.3)';
  } else if (isSelected) {
    style.backgroundColor = 'rgba(0, 120, 212, 0.2)';
    style.border = '2px solid rgba(0, 120, 212, 0.8)';
    style.boxShadow = 'inset 0 0 0 1px rgba(0, 120, 212, 0.4)';
  }

  return <div className={className} style={style} />;
}

interface TableDragSelectionOverlayProps {
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
  containerRef: React.RefObject<HTMLDivElement>;
}

export function TableDragSelectionOverlay({
  startX,
  startY,
  currentX,
  currentY,
  containerRef,
}: TableDragSelectionOverlayProps) {
  if (!containerRef.current) {
    return null;
  }

  const containerRect = containerRef.current.getBoundingClientRect();
  
  // Convert to container-relative coordinates
  const relativeStartX = startX - containerRect.left;
  const relativeStartY = startY - containerRect.top;
  const relativeCurrentX = currentX - containerRect.left;
  const relativeCurrentY = currentY - containerRect.top;

  const left = Math.min(relativeStartX, relativeCurrentX);
  const top = Math.min(relativeStartY, relativeCurrentY);
  const width = Math.abs(relativeCurrentX - relativeStartX);
  const height = Math.abs(relativeCurrentY - relativeStartY);

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    pointerEvents: 'none',
    zIndex: 15,
    border: '1px solid rgba(0, 120, 212, 0.8)',
    backgroundColor: 'rgba(0, 120, 212, 0.1)',
    opacity: 0.8,
  };

  return <div className="table-drag-selection" style={overlayStyle} />;
}