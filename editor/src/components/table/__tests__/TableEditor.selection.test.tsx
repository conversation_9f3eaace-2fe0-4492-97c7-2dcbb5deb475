import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";
import { TableEditor } from "@/components/table/TableEditor";
import type { TableProperties } from "@/types/table";

describe("TableEditor selection with colspan/rowspan", () => {
	it("enforces rectangular selection with merged cells (colspan/rowspan)", async () => {
		const user = userEvent.setup();
		// Table layout:
		// 1 1 1
		// 1 1 1
		// 2   1
		// Where '2' is a cell with colspan=2
		const tableProperties = {
			rows: 3,
			columns: 3,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				[
					{
						content: "A",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "B",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "C",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
				[
					{
						content: "D",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "E",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "F",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
				[
					{
						content: "G",
						colspan: 2,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "I",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
			],
			selection: null,
		};
		// We'll track selection changes
		let latestProps: TableProperties = { ...tableProperties };
		const handleChange = (props: TableProperties) => {
			latestProps = props;
		};
		// Render the TableEditor
		render(
			<TableEditor
				tableProperties={tableProperties}
				onChange={handleChange}
				setActiveEditor={() => {}}
			/>,
		);
		// Simulate mouse selection: select (0,0) then drag to (1,1)
		const allCells = screen.getAllByRole("cell");
		// Click first cell (A)
		await user.click(allCells[0]);
		// Shift+click cell E (row 1, col 1)
		await user.keyboard("{Shift>}");
		await user.click(allCells[4]);
		await user.keyboard("{/Shift}");
		// The selection should be a rectangle covering (0,0)-(1,1)
		expect(latestProps.selection).not.toBeNull();
		expect(latestProps.selection).toEqual({
			start: { row: 0, col: 0 },
			end: { row: 1, col: 1 },
		});
		// Now extend selection to the last row (row 2)
		// Simulate shift+arrow down from (1,1) to (2,1)
		allCells[4].focus();
		await user.keyboard("{Shift>}{ArrowDown}{/Shift}");
		expect(latestProps.selection).not.toBeNull();
		if (latestProps.selection) {
			expect(latestProps.selection.start).toEqual({ row: 0, col: 0 });
			expect(latestProps.selection.end?.row).toBe(2);
		}
		// Now select bottom right and extend up
		allCells[5].focus(); // cell F (row 1, col 2)
		await user.keyboard("{Shift>}{ArrowDown}{/Shift}"); // move to (2,2) (should be cell I)
		expect(latestProps.selection).not.toBeNull();
		if (latestProps.selection) {
			expect(latestProps.selection.end?.row).toBe(2);
			expect(latestProps.selection.end?.col).toBe(1); // changed from 2 to 1
		}
		// Now extend up
		await user.keyboard("{Shift>}{ArrowUp}{/Shift}");
		expect(latestProps.selection).not.toBeNull();
		if (latestProps.selection) {
			expect(latestProps.selection.end?.row).toBe(1);
		}
		// The selection should always be a rectangle, never skipping columns due to colspan
	});
});
