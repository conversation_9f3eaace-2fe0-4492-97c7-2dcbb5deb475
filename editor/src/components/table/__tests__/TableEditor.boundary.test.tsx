import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";
import { TableEditor } from "@/components/table/TableEditor";
import type { TableProperties } from "@/types/table";

describe("TableEditor boundary handling", () => {
	it("constrains selection within table boundaries", async () => {
		const user = userEvent.setup();
		
		// Simple 2x2 table
		const tableProperties: TableProperties = {
			rows: 2,
			columns: 2,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				[
					{
						content: "A",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "B",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
				[
					{
						content: "C",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
					{
						content: "D",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
			],
			selection: null,
		};

		let latestProps: TableProperties = { ...tableProperties };
		const handleChange = (props: TableProperties) => {
			latestProps = props;
		};

		render(
			<TableEditor
				tableProperties={tableProperties}
				onChange={handleChange}
				setActiveEditor={() => {}}
			/>,
		);

		const allCells = screen.getAllByRole("cell");
		
		// Click first cell (A) at (0,0)
		await user.click(allCells[0]);
		
		// Shift+click last cell (D) at (1,1) - this should work normally
		await user.keyboard("{Shift>}");
		await user.click(allCells[3]);
		await user.keyboard("{/Shift}");

		// Selection should cover the entire table
		expect(latestProps.selection).not.toBeNull();
		expect(latestProps.selection).toEqual({
			start: { row: 0, col: 0 },
			end: { row: 1, col: 1 },
		});

		// Try to extend beyond boundaries by simulating coordinates outside table
		// This would normally be handled by the boundary constraint logic
		// The selection should remain constrained to table bounds
		expect(latestProps.selection?.start.row).toBeGreaterThanOrEqual(0);
		expect(latestProps.selection?.start.col).toBeGreaterThanOrEqual(0);
		expect(latestProps.selection?.end.row).toBeLessThan(tableProperties.rows);
		expect(latestProps.selection?.end.col).toBeLessThan(tableProperties.columns);
	});

	it("handles invalid cell coordinates gracefully", async () => {
		const user = userEvent.setup();
		
		// Single cell table
		const tableProperties: TableProperties = {
			rows: 1,
			columns: 1,
			borderWidth: 1,
			borderStyle: "solid",
			cells: [
				[
					{
						content: "Single",
						colspan: 1,
						rowspan: 1,
						borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						backgroundColor: null,
					},
				],
			],
			selection: null,
		};

		let latestProps: TableProperties = { ...tableProperties };
		const handleChange = (props: TableProperties) => {
			latestProps = props;
		};

		render(
			<TableEditor
				tableProperties={tableProperties}
				onChange={handleChange}
				setActiveEditor={() => {}}
			/>,
		);

		const cell = screen.getByRole("cell");
		
		// Click the single cell
		await user.click(cell);

		// Selection should be constrained to the single cell
		expect(latestProps.selection).not.toBeNull();
		expect(latestProps.selection).toEqual({
			start: { row: 0, col: 0 },
			end: { row: 0, col: 0 },
		});

		// Verify bounds are within table limits
		expect(latestProps.selection?.start.row).toBe(0);
		expect(latestProps.selection?.start.col).toBe(0);
		expect(latestProps.selection?.end.row).toBe(0);
		expect(latestProps.selection?.end.col).toBe(0);
	});
});