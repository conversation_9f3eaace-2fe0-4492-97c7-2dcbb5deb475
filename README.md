# TS4SF / Directhub - General

TS4SF or DirectHUB - see Confluence for the main [documentation](https://dialogagentur.atlassian.net/wiki/spaces/TS4SF/pages/161906858/20.10+Technische+Dokumentation).

# Laravel

We are using the [<PERSON>vel framework](https://laravel.com/docs/).

## Environments

Laravel can be configured depending on different [enviroments](https://laravel.com/docs/8.x/configuration#determining-the-current-environment).

We do use:
- ```production``` - live/production/customer-facing
- ```staging``` - internally used to test, stage, QS
- ```local``` - running locally for development


# Editor2025

Notizen zur Runtime von Editor2025, unvollständig:
- Branch `feature/Editor2025` nutzen.
- DB-Dump von `directhub-dev`
- Migrations laufen lassen
    - zwei sind doppelt, als gemacht in DB schreiben
- Dateien aus Storage von ebendort. 
- einige DB-Anpassungen für Jobs. FH weiß mehr. 

## PDF creation testing

- Have a prepared job
- use FH's branch
- call HOST:PORT/get/editor/testpdf/JOB_ID, i.e. `localhost:8080/get/editor/testpdf/118`


# Docker

## Kaniko

Bauen des Containers lokal mit Kaniko:

`docker run -v ./:/workspace  gcr.io/kaniko-project/executor:debug --context ./ --dockerfile ./Dockerfile --no-push`

# Deployments

Deployments of any version can be made from Infomaniaks Jelastic Dashboard by selecting the respective Docker image tag. Currently this would be needed to select an older version.

During normal operations deployments are done by Gitlab CI/CD pipeline.
* Versions merged to develop are deployed to agency stage automatically.
* Any version can be deployed to our dev environment, started manually.
* Versions merged to master can be deployed to customer stage and prod, started manually. These deployments are protected against accidental starts: You have to set a pipeline variable `DEPLOY` with a value of `yes`.
