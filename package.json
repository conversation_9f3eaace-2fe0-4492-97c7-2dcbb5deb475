{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.19.2", "bootstrap": "^4.5.3", "cross-env": "^5.1", "laravel-mix": "^4.0.7", "lodash": "^4.17.21", "popper.js": "^1.16.1", "resolve-url-loader": "^2.3.1", "sass": "^1.37.0", "sass-loader": "7.*", "vue-template-compiler": "^2.6.14"}, "dependencies": {"bootstrap-table": "^1.18.3", "jquery": "^3.6.0", "jquery-ui": "^1.12.1", "jsdom": "^16.6.0", "pdfjs": "^2.4.5"}}