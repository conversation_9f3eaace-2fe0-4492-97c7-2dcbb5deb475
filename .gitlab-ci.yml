---
# no default image currently, we use dedicated images for every job.

variables:
  # Gitlab feature flag: use Docker network per build:
  FF_NETWORK_PER_BUILD: 1
  # FastZIP
  FF_USE_FASTZIP: 1

.template-docker-build:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  variables:
    DOCKERFILE: "${CI_PROJECT_DIR}/docker/CONFIGURE_ME/Dockerfile"
    DOCKER_DESTINATION_ARG: "--destination CONFIGURE_ME"
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_JOB_TOKEN}" | base64 | tr -d '\n')\"},\"$(echo -n $CI_DEPENDENCY_PROXY_SERVER | awk -F[:] '{print $1}')\":{\"auth\":\"$(printf "%s:%s" ${CI_DEPENDENCY_PROXY_USER} "${CI_DEPENDENCY_PROXY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - echo Starting for image ${CI_REGISTRY_IMAGE}
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile ${DOCKERFILE}
      ${DOCKER_DESTINATION_ARG}
      --cache=true
      --cache-ttl=12h
      --cache-repo="${CI_REGISTRY_IMAGE}/cache"
  tags:
    - saas-linux-small-amd64

docker-build-master-app:
  extends: .template-docker-build
  variables:
    DOCKER_DESTINATION_ARG: '--destination ${CI_REGISTRY_IMAGE}/app:latest --destination ${CI_REGISTRY_IMAGE}/app:${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}'
    DOCKERFILE: "${CI_PROJECT_DIR}/docker/app/Dockerfile"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  tags:
    - saas-linux-small-amd64

docker-build-master-app-init:
  extends: .template-docker-build
  variables:
    DOCKER_DESTINATION_ARG: '--destination ${CI_REGISTRY_IMAGE}/app-init:latest --destination ${CI_REGISTRY_IMAGE}/app-init:${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}'
    DOCKERFILE: "${CI_PROJECT_DIR}/docker/app-init/Dockerfile"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  tags:
    - saas-linux-small-amd64

docker-build-feature-app:
  extends: .template-docker-build
  variables:
    DOCKER_DESTINATION_ARG: '--destination ${CI_REGISTRY_IMAGE}/app:${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}'
    DOCKERFILE: "${CI_PROJECT_DIR}/docker/app/Dockerfile"
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "master"
  tags:
    - saas-linux-small-amd64

docker-build-feature-app-init:
  extends: .template-docker-build
  variables:
    DOCKER_DESTINATION_ARG: '--destination ${CI_REGISTRY_IMAGE}/app-init:${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}'
    DOCKERFILE: "${CI_PROJECT_DIR}/docker/app-init/Dockerfile"
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "master"
  tags:
    - saas-linux-small-amd64

.deploy-template:
  stage: deploy
  image: alpine/k8s:1.30.0
  variables:
    SECURE_FILES_DOWNLOAD_PATH: '.secure_files'
    DOCKER_TAG: 'CONFIGURE_ME'
    INSTANCE_NAME: 'INSTANCE_NAME_UNCONFIGURED'
    DB_NAME: DB_UNCONFIGURED_USE_INSTANCE_WITH_UNDERSCORE
    CLUSTER_DOMAIN: 'CLUSTER_DOMAIN_UNCONFIGURED'
    MEMORY_REQUEST: 2000M
    HTTP_HOSTNAME_EXT: 'CONFIGURE_HOSTNAME_HERE'
  script:
    - apk update
      && apk add --no-cache curl jq
      && rm -rf /var/cache/apk/*
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - ls -lah .secure_files
    - echo Going to deploy tag ${DOCKER_TAG} to k8s instance ${INSTANCE_NAME}
    # Print HTTP body, check delivered JSON for result==0 using jq.
    - export DOCKER_TAG=${DOCKER_TAG}
    - export INSTANCE_NAME=${INSTANCE_NAME}
    - export DB_NAME=${DB_NAME}
    - export HTTP_HOSTNAME_EXT=${HTTP_HOSTNAME_EXT}
    - export MEMORY_REQUEST=${MEMORY_REQUEST}
    - export DB_PASSWORD=$(cat .secure_files/db_pw_${INSTANCE_NAME})
    - envsubst <directhub-deployment.yml > directhub-deployment-vars.yml
    - kubectl --kubeconfig .secure_files/kubeconfig-deployer version
    - kubectl --kubeconfig .secure_files/kubeconfig-deployer -n ${INSTANCE_NAME} apply -f directhub-deployment-vars.yml
    - kubectl --kubeconfig .secure_files/kubeconfig-deployer -n ${INSTANCE_NAME} get deployments,pods -o wide
  tags:
    - saas-linux-small-amd64

deploy-directhub-dev:
  extends: .deploy-template
  environment:
    name: directhub-dev
    url: https://dev.directhub.de
  needs: ["docker-build-feature-app", "docker-build-feature-app-init"]
  variables:
    DOCKER_TAG: "${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
    INSTANCE_NAME: directhub-dev
    DB_NAME: directhub_dev
    HTTP_HOSTNAME_EXT: dev.directhub.de
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "master"
      when: manual
      allow_failure: true

deploy-directhub-stage:
  extends: .deploy-template
  environment:
    name: directhub-stage
    url: https://stage.directhub.de
  needs: ["docker-build-feature-app", "docker-build-feature-app-init"]
  variables:
    DOCKER_TAG: "${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
    INSTANCE_NAME: directhub-stage
    DB_NAME: directhub_stage
    HTTP_HOSTNAME_EXT: stage.directhub.de
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_BRANCH == "feature/Editor2025"
      when: manual
      allow_failure: true

deploy-ts4sf-stage-editor:
  extends: .deploy-template
  environment:
    name: ts4sf-stage-editor
    url: https://stage-editor.transfersafe4sf.de
  needs: ["docker-build-feature-app", "docker-build-feature-app-init"]
  variables:
    DOCKER_TAG: "${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
    INSTANCE_NAME: ts4sf-stage-editor
    DB_NAME: ts4sf_stage_editor
    HTTP_HOSTNAME_EXT: stage-editor.transfersafe4sf.de
  rules:
    - if: $CI_COMMIT_BRANCH == "feature/Editor2025"
      when: manual
      allow_failure: true

deploy-ts4sf-stage:
  extends: .deploy-template
  environment:
    name: ts4sf-stage
    url: https://stage.transfersafe4sf.de
  needs: ["docker-build-feature-app", "docker-build-feature-app-init"]
  variables:
    DOCKER_TAG: "${CI_COMMIT_REF_SLUG}_${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
    INSTANCE_NAME: ts4sf-stage
    DB_NAME: ts4sf_stage
    HTTP_HOSTNAME_EXT: stage.transfersafe4sf.de
  rules:
    - if: $CI_COMMIT_BRANCH == "feature/Editor2025"
      when: manual
      allow_failure: true

#deploy-ts4sf-stage:
#  extends: .deploy-template
#  environment:
#    name: ts4sf-stage
#    url: https://stage.transfersafe4sf.de
#  needs: ["docker-build-master-app", "docker-build-master-app-init"]
#  variables:
#    DOCKER_TAG: "${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
#    INSTANCE_NAME: ts4sf-stage
#    DB_NAME: ts4sf_stage
#    HTTP_HOSTNAME_EXT: stage.transfersafe4sf.de
#  before_script:
#    - if [ "$DEPLOY" != "yes" ]; then echo "You have to set \$DEPLOY to yes." ; false ; fi
#  rules:
#    - if: $CI_COMMIT_BRANCH == "master"
#      when: manual
#      allow_failure: true

deploy-prod-eprimo:
  extends: .deploy-template
  environment:
    name: prod_eprimo
    url: https://eprimo.transfersafe4sf.de
  needs: ["docker-build-master-app", "docker-build-master-app-init"]
  variables:
    DOCKER_TAG: "${CI_PIPELINE_IID}_${CI_COMMIT_SHORT_SHA}"
    INSTANCE_NAME: ts4sf-prod-epri
    DB_NAME: ts4sf_prod_epri
    HTTP_HOSTNAME_EXT: eprimo.transfersafe4sf.de
  before_script:
    - if [ "$DEPLOY" != "yes" ]; then echo "You have to set \$DEPLOY to yes." ; false ; fi
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
      allow_failure: true
