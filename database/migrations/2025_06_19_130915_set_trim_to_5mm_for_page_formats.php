<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update all existing page formats to have 5mm trim on all sides
        DB::table('page_formats')->update([
            'trim_top' => 5,
            'trim_right' => 5,
            'trim_bottom' => 5,
            'trim_left' => 5,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('page_formats')->update([
            'trim_top' => 3,
            'trim_right' => 3,
            'trim_bottom' => 3,
            'trim_left' => 3,
        ]);
    }
};
