<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('templates', function ($table) {
            // First change the column type to longText for unlimited length
            $table->longText('json')->after('html')->nullable();
            $table->dropColumn('html');

            // Then rename the column from html to json
            //$table->renameColumn('html', 'json');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('templates', function ($table) {
            // Rename back from json to html
            //$table->renameColumn('json', 'html');

            // Change back to text type
            $table->text('html')->nullable();
            $table->dropColumn('json');
        });
    }
};
