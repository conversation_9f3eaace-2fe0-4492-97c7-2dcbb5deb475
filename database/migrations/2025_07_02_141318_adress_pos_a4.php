<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update page formats for A4 portrait and landscape
        DB::table('page_formats')
            ->where('height', 297)
            ->orWhere('height', 210)
            ->update([
                'recipient_y' => 26.7,
                'sender_y' => 5,
                'blocker_y' => 6.7
            ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Note: Reversing data changes is typically not straightforward
        // You would need to store the original values if rollback is needed
        // For now, leaving this empty as it's a data migration
    }
};
