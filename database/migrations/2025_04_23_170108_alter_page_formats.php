<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('page_formats', function ($table) {
            $table->float('pagenumber_w')->after('address_h')->nullable();
            $table->float('pagenumber_y')->after('pagenumber_w')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('page_formats', function ($table) {
            $table->dropColumn('pagenumber_w');
            $table->dropColumn('pagenumber_y');
        });
    }
};
