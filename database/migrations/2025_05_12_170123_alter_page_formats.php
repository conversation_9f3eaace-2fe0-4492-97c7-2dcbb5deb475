<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('page_formats', function ($table) {
            $table->float('sender_y')->after('pagenumber_y')->nullable();
            $table->float('recipient_y')->after('sender_y')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('page_formats', function ($table) {
            $table->dropColumn('sender_y');
            $table->dropColumn('recipient_y');
        });
    }
};
