<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('imagefiles', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('jobId');
            $table->string('fileName')->index();
            $table->string('originalFileName');
            $table->string('extension');
            $table->string('path');
            $table->dateTime('updated_at');
            $table->dateTime('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('imagefiles');
    }
};
