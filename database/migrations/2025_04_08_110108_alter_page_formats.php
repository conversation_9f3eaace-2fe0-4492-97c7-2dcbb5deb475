<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('page_formats', function ($table) {
            $table->text('address_w')->after('address_y')->nullable()->default(90);
            $table->text('address_h')->after('address_w')->nullable()->default(45);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('page_formats', function ($table) {
            $table->dropColumn('address_w');
            $table->dropColumn('address_h');
        });
    }
};
