<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('page_formats', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('description')->nullable();
            $table->integer('height');
            $table->integer('width');
            $table->integer('trim_top');
            $table->integer('trim_right');
            $table->integer('trim_bottom');
            $table->integer('trim_left');
            $table->integer('address_x');
            $table->integer('address_y');
            $table->integer('active')->default(1);
            $table->dateTime('updated_at');
            $table->dateTime('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('page_formats');
    }
};
