<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('blocked_areas', function (Blueprint $table) {
            $table->id();
            $table->integer('jobId');
            $table->integer('pageFormatId');
            $table->integer('block_x');
            $table->integer('block_y');
            $table->integer('block_w');
            $table->integer('block_h');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('blocked_areas');
    }
};
