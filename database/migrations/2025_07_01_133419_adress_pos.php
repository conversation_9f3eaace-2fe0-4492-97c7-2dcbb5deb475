<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update page formats where height == 105
        DB::table('page_formats')
            ->where('height', 105)
            ->update([
                'address_x' => 157,
                'address_y' => 3,
                'recipient_y' => null,
                'address_w' => 50, // 210 - 160
                'address_h' => 95,
                'sender_y' => 62, // 65 - address_y (65 - 3)
                'blocker_y' => 87 // 90 - address_y (90 - 3)
            ]);

        // Update page formats where height == 125
        DB::table('page_formats')
            ->where('height', 125)
            ->update([
                'address_x' => 163,
                'address_y' => 3,
                'recipient_y' => 81, // 84 - address_y (84 - 3)
                'address_w' => 67, // 230 - 163
                'address_h' => 106,
                'sender_y' => 63, // 66 - address_y (66 - 3)
                'blocker_y' => 65 // 68 - address_y (68 - 3)
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Note: Reversing data changes is typically not straightforward
        // You would need to store the original values if rollback is needed
        // For now, leaving this empty as it's a data migration
    }
};
