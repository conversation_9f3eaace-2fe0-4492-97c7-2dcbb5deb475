{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-snappy": "^1.0", "bschmitt/laravel-amqp": "^2.1", "cache/redis-adapter": "^1.0", "cache/simple-cache-bridge": "^1.0", "dompdf/dompdf": "^3.1", "grofgraf/laravel-pdf-merger": "^1.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^9.0", "laravel/helpers": "^1.4", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "league/flysystem": "^3.0", "league/flysystem-sftp": "^3.0", "phpoffice/phpspreadsheet": "^1.15", "sendinblue/api-v3-sdk": "^7.4", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.3", "symfony/cache": "^5.1"}, "require-dev": {"spatie/laravel-ignition": "^1.2.2", "fzaninotto/faker": "^v1.9.2", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^6.0", "phpunit/phpunit": "^8.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}