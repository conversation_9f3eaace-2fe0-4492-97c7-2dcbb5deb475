function copytext(element) {
    //alert(element);
    var copyText = document.querySelector('#'.element);
    //copyText.innerHTML.select();
    document.execCommand("copy");
}

function loadPdf(element, data) {
    var viewer = document.getElementById(element, data);
    var parent = viewer.parentElement;
    var newViewer = document.createElement("object");
    var content = JSON.parse(data);

    //viewer.remove();
    newViewer.setAttribute("data", "data:" + content[0].mime + ";base64," + content[0].file);
    newViewer.setAttribute("type", content[0].mime);
    viewer.replaceWith(newViewer);
}

/*
function print(element) {
    var canvas = document.getElementById(element);
    var ctx = canvas.toDataURL("image/jpeg", 1.0);

    printJS({printable: ctx, type:'image', showModal:false});
}
*/

function print_id(id, page) {

    $.get('../../../filedata/' + id + '/page/' + page, {},
        function (data) {
            var content = JSON.parse(data);

            var pdfcontent = (content[0].file);
            //  const pdfBlob = new Blob([pdfcontent], { type:  content[0].mime});
            /*
              const pdfBlob = dataURItoBlob(pdfcontent);

              console.log(pdfBlob.size)
              const url = URL.createObjectURL(pdfBlob);
              window.open(url);

             */
            printJS({printable: pdfcontent, type: 'pdf', showModal: false, base64: true});
        }
    );
}


function filedata2pdf(id, element, page, zoom) {
    if (zoom <= 0) zoom = 0.1;
    $('#' + element).attr('data', '');

    $.get('../../../filedata/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            $('#' + element).attr('type', content[0].mime);

            if (content[0].mime.indexOf('image') !== -1) {
                $('#' + 'pdf_inhalt').hide();
                $('#' + 'img_inhalt').attr('src', 'data:' + content[0].mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;
                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');


                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById('pdf_inhalt');
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context
                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };
                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    if (numPages <= page) $('#pdf_next').hide();
                    else if (numPages != 1) $('#pdf_next').show();

                    $('#' + 'pdf_inhalt').show();
                }, function (reason) {
                    // PDF loading error
                    console.error(reason);
                });
            }
        }
    );
}

function detectMimeType(b64) {
    var signatures = {
        'J': "application/pdf",
        'R': "image/gif",
        'i': "image/png",
        '/': "image/jpeg"
    };
    for (var s in signatures) {
        if (b64.charAt(0) === s) {
            return signatures[s];
        }
    }
}


function previewpdf(id, element, page, zoom) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');

    $.get('previewpdf/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    //$('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}

function previewhashdoc(id, element, page, zoom) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');

    $.get('../previewhashpdf/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}

function previewdocpdf(id, element, page, zoom, path) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');
    $.get(path + '/previewdocpdf/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                //console.log(Date.getTime());
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                //console.log(Date.getTime());
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    //$('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}

function getdirdokpreview(id, element, page, zoom, path) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');
    $.get(path + 'getdirdokpreview/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    //$('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}


function previewweddingdoc(id, element, page, zoom, path, fileid, datasetnr, showvars) {
    $('#' + element).hide();
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    const start = Date.now();
    $('#' + element).attr('data', '')
    console.log(path + '/weddingpreview/' + id + '/file/' + fileid + (datasetnr ? '/' + datasetnr : '') + (showvars ? '/' + showvars : ''));
    $.get(path + '/weddingpreview/' + id + '/file/' + fileid + (datasetnr ? '/' + datasetnr : '') + (showvars ? '/' + showvars + '/1' : ''), {},
        function (data) {
            const millis = Date.now() - start;
            console.log(millis);
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}

async function previewweddingpdf(id, element, path, fileid, datasetnr, showvars) {
    //$('#' + element).attr('data', '')
    // $('#' + element).removeAttr('type');
    $('#' + element).removeAttr('src');
    $('#spinner').show();

    //console.log(path + '/weddingpreview/' + id + '/file/' + fileid + (datasetnr ? '/' +  datasetnr: '') + (showvars ? '/' +  showvars : ''));

    const data = await $.get(
        {
            url: path + '/weddingpreview/' + id + '/file/' + fileid + (datasetnr ? '/' + datasetnr : '') + (showvars ? '/' + showvars + '/1' : ''),
            type: 'GET',
            success: function (data) {

                const content = JSON.parse(data);
                const mime = detectMimeType(content[0].file);
                let b64 = 'data:application/pdf;BASE64,' + (content[0].file);
                $('#' + element).replaceWith($('#' + element).clone().attr('src', b64));
                // $('#' + element).attr('type', mime);
                $('#spinner').hide();
                //$('#' + element).attr('type', mime);
                //$('#' + element).html( b64);
                //$('#' + element).attr('data', 'data:' + mime + ';base64,' + content[0].file);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                // Empty most of the time...
            }
        });

}

function previewmakerdoc(id, element, page, zoom, path, fileid) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');
    $.get(path + '/weddingmakerpreview/' + id + '/file/' + fileid, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    $('#zoom').val(zoom.toFixed(1));
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}


function previewdruckdoc(id, element, page, zoom, path, fileid) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');
    $.get(path + '/druckdateiview/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}


function previewmakerdoclookup(id, element, page, zoom, path, fileid) {
    if (zoom <= 0) zoom = 0.1;
    //console.log("test: " + element);
    $('#' + element).attr('data', '');
    $.get(path + '/previewmakerdoclookup/' + id, {},
        function (data) {
            var content = JSON.parse(data);
            var mime = detectMimeType(content[0].file);

            $('#' + element).attr('type', mime);

            if (mime !== 'application/pdf') {
                $('#' + 'preview_' + id).hide();
                //$('#' + element).height('auto');
                //$('#' + element).width('auto');
                $('#' + 'img_inhalt').attr('src', 'data:' + mime + ';base64,' + content[0].file);
                $('#' + 'img_inhalt').show();

                $('#seitenzahlen').hide();

            } else {
                //$('#' + element).height('100%');
                $('#' + 'img_inhalt').hide();
                var pdfcontent = atob(content[0].file);
                // console.log(pdfcontent);
                var loadingTask = pdfjsLib.getDocument({data: pdfcontent});
                loadingTask.promise.then(function (pdf) {
                    var numPages = pdf.numPages;

                    // Fetch the first page
                    var pageNumber = 1;
                    if (page > 1) pageNumber = page;
                    pdf.getPage(pageNumber).then(function (page) {
                        console.log('Page loaded');
                        var scale = zoom;

                        var viewport = page.getViewport({scale: scale});
                        // Prepare canvas using PDF page dimensions
                        var canvas = document.getElementById(element);
                        var context = canvas.getContext('2d');

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        // Render PDF page into canvas context

                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        var renderTask = page.render(renderContext);
                        renderTask.promise.then(function () {
                            console.log('Page rendered');
                        });
                    });
                    $('#max_pages').html(numPages);
                    //if(numPages <= page) $('#pdf_next').hide();
                    //else if(numPages != 1) $('#pdf_next').show();

                    $('#' + element).show();
                }, function (reason) {
                    // PDF loading error
                    console.error("ERROR " + reason);
                });

            }
        }
    );
}

function filedata2adressdaten(id, element) {

}


function toggle_fav(id) {
    $.get('./togglefav/' + id, {},
        function (data) {
            //alert(data);
            location.reload();
        }
    );
}


function return_auser(id, element) {
    //alert(id);
    $.get('./returnkuser/' + id,
        function (data) {
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $.each(newElements, function (key, value) {
                $el.append("<li >" +
                    "<li >" + (value).vorname + " " + (value).name + "</li>" +
                    "<li ><span><input type='checkbox'>Freigabe</span></li>" +
                    "</ul></li>" +
                    "<div style=\"clear:both;\"></div>");
            });
        });

}

function return_bbutton_bt(inhalt) {
    console.log(inhalt);
    if (inhalt != '') {
        $('#b_hinzufuegen').addClass('bluebutton');
        $('#b_hinzufuegen').prop('disabled', false);

    } else {
        $('#b_hinzufuegen').removeClass('bluebutton');
        $('#b_hinzufuegen').prop('disabled', true);
    }

}


function return_auser_bt(id) {

    if (id != '') {
        $('#a_button').addClass('bluebutton');
        $('#a_button').attr('disabled', false);
        $('#a_button').removeAttr('disabled');

    } else {
        $('#a_button').removeClass('bluebutton');
        $('#a_button').attr('disabled', true);
    }

}


function return_kuser_bt(id) {

    if (id != '') {
        $('.ku_button').addClass('bluebutton');
        $('.ku_button').attr('disabled', false);
        $('.ku_button').removeAttr('disabled');
    } else {
        $('.ku_button').removeClass('bluebutton');
        $('.ku_button').attr('disabled', true);
    }

}


function return_duser_bt(id) {

    if (id != '') {
        $('.du_button').addClass('bluebutton');
        $('.du_button').attr('disabled', false);
        $('.du_button').removeAttr('disabled');
    } else {
        $('.du_button').removeClass('bluebutton');
        $('.du_button').attr('disabled', true);
    }

}


function gluekats(item, index) {
    var glued;
    glued += "<option value='" + index + "'>" + item + "</option>";
    return glued;
}


function add_ftpproject(id, element, key) {

    $.get('../getprojekt/' + id,
        function (data) {
            var select;
            var inhalt = JSON.parse(data);
            var bezeichnung = (inhalt).bezeichnung;
            var id_job = (inhalt).id;
            var kats = (inhalt).kats;
            var $el = $("." + element);
            $.each(kats, function (index, value) {
                select += "<option value='" + index + "'>" + value + "</option>";
            });
            $el.append("<div>" + bezeichnung + '' +
                '<select id="kat_id' + id_job + '" name="kat_id' + id_job + '[]" class="custom-select  col-12 col-md-4 m-0 ml-2">' + select +
                "" +
                "<input type='hidden' name='ids[]' value='" + id_job + "'/></div>"
            );

        });

    setTimeout(function () {
        check_job(key)
    }, 500);
}


function return_kuser(id, element) {
    //alert(id);
    $.get('../returnkuser/' + id,
        function (data) {
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $el.append("<option value=''>Mitarbeiter auswählen</option>");
            $.each(newElements, function (key, value) {
                $el.append("<option value='" + (value).id + "'>" +
                    "" + (value).vorname + " " + (value).name + "</option>");
                /*
                $el.append("<li><ul>" +
                    "<li>"+ (value).vorname + " "+  (value).name +"</li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_ansehen' type='checkbox'>Ansehen</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_upload' type='checkbox'>Upload</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_download' type='checkbox'>Download</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_freigabe'  type='checkbox'>Freigabe</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_benachrichtigung' type='checkbox'>Benachrichtigung</span></li>" +
                    "</ul></li>" +
                    "<div style=\"clear:both;\"></div>");
                 */
            });
        });

}

function return_kuser_stamm(id, element) {
    //alert(id);
    $.get('./returnkuser/' + id,
        function (data) {
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $el.append("<option value=''>Mitarbeiter auswählen</option>");
            $.each(newElements, function (key, value) {
                $el.append("<option value='" + (value).id + "'>" +
                    "" + (value).vorname + " " + (value).name + "</option>");
                /*
                $el.append("<li><ul>" +
                    "<li>"+ (value).vorname + " "+  (value).name +"</li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_ansehen' type='checkbox'>Ansehen</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_upload' type='checkbox'>Upload</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_download' type='checkbox'>Download</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_freigabe'  type='checkbox'>Freigabe</span></li>" +
                    "<li><span><input p_id='" +  (value).id + "' name='" + (value).id +"_benachrichtigung' type='checkbox'>Benachrichtigung</span></li>" +
                    "</ul></li>" +
                    "<div style=\"clear:both;\"></div>");
                 */
            });
        });

}


function return_duser(id, element) {
    $.get('./returnduser/' + id,
        function (data) {
            // alert(data);
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $el.append("<option value=''>Mitarbeiter auswählen</option>");
            $.each(newElements, function (key, value) {
                $el.append("<option value='" + (value).id + "'>" +
                    "" + (value).vorname + " " + (value).name + "</option>");
            });
        });

}


function return_duser_stamm(id, element) {
    //alert(id);
    $.get('./returnduser/' + id,
        function (data) {
            // alert(data);
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $el.append("<option value=''>Mitarbeiter auswählen</option>");
            $.each(newElements, function (key, value) {
                $el.append("<option value='" + (value).id + "'>" +
                    "" + (value).vorname + " " + (value).name + "</option>");
            });
        });

}

function return_duser_short(id, element, url, linkurl) {
//alert(id);

    $.get(url + '/' + id,
        function (data) {
            var newElements = data;
            var $el = $("#" + element);
            $el.empty();
            $.each(newElements, function (key, value) {
                $el.append("<a href='" + linkurl + "/" + (value).id + "'><li class='shortname'>" + (value).shortname + "</li></a>");
            });
        });

}

function return_user_ausgabe(id, element, id_user, name) {
    var $el = $("#" + element);
    $el.append("" +
        "<input name='" + id_user + "_anlegen' type='hidden' value='1'>" +
        "<li class='list-group p-0'>\n" +
        "                                            <ul class='d-flex flex-row  p-0'>\n" +
        "                                                <li  class='  col-2 active p-2 m-1'>" + name + "</li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Ansehen\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_ansehen'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Download\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_download'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Freigabe\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_freigabe'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "<li class='p-2'>\n" +
        "                                                    Fastlane\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_fastlane'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "<li class='p-2'>\n" +
        " Editor\n" +
        " <label class='switch'>\n" +
        " <input name='" + id_user + "_editor'\n" + " type='checkbox'>\n" +
        " <span class='slider round'></span>\n" +
        "                                                   </label>\n" +
        "                                                 </li>\n" +
        "<li class='p-2'>\n" +
        "                                                    MC Freigabe\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_sffreigabe'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Benachrichtigung\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_benachrichtigung' type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                            </ul>\n" +
        "                                        </li>" +
        "<div class='clear'></div>" +
        ""
    )
    ;


}

function return_auser_ausgabe(id, element, id_user, name) {
    var $el = $("#" + element);
    $el.append("" +
        "<input name='" + id_user + "_anlegen' type='hidden' value='1'>" +
        "<li class='list-group p-0'>\n" +
        "                                            <ul class='d-flex flex-row  p-0'>\n" +
        "                                                <li class='  col-2 active p-2 m-1'>" + name + "</li>\n" +
        "                                                <li  class='p-2'>\n" +
        "                                                    Ansehen\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_ansehen'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Freigabe\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_freigabe'\n" + "type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Editor\n" +
        "                                                <label class=\"switch\">\n" +
        "                                                         <input name='" + id_user + "_editor'\n" + " type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                   </label>\n" +
        "                                                 </li>\n" +
        "                                                <li class='p-2'>\n" +
        "                                                    Benachrichtigung\n" +
        "                                                    <label class=\"switch\">\n" +
        "                                                        <input name='" + id_user + "_benachrichtigung' type='checkbox'>\n" +
        "                                                        <span class=\"slider round\"></span>\n" +
        "                                                    </label>\n" +
        "                                                </li>\n" +
        "                                            </ul>\n" +
        "                                        </li>" +
        "<div class='clear'></div>" +
        "");


}

function check_job(key) {
    if ($('#chbx' + key).is(":checked") && ($('.file' + key).children().length >= 1)) {
        $('#idpu' + key).removeClass('button-disabled');
        $('#idpu' + key).addClass('btn-primary');
        $('#idpu' + key).attr('disabled', false);
    } else {
        $('#idpu' + key).removeClass('btn-primary');
        $('#idpu' + key).addClass('button-disabled');
        $('#idpu' + key).attr('disabled', true);
    }
}

