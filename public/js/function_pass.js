function show_pass(){

    document.getElementById('pass_ausgabe').style.display = 'block';

}


function hide_pass(){

    document.getElementById('pass_ausgabe').style.display = 'none';

}

function pass_check(val){
    //Über die DOM-Methode document.getElementById wird der Wert aus dem Eingabefeld geholt
    //und der Variablen val zugewiesen.
    // var val = document.getElementById('eingabe').value;

    //Anschließend wird über die selbe DOM-Methode ein Referenzpunkt für das Feedback erzeugt
    //und in der Variablen call gespeichert.
    var call = document.getElementById('pass_ausgabe');
    var summe = 0;
    //Ab hier beginnt die Prüfung.
    //Das Passwort ist entweder zu kurz, unsicher, sicher oder sehr sicher

    //Ist das Passwort wenigstens 8 Zeichen lang?
    if (val.length > 7)  {

        document.getElementById('achtzeichen').style.backgroundColor = '#048c27';
        summe = 5;
    }
    else{
        summe = 0;
        document.getElementById('achtzeichen').style.backgroundColor = 'transparent';
    }
    if (val.match(/\d{1,}/) ){
        document.getElementById('zahl').style.backgroundColor = '#048c27';
        summe++;
    }
    else{
        document.getElementById('zahl').style.backgroundColor = 'transparent';
        summe--;
    }

    //Wenn das Passwort nur eine Zahl oder ein Sonderzeichen enthält, ist es "sicher"?
    if ( val.match(/[A-ZÄÖÜ]{1,}/)) // || val.match(/\W/) && val.match(/[a-zA-ZäöüÄÖÜ]{1,}/))
    {
        document.getElementById('grossbuchstaben').style.backgroundColor = '#048c27';
        summe++;
    }
    else{
        document.getElementById('grossbuchstaben').style.backgroundColor = 'transparent';
        summe--;
    }

    if ( val.match(/[a-zäöü]{1,}/)) // || val.match(/\W/) && val.match(/[a-zA-ZäöüÄÖÜ]{1,}/))
    {
        document.getElementById('kleinbuchstaben').style.backgroundColor = '#048c27';
        summe++;

    }
    else{
        document.getElementById('kleinbuchstaben').style.backgroundColor = 'transparent';
        summe--;
    }

    if ( val.match(/\W/)){
        document.getElementById('sonderzeichen').style.backgroundColor = '#048c27';
        summe++;

    }
    else{
        document.getElementById('sonderzeichen').style.backgroundColor = 'transparent';
        summe--;
    }

    if(summe >= 7) {
        document.getElementById('button').style.backgroundColor = '#2f4c71';
        document.getElementById('passwort_ready').value = 1;
        document.getElementById('button').disabled = false;

    }
    else{
        document.getElementById('button').style.backgroundColor = 'lightgrey';
        document.getElementById('passwort_ready').value = 0;
        document.getElementById('button').disabled = true;
    }
    /*}
    else {
        document.getElementById('passwort_ok').innerHTML =  "<img src='./images/error.png' />";
        document.getElementById('zahl').style.backgroundColor = 'grey';
        document.getElementById('grossbuchstaben').style.backgroundColor = 'grey';
        document.getElementById('kleinbuchstaben').style.backgroundColor = 'grey';
        document.getElementById('sonderzeichen').style.backgroundColor = 'grey';
        document.getElementById('achtzeichen').style.backgroundColor = 'grey';
        document.getElementById('passwort').style.backgroundColor = 'grey';
    }*/
}