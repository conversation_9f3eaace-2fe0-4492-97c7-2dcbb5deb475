APP_NAME=ts4sf_CONFIGURE_ME_IN_DOTENV
APP_ENV=production
APP_KEY=
APP_DEBUG=false
# APP_URL is set via env, written by Ansible

LOG_LEVEL=debug
# info for production
LOG_PATH=./storage/logs
# /var/log/ts4sf/ for production

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120
QUEUE_DRIVER=sync

LOG_CHANNEL=stack

CUSTOMER_ID=CONFIGURE_ME

HASH="CONFIGURE_ME"
filekey="CONFIGURE_ME"
mandant=CONFIGURE_ME

send_delay=1
error_delay=60
error_archive_time=1440
error_archivetransmit_time=2880

max_sw_preview=100

SALT=CONFIGURE_ME

# Please adapt if it is not 'Marketing Cloud'
CUSTOMER_CRM="Marketing Cloud"
CUSTOMER_CRM_ARTIKEL="die"
CUSTOMER_CRM_AUS="der"

monitoringSecret=CONFIGURE_ME

ALLOWED_ORIGINS="*"

esp_user=test
esp_pw=test
esp_role=transfersafe

esp_token_url=https://quaintix.go.dyndns.org/espemu/token/

esp_transmit_limit=50000
esp_transmit_response_limit=500
esp_transmit_api=https://quaintix.go.dyndns.org/espemu/document-api/1.0

ESP_RETRY=28

SUPPORT_MAIL=<EMAIL>

SENDINBLUE_FROM_EMAIL=<EMAIL>
SENDINBLUE_FROM_NAME="TS4SF Status"
SENDINBLUE_API_KEY="CONFIGURE_SENDINBLUE_KEY_HERE"

SET_MAX_UPLOAD_EINDRUCK=26

EMAIL_PROJEKTANLAGE_TGD=3
EMAIL_2TE_FREIGABE_TGD=5
EMAIL_FREIGABENABSCHLUSS_TGD=6
EMAIL_PROJEKTFREIGABE_TGD=7
EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE=8
EMAIL_2TE_FREIGABE_ERFORDERLICH_KUNDE_INFOTGD=9
EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE=10
EMAIL_2TE_FREIGABE_ERFOLGT_KUNDE_INFOTGD=11
EMAIL_MC_FREIGABE_ERFOLGT=12
EMAIL_FREIGABE_ABGELEHNT=13
EMAIL_PWL_UPLOAD=14
EMAIL_CRM_BELADUNG=15
EMAIL_DATENUEBERGABE_AN_DRUCKER=30
EMAIL_DATENUEBERGABE_AN_DRUCK_INFOKUNDE=16
EMAIL_DATENUEBERGABE_VON_DRUCKER_TGD=17
EMAIL_RESPONSE1_KUNDE=18
EMAIL_RESPONSE1_TGD=19
EMAIL_RESPONSE2_KUNDE=20
EMAIL_RESPONSE2_TGD=21
EMAIL_RESPONSE3_KUNDE=31
EMAIL_RESPONSE3_TGD=32
EMAIL_LOESCHBESTAETIGUNG=24
EMAIL_BENUTZER_ANLAGE=25
EMAIL_PW_AENDERUNG_OK=26
EMAIL_PW_RESET=27
EMAIL_SYSTEMMELDUNG_TGD=33
EMAIL_DRUCKFTP_FEHLER_TGD=34
EMAIL_SFTP_DOPPELTER_DATEINAME_DDL=35
EMAIL_SFTP_DOPPELTER_DATEINAME_TGD=36
EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_DDL=37
EMAIL_SFTP_FEHLERHAFTE_ZUORDNUNG_TGD=38
TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_DDL=39
TS4SF_EMAIL_SFTP_FALSCHER_DATEITYP_TGD=40
TS4SF_EMAIL_AENDERUNG_AUSLIEFERUNGSDATUM=49

STANDARD_DL=56

# configure FTP server to get print status XML files from. For the format see example below:
PRINT_STATUS_FTP={"host":"transfersafe.ftpdatenserver.de","port":22,"user":"p8151784-ts2","pass":"PW_HERE","root":"\/TS4SF\/PrintStatus","timeout":10,"auth_file":""}
PRINT_STATUS_DIR="print-status/od-media"

printWarningHours=60
printAlertHours=48
printCheckHours=24
palMaxTime=18:00:00
