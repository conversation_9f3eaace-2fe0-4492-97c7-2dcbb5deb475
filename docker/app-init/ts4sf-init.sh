#!/bin/bash

echo "ts4sf-init.sh running"

echo "Checking crontab"
if [ ! -f "/etc/ts4sf-config/ts4sf-crontab" ]; then
    echo "Copying ts4sf-crontab to ts4sf-config"
    cp -v /ts4sf-crontab /etc/ts4sf-config/
fi

echo "Checking log file"
if [ ! -f "/var/log/ts4sf/laravel.log" ]; then
    echo "Initializing /var/log/ts4sf/"
    mkdir -p /var/log/ts4sf/ && touch -a /var/log/ts4sf/laravel.log && chown www-data /var/log/ts4sf/ /var/log/ts4sf/laravel.log
fi

echo "prepare keys directory"
mkdir -p /etc/ts4sf-config/keys && chown www-data:root /etc/ts4sf-config/keys && chmod o-rwx /etc/ts4sf-config/keys

echo "Checking .env"
if [ ! -f "/etc/ts4sf-config/.env" ]; then
    echo "Copying .env.prod-empty to system."
    cp -v /.env.prod-empty /etc/ts4sf-config/.env
fi

echo "Preparing storage/app"
mkdir -p /srv/ts4sf/storage/app/public && chown www-data:root /srv/ts4sf/storage/app /srv/ts4sf/storage/app/public && chmod o-rwx /srv/ts4sf/storage/app

echo "ts4sf-init.sh done"
