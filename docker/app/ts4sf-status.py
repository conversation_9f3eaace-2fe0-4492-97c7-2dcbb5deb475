#!/usr/bin/env python3

import requests, json
import os
#from sys import stderr,exit
import sys
import logging


### vars

#url="https://directhub-dev.jcloud.ik-server.com/status/get"
url="http://localhost/status/get"
secret=os.getenv("monitoringSecret")


### functions

#
# send_monitoring_data
# args:
#     status: 0 for success, 1 or other for error
#     text: text to append in any case
#
def send_monitoring_data(status, text):
    token = os.getenv("MONITORING_TOKEN_APPDATA")

    if not token:
        logger.error("Cloudradar token missing.")
        return(1)

    if status==0:
        logger.debug("status given=0, text=", text)
        status_to_send=1
    else:
        logger.error(text)
        status_to_send=0

    if len(text)>=500:
        logger.warning("Text to send is longer than 500 characters")
        logger.warning("Text: " + text)
        text=text[0:490] + "[..]"
        logger.warning("Text: " + text)
        #text=text_to_send

    url = "https://hub.cloudradar.io/cct/"
    payload = {
    "appstatus.success": status_to_send,
    "appstatus.text": text,
    #"appstatus.any_number": 12345,
    #"appstatus.any_float": 0.1245,
    #"appstatus.alert": "This text triggers an alert. Optional.",
    #"appstatus.warning": "This text triggers a warning. Optional"
    }
    headers = { 'X-CustomCheck-Token': token }

    response = requests.request("POST", url, json=payload, headers=headers)
    if response.status_code != 204:
        logger.error("Sending data failed. " + response.text + "\n" )
        return(response.status_code)

    return 0


### main()

## init logger
#logging.basicConfig(level=logging.DEBUG)
#logging.basicConfig(level=logging.INFO)
logging.basicConfig(level=logging.WARN)
logger = logging.getLogger(__name__)


if not secret:
    send_monitoring_data(1, "Secret is missing.")
    sys.exit(1)

# get status json
try:
    logger.info("Going to get URL " + url + '/SECRET')
    json_raw = requests.get(url + '/' + secret)
    if "encoding" in json_raw:
        logger.debug("Encoding of " + url + " is " + json_raw.encoding)
    json_raw.raise_for_status()

except requests.exceptions.HTTPError as errh:
    send_monitoring_data(1, "Http Error:",errh)
    sys.exit(1)
except requests.exceptions.ConnectionError as errc:
    send_monitoring_data(1, "Error Connecting:",errc)
    sys.exit(1)
except requests.exceptions.Timeout as errt:
    send_monitoring_data(1, "Timeout Error:",errt)
    sys.exit(1)
except requests.exceptions.RequestException as err:
    send_monitoring_data(1, "OOps: Something Else",err)
    sys.exit(1)

json_text = json_raw.text

json_obj = json.loads(json_text)

if type(json_obj) is not dict:
    send_monitoring_data(1, "Did not get a JSON dict")
    sys.exit(1)

logger.debug("JSON dump: " + json.dumps(json_obj))

global_error = 0
top_output = "Results from " + url + "\n"
bottom_output = ""

global_result = json_obj["globalResult"]

if not global_result:
    global_error = 1
    top_output = top_output + "ERROR condition(s) found. See details below:\n"

for monitor in json_obj["detailedResult"]:

    logger.debug("Reading object for monitor " + monitor)

    # check for error condition
    if not "result" in json_obj["detailedResult"][monitor]:
        bottom_output = "ERROR: result is missing for " + monitor + "\n"
        global_error = 1;
        send_monitoring_data(1, "ERROR: result missing for monitor " + monitor + "\n")
        sys.exit(1)

    if not json_obj["detailedResult"][monitor]["result"]:
        detail_string = ""
        if not json_obj["detailedResult"][monitor].get("details"):
            detail_string = detail_string + "ERROR in status JSON: Field 'details' is missing inside monitor "+ monitor +".\n"
        else:
            for detail in json_obj["detailedResult"][monitor]["details"]:
                detail_string = detail_string + detail + "  "

        bottom_output = bottom_output + "ERROR for " + monitor + ": " + detail_string + "\n"
        global_error = 1;
    else:
        top_output = top_output + "OK for " + monitor + "\n"

send_monitoring_data(global_error, top_output + bottom_output)

if global_error:
    sys.exit(1)

sys.exit(0)
