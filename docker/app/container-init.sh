#!/bin/bash

# write env to file to be read by cron started scripts.


env | grep -E "^(BACKUP_|ZABBIX_).*" >/usr/local/bin/set-env.sh

#env | grep -E "^(MONITORING_|monitoringSecret)" >>/usr/local/bin/set-env.sh


cd /var/www/html

if grep -qE "^APP_KEY=$" .env ; then
    echo "Running artisan config:clear and key:generate:"
    php artisan config:clear && php artisan key:generate
    # FIXME: --force needed?

    chown -R www-data /var/www/html
fi

# only activate after https://dialogagentur.atlassian.net/browse/TS4SFDEV-411 has been resolved.
#cd /var/www/html/ ; php artisan config:cache
cd /var/www/html ; php artisan optimize ; php artisan view:cache
