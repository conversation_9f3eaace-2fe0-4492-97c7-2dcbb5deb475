#!/bin/bash

logger Configuring zabbix url ZABBIX_LOCAL_HOSTNAME local hostname $ZABBIX_LOCAL_HOSTNAME

sed -i.bak "s;^Hostname.*=.*;Hostname=$ZABBIX_LOCAL_HOSTNAME;" /etc/zabbix/zabbix_agent2.d/zabbix-local.conf

# temporary workaround for a bug https://support.zabbix.com/browse/ZBXNEXT-8273
# Zabbix sender cannot use relative path in zabbix_agent2.conf file
sed -i.bak "s;^Include=./zabbix_agent2.d/plugins.d/\*.conf;Include=/etc/zabbix/zabbix_agent2.d/plugins.d/*.conf;" /etc/zabbix/zabbix_agent2.conf

# Have directory for Zabbix PID file
mkdir -p /var/run/zabbix ; chown zabbix /var/run/zabbix/
