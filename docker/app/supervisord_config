
[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0


[program:apache2]
#command=/bin/bash -c "source /etc/apache2/envvars && exec /usr/sbin/apache2 -DFOREGROUND"
command=/usr/local/bin/apache2-foreground
priority=10

killasgroup=false
stopasgroup=false

autorestart=true

redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
stopsignal=SIGWINCH


[program:cron]
command=/usr/sbin/cron -f
user=root
priority=50

autorestart=true

redirect_stderr=true
redirect_stdout=true


[program:container-init]
command=/usr/local/bin/container-init.sh
exitcodes=0
autorestart=false
startretries=1
priority=40

redirect_stderr=true
redirect_stdout=true


[program:zabbix-prepare]
command=/usr/local/bin/zabbix-prepare
exitcodes=0
autorestart=false
startretries=1
priority=100

redirect_stderr=true
redirect_stdout=true


[program:zabbix-agent]
command=/usr/sbin/zabbix_agent2 -c /etc/zabbix/zabbix_agent2.conf
user=zabbix
priority=101

autorestart=true

redirect_stderr=true
redirect_stdout=true
