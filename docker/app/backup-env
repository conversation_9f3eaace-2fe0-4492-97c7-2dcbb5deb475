#!/bin/bash

# source ENV vars
#DONE via cron ENV now
#. /usr/local/bin/set-env.sh

set -e

env_name=$BACKUP_ENV_NAME
jelastic_session=$BACKUP_ACCESS_TOKEN
node_id=$BACKUP_NODE_ID

bak_dir_base=/bak01/env/

date_now=$(date +%Y%m%d_%H%M)


logger Going to dump environment
echo Going to dump environment


if [ -z $jelastic_session ]; then
	echo Backup access token not set.
	exit 1
fi
if [ -z $env_name ]; then
	echo Backup env not set.
	exit 1
fi
if [ -z $node_id ]; then
	echo Node id not set.
	exit 1
fi

if [ ! -d $bak_dir_base ]; then
	echo Backup target missing.
	exit 1
fi

if [ ! -d "$bak_dir_base/$env_name" ]; then
	mkdir ${bak_dir_base}/${env_name}
fi

curl --fail "https://app.jpe.infomaniak.com/1.0/environment/control/rest/getcontainerenvvars?envName=$env_name&session=$jelastic_session&nodeId=$BACKUP_NODE_ID" -X POST >/${bak_dir_base}/${env_name}/${env_name}_app_${node_id}_${date_now}.json

echo "Deleting old files: "
find ${bak_dir_base}/${env_name}/ -maxdepth 1 -name "${env_name}_app_${node_id}_????????_????.json" -type f -mtime +90 -ls -delete
