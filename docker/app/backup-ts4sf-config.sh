#!/bin/bash

# source ENV vars

set -e

env_name=$BACKUP_ENV_NAME

bak_dir_tmp=/root/

bak_target_server=directhub_backup@********
bak_target_dir=/srv/backup/ts4sf-config

ssh_identity_file=/etc/ts4sf-config/keys/id_backup

date_now=$(date +%Y%m%d_%H%M)


logger Going to backup config files
echo Going to backup config files


# FIXME: check via ssh?
#if [ ! -d $bak_dir_base ]; then
#	echo Backup target missing.
#	exit 1
#fi
if [ -z $env_name ]; then
	echo env_name not set, exiting
	exit
fi

if [ ! -f $ssh_identity_file ]; then
	echo Identity file $ssh_identity_file is missing.
	exit
fi

ssh -i $ssh_identity_file -o StrictHostKeyChecking=no $bak_target_server "bash -c 'if [ ! -d $bak_target_dir/$env_name ]; then mkdir -p $bak_target_dir/$env_name ; fi ' "


file_name="${env_name}-app_ts4sf-config_${date_now}.tar.gz"
tar -cvvzf "${bak_dir_tmp}/$file_name" /etc/ts4sf-config/


echo "Copying file $file_name to $bak_dir_target"
scp -i $ssh_identity_file -o StrictHostKeyChecking=no "$bak_dir_tmp/$file_name" "$bak_target_server:$bak_target_dir/$env_name/"

echo "Deleting old files: "
ssh -i $ssh_identity_file -o StrictHostKeyChecking=no $bak_target_server "bash -c ' find ${bak_target_dir}/${env_name} -maxdepth 1 -name \"${env_name}-app_ts4sf-config_????????_????.tar.gz\" -type f -mtime +14 -ls -delete' "
