### Option: Server
#   List of comma delimited IP addresses, optionally in CIDR notation, or DNS names of Zabbix servers and Zabbix proxies.
#   Incoming connections will be accepted only from the hosts listed here.
#   If IPv6 support is enabled then '127.0.0.1', '::127.0.0.1', '::ffff:127.0.0.1' are treated equally
#   and '::/0' will allow any IPv4 or IPv6 address.
#   '0.0.0.0/0' can be used to allow any IPv4 address.
#   Example: Server=127.0.0.1,***********/24,::1,2001:db8::/32,zabbix.example.com
#
# Mandatory: yes, if StartAgents is not explicitly set to 0
# Default:
# Server=

Server=zabbix.somesite.de
ServerActive=zabbix.somesite.de

#HostnameItem=system.hostname
Hostname=ts4sf-unconfigured-hostname
