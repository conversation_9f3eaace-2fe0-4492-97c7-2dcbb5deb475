
MAILTO=<EMAIL>

SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

BASH_ENV=/usr/local/bin/set-env.sh


# Example of job definition:
# .---------------- minute (0 - 59)
# |  .------------- hour (0 - 23)
# |  |  .---------- day of month (1 - 31)
# |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
# |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
# |  |  |  |  |
# *  *  *  *  * user-name command to be executed
#0 0-23/6 * * *  root    . /usr/local/bin/set-env.sh ; export BACKUP_ENV_NAME BACKUP_ACCESS_TOKEN BACKUP_NODE_ID ZABBIX_LOCAL_HOSTNAME ; /usr/local/bin/backup-env ; returncode=$? ; zabbix_sender -c /etc/zabbix/zabbix_agent2.conf -s $ZABBIX_LOCAL_HOSTNAME -k backup-env.exit -o $returncode
#0 0-23/6 * * *  root    . /usr/local/bin/set-env.sh ; export BACKUP_ENV_NAME ZABBIX_LOCAL_HOSTNAME ; /usr/local/bin/backup-ts4sf-config.sh ; returncode=$? ; zabbix_sender -c /etc/zabbix/zabbix_agent2.conf -s $ZABBIX_LOCAL_HOSTNAME -k backup-ts4sf-config.exit -o $returncode

0 0-23/6 * * *  root    /usr/local/bin/backup-ts4sf-config.sh ; returncode=$? ; zabbix_sender -c /etc/zabbix/zabbix_agent2.conf -s $ZABBIX_LOCAL_HOSTNAME -k backup-ts4sf-config.exit -o $returncode

#*/3 * * * *   root      . /usr/local/bin/set-env.sh ; export monitoringSecret MONITORING_TOKEN_APPDATA ; /usr/local/bin/ts4sf-status.py

#* * * * *   root    ( env ; date ) > /tmp/cron-test-env
