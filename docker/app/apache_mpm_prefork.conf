# prefork MPM
# StartServers: number of server processes to start
# MinSpareServers: minimum number of server processes which are kept spare
# MaxSpareServers: maximum number of server processes which are kept spare
# MaxRequestWorkers: maximum number of server processes allowed to start
# MaxConnectionsPerChild: maximum number of requests a server process serves

<IfModule mpm_prefork_module>
	StartServers			 10
	MinSpareServers		  10
	MaxSpareServers		 45
	MaxRequestWorkers	  150
	MaxConnectionsPerChild   2500
</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
