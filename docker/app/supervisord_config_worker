
[program:schedule]
user = www-data

#autostart = false

command=/usr/local/bin/php /var/www/html/artisan schedule:work -n

priority=50

killasgroup=true
stopasgroup=true

autorestart=true

redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM


### worker for our Laravel queues

[program:worker_GetPalDates]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=GetPalDates --name=GetPalDates_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM


[program:worker_checkContent]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=checkContent --name=checkContent_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_processPrintdata]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processPrintdata --name=processPrintdata_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_getResponseFromDl]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=getResponseFromDl --name=getResponseFromDl_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_processArchive]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processArchive --name=processArchive_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_deliverPrintData]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=deliverPrintData --name=deliverPrintData_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_deliverPWL]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=deliverPWL --name=deliverPWL_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_processPrintStatus]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processPrintStatus --name=processPrintStatus_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

######################################################
### ESP transfer - run only within defined limits! ###
###
### This job stays on autostart=false and will be started by a cron job on the defined time.
###
### FIXME: get max-jobs from ENV
### max-time: 1:15 to 5:00 is 3.75 hours = 13500 seconds

[program:worker_processTransmit]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processTransmit --name=processTransmit_worker --max-jobs=10000 --max-time=13500
user = www-data
priority=50
killasgroup=true
stopasgroup=true

### do not change this, see above.
autostart = false
autorestart=false

redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM


[program:worker_processTransmitResponses]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processTransmitResponses --name=processTransmitResponses_worker --max-jobs=10000 --max-time=13500
user = www-data
priority=50
killasgroup=true
stopasgroup=true

### do not change this, see above.
autostart = false
autorestart=false

redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_checkDlFtp]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=checkDlFtp --name=checkDlFtp_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_processDelete]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processDelete --name=processDelete_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM

[program:worker_processDeleteMail]
command=/usr/local/bin/php /var/www/html/artisan queue:work -v --queue=processDeleteMail --name=processDeleteMail_worker
user = www-data
priority=50
killasgroup=true
stopasgroup=true
autorestart=true
redirect_stderr=true
redirect_stdout=true

stopwaitsecs=300
#stopsignal=TERM
