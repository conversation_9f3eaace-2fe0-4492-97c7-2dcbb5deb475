# Build React app within build container
FROM node:24 AS node-build
WORKDIR /usr/src/app
COPY editor/ ./
RUN echo npm version: ; npm --version ; npm install && npm run build
# Make sure tests for react app pass
RUN npm test

# Set the base image for subsequent instructions
FROM registry.gitlab.com/dialogagentur/docker/php-base:8.2

RUN sed -i 's/<policy domain="coder" rights="none" pattern="PDF"/<policy domain="coder" rights="read|write" pattern="PDF"/' /etc/ImageMagick-6/policy.xml


# With current supervisord config this ends up in supervisord logs. Use Apache logs then, so
# remove symlinks and let Apache create files:
RUN rm /var/log/apache2/access.log  /var/log/apache2/error.log  /var/log/apache2/other_vhosts_access.log

# have more Apache MPM workers
COPY docker/app/apache_mpm_prefork.conf /etc/apache2/mods-enabled/mpm_prefork.conf


WORKDIR /var/www/html

# Install PHP composer dependencies
COPY composer.json composer.lock ./
RUN composer install --prefer-dist --no-scripts --no-dev --no-autoloader && rm -rf /root/.composer


COPY docker/app/supervisord_config /etc/supervisor/conf.d/supervisord.conf
COPY docker/app/supervisord_config_worker /etc/supervisor/conf.d/worker.conf

# Have some bash aliases for daily work
COPY docker/app/bashrc-additions /root/
RUN cat /root/bashrc-additions >>/root/.bashrc && rm /root/bashrc-additions


RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"



## document_root
COPY docker/app/apache_vhost_ts4sf.conf /etc/apache2/sites-available/ts4sf.conf
RUN a2dissite 000-default.conf && a2ensite ts4sf

COPY docker/app/apache_conf_ts4sf.conf /etc/apache2/conf-available/ts4sf-local.conf
RUN a2enconf ts4sf-local


ENV APACHE_DOCUMENT_ROOT /var/www/html/public
# setting both should be fine if only one folder is being used.
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf


COPY docker/app/php_ts4sf-local.ini /usr/local/etc/php/conf.d/ts4sf-local.ini
ENV PHP_MEMORY_LIMIT 2G
ENV PHP_UPLOAD_LIMIT 150M
ENV PHP_MAX_EXECUTION_TIME 30m

COPY docker/app/.htaccess /var/www/html/public/
RUN a2enmod rewrite ssl
RUN make-ssl-cert generate-default-snakeoil

RUN mkdir /tmp/upload_dir && chown www-data /tmp/upload_dir/ && chmod o-rwx /tmp/upload_dir/ && ls -al /tmp/

# Backup
COPY --chmod=755 docker/app/backup-env /usr/local/bin/
COPY --chmod=755 docker/app/backup-ts4sf-config.sh /usr/local/bin/
COPY --chmod=644 docker/app/crontab-directhub-sysjobs-local /etc/cron.d/directhub-sysjobs-local
RUN echo FIXME - should work with COPY ; chmod 644 /etc/cron.d/directhub-sysjobs-local
COPY  --chmod=755 --chown=root:root docker/app/container-init.sh /usr/local/bin/container-init.sh

# Monitoring
COPY --chmod=755 docker/app/ts4sf-status.py /usr/local/bin/ts4sf-status.py

# Have a volume for persistent configuration.RUN mkdir /etc/ts4sf-config
# Have a folder for SSH keys
# Do this from init container, too:
#RUN mkdir -p /etc/ts4sf-config/keys && chown www-data.root /etc/ts4sf-config/keys && chmod o-rwx /etc/ts4sf-config/keys && ln -s /etc/ts4sf-config/keys /var/www/html/keys
RUN ln -s /etc/ts4sf-config/keys /var/www/html/keys
# Have a crontab persisting re-deployments of Docker container:

    # init container will bring this -
#COPY --chown=root:root docker/ts4sf-crontab /etc/ts4sf-config/ts4sf-crontab

RUN ln -s /etc/ts4sf-config/ts4sf-crontab /etc/cron.d/ts4sf-local
VOLUME [ "/etc/ts4sf-config/" ]

# Have persistent logs
# do this from the init container:
#RUN mkdir /var/log/ts4sf/ && touch -a /var/log/ts4sf/laravel.log && chown www-data /var/log/ts4sf/ /var/log/ts4sf/laravel.log && mkdir -p /var/www/html/storage/logs
VOLUME [ "/var/log/ts4sf" ]
VOLUME [ "/var/log/supervisor" ]

# Have persistent storage for writing assets, for now: with subfolder for XML files from monitoring

# moved to init container - or move to app?
# RUN mkdir -p /srv/ts4sf/storage/print-status/od-media/ && chown -R www-data /srv/ts4sf/storage/print-status/od-media
VOLUME [ "/srv/ts4sf/storage/" ]

# logrotate config
COPY --chmod=640 docker/app/logrotate_ts4sf-local /etc/logrotate.d/ts4sf-local

EXPOSE 80 443

HEALTHCHECK --interval=15s --retries=3 --start-period=10s --timeout=30s CMD curl -f http://localhost/login  || exit 1

# Application
COPY ./ /var/www/html
RUN rm -r docker

# Copy React editor app from build container
COPY --from=node-build /usr/src/app/dist /var/www/html/editor/dist

# We can write and change within /var/www/html here, but not /srv/ts4sf/* - the latter is a persistent _runtime_ volume.
RUN mkdir -p /var/www/html/storage/logs && ln -sf /var/log/ts4sf/laravel.log /var/www/html/storage/logs/laravel.log \
  && ls -al /var/www/html/storage/logs && ln -s /srv/ts4sf/storage/print-status/ /var/www/html/storage/print-status \
  && mv /var/www/html/storage/app /var/www/html/storage/app_git && ln -s /srv/ts4sf/storage/app /var/www/html/storage/app

# Finish composer
RUN composer dump-autoload --no-dev --optimize

# moved to init container
#RUN cp .env.example .env ; php artisan config:clear ; php artisan key:generate --force
RUN ln -s /etc/ts4sf-config/.env /var/www/html/.env

# FIXME: would need to be included in every COPY and file generating RUN above to go away ..
RUN chown -R 33 .

# we need SIGTERM currently for supervisord
STOPSIGNAL SIGTERM

CMD [ "supervisord", "-n", "-c", "/etc/supervisor/supervisord.conf" ]
ENTRYPOINT [ "dumb-init", "--single-child", "--verbose", "--" ]
#"/usr/local/bin/docker-entrypoint" ]
