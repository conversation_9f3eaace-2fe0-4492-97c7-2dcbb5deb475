<VirtualHost *:80>

    <Directory ${APACHE_DOCUMENT_ROOT}>
        AllowOverride all
    </Directory>

	ServerA<PERSON><PERSON> webmaster@localhost
	DocumentRoot /var/www/html

	# Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
	# error, crit, alert, emerg.
	# It is also possible to configure the loglevel for particular
	# modules, e.g.
	#LogLevel info ssl:warn

	ErrorLog ${APACHE_LOG_DIR}/error.log
	CustomLog ${APACHE_LOG_DIR}/access.log combined_plus_time

</VirtualHost>

<VirtualHost *:443>

    <Directory ${APACHE_DOCUMENT_ROOT}>
        AllowOverride all
    </Directory>

	ServerAd<PERSON> webmaster@localhost
	DocumentRoot /var/www/html

	# Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
	# error, crit, alert, emerg.
	# It is also possible to configure the loglevel for particular
	# modules, e.g.
	#LogLevel info ssl:warn

	<PERSON><PERSON>rLog ${APACHE_LOG_DIR}/error.log
	CustomLog ${APACHE_LOG_DIR}/access.log combined_plus_time

	SSLEngine on

	SSLCertificateFile		/etc/ssl/certs/ssl-cert-snakeoil.pem
	SSLCertificateKeyFile   /etc/ssl/private/ssl-cert-snakeoil.key

</VirtualHost>



# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
