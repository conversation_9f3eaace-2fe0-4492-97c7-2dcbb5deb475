
= RabbitMQ goes K8s

Notes about installation, configuration and using RabbitMQ within our K8s environment. 

Wir nutzen den Operator: 
  * https://www.rabbitmq.com/kubernetes/operator/install-operator
  * https://github.com/rabbitmq/cluster-operator


Veery nice is the kubectl krew plugin: 
  * https://www.rabbitmq.com/kubernetes/operator/kubectl-plugin

= Installation via cluster operator and kubectl plugin

```
kubectl rabbitmq install-cluster-operator
kubectl create namespace rabbitmq-editor2025
kubectl apply -f rabbitmq-editor2025.yml 
```
