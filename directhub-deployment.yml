---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "${INSTANCE_NAME}-app"
  labels:
    app: directhub
    instance: "${INSTANCE_NAME}"
    service: app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: directhub
      instance: "${INSTANCE_NAME}"
      service: app
  strategy:
    # Use rolling strategy again once we have ReadWriteMany PVs
    type: Recreate
  template:
    metadata:
      labels:
        app: directhub
        instance: "${INSTANCE_NAME}"
        service: app
    spec:
      terminationGracePeriodSeconds: 300
      topologySpreadConstraints:
          - maxSkew: 1
            topologyKey: kubernetes.io/hostname
            whenUnsatisfiable: ScheduleAnyway
            labelSelector:
              matchLabels:
                app: directhub
                instance: "${INSTANCE_NAME}"
            # consider each deployment seperately:
            matchLabelKeys:
              - pod-template-hash
      initContainers:
        - name: directhub-app-init
          image: registry.gitlab.com/dialogagentur/ts4sf/app-init:${DOCKER_TAG}
          volumeMounts:
            - name: directhub-app-config
              mountPath: /etc/ts4sf-config
            - name: directhub-app-varlog
              mountPath: /var/log/ts4sf
              subPath: ts4sf
            - name: directhub-app-storage
              mountPath: /srv/ts4sf/storage
      containers:
        - name: directhub-app
          image: registry.gitlab.com/dialogagentur/ts4sf/app:${DOCKER_TAG}
          ports:
            - name: web
              containerPort: 80
            - name: websecure
              containerPort: 443
          volumeMounts:
            - name: directhub-app-varlog
              mountPath: /var/log/supervisor
              subPath: supervisor
            - name: directhub-app-varlog
              mountPath: /var/log/ts4sf
              subPath: ts4sf
            - name: directhub-app-config
              mountPath: /etc/ts4sf-config
            - name: directhub-app-storage
              mountPath: /srv/ts4sf/storage
          env:
            #- name: APP_URL
            #  value: "${HTTP_HOSTNAME_EXT}"
            - name: DB_HOST
              value: "${INSTANCE_NAME}-db"
            - name: DB_DATABASE
              value: "${DB_NAME}"
            - name: DB_PASSWORD
              value: "${DB_PASSWORD}"
            - name: DB_USERNAME
              value: "${DB_NAME}"
            - name: PHP_MEMORY_LIMIT
              value: 15G
            - name: SESSION_LIFETIME
              value: "120"
            - name: PHP_MAX_EXECUTION_TIME
              value: "7200"
            - name: BACKUP_ENV_NAME
              value: "${INSTANCE_NAME}"
            - name: ZABBIX_LOCAL_HOSTNAME
              value: "${INSTANCE_NAME}-app"
            - name: INSTANCE_NAME
              value: "${INSTANCE_NAME}"
            - name: LOG_PATH
              value: "/var/log/ts4sf/"
          resources:
            requests:
              memory: "${MEMORY_REQUEST}"
            limits:
              memory: "16000M"
      volumes:
      - name: directhub-app-config
        persistentVolumeClaim:
          claimName: "${INSTANCE_NAME}-app-config"
      - name: directhub-app-storage
        persistentVolumeClaim:
          claimName: "${INSTANCE_NAME}-app-storage"
      - name: directhub-app-varlog
        persistentVolumeClaim:
          claimName: "${INSTANCE_NAME}-app-varlog"
      imagePullSecrets:
      - name: registry-credentials
